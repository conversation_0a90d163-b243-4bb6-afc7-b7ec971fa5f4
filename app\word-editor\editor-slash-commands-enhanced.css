/* Advanced CSS for Slash Commands with modern glass-morphism design */

/* Enhanced Slash Command Menu with premium styling */
#slash-command {
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15), 0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  max-width: 450px;
  width: 100%;
  background-color: hsl(var(--background) / 0.95);
  backdrop-filter: blur(24px) saturate(180%);
  border: 1px solid hsl(var(--border) / 0.6);
  animation: commandFadeIn 0.4s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  z-index: 1000;
  position: relative;
}

.dark #slash-command {
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4), 0 4px 16px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  background-color: hsl(var(--card) / 0.95);
  border: 1px solid hsl(var(--border) / 0.8);
}

@keyframes commandFadeIn {
  from {
    opacity: 0;
    transform: translateY(16px) scale(0.95);
    filter: blur(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

#slash-command .command-list {
  padding: 0.75rem;
  max-height: 360px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--accent) / 0.6) transparent;
}

/* Enhanced scrollbar styling */
#slash-command .command-list::-webkit-scrollbar {
  width: 6px;
}

#slash-command .command-list::-webkit-scrollbar-track {
  background: transparent;
}

#slash-command .command-list::-webkit-scrollbar-thumb {
  background: hsl(var(--accent) / 0.3);
  border-radius: 3px;
}

#slash-command .command-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.875rem 1.125rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.16, 1, 0.3, 1);
  border: 1px solid transparent;
  position: relative;
  margin-bottom: 0.25rem;
}

#slash-command .command-item:last-child {
  margin-bottom: 0;
}

#slash-command .command-item:hover,
#slash-command .command-item[data-selected="true"] {
  background-color: hsl(var(--accent) / 0.15);
  transform: translateX(2px) scale(1.005);
  border-color: hsl(var(--accent) / 0.4);
  box-shadow: 0 4px 12px hsl(var(--accent) / 0.15), 0 2px 4px
    rgba(0, 0, 0, 0.05);
}

#slash-command .command-item[data-selected="true"] {
  background-color: hsl(var(--accent) / 0.2);
  border-color: hsl(var(--accent) / 0.6);
  box-shadow: 0 4px 16px hsl(var(--accent) / 0.25), 0 2px 6px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

#slash-command .command-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.75rem;
  height: 2.75rem;
  border-radius: 0.75rem;
  background-color: hsl(var(--accent) / 0.15);
  color: hsl(var(--accent));
  flex-shrink: 0;
  transition: all 0.25s cubic-bezier(0.16, 1, 0.3, 1);
  position: relative;
  overflow: hidden;
}

#slash-command .command-item-icon::before {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(
    135deg,
    hsl(var(--accent) / 0.1),
    hsl(var(--accent) / 0.05)
  );
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.25s ease;
}

#slash-command .command-item:hover .command-item-icon,
#slash-command .command-item[data-selected="true"] .command-item-icon {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
  transform: scale(1.05);
  box-shadow: 0 4px 12px hsl(var(--accent) / 0.3), 0 2px 4px rgba(0, 0, 0, 0.1);
}

#slash-command .command-item:hover .command-item-icon::before,
#slash-command .command-item[data-selected="true"] .command-item-icon::before {
  opacity: 1;
}

#slash-command .command-item-title {
  font-size: 0.95rem; /* Slightly larger title font */
  font-weight: 500;
  transition: all 0.2s ease;
  color: hsl(var(--foreground));
}

#slash-command .command-item:hover .command-item-title,
#slash-command .command-item[data-selected="true"] .command-item-title {
  transform: translateX(1px);
  color: hsl(var(--accent-foreground)); /* Ensure title is readable on accent background */
}

#slash-command .command-item-desc {
  font-size: 0.825rem; /* Slightly larger description font */
  color: hsl(var(--muted-foreground));
  transition: all 0.2s ease;
}

#slash-command .command-item:hover .command-item-desc,
#slash-command .command-item[data-selected="true"] .command-item-desc {
  color: hsl(var(--accent-foreground) / 0.9); /* Ensure description is readable */
}

/* Enhanced Text Selection with animation */
.ProseMirror .is-selected {
  background-color: hsl(var(--primary) / 0.2) !important; /* More visible selection */
  border-radius: 3px !important; /* Slightly more rounded selection */
  position: relative;
  /* animation: selectPulse 1.8s ease-in-out infinite; */ /* Optional: re-enable if desired, can be distracting */
  box-shadow: 0 0 0 2px hsl(var(--primary) / 0.3); /* Subtle glow for selection */
}

/* Optional: Re-enable or adjust selectPulse animation if needed */
/* @keyframes selectPulse {
  0%,
  100% {
    background-color: hsl(var(--primary) / 0.2);
    box-shadow: 0 0 0 2px hsl(var(--primary) / 0.3);
  }
  50% {
    background-color: hsl(var(--primary) / 0.25);
    box-shadow: 0 0 0 3px hsl(var(--primary) / 0.4);
  }
} */

.ProseMirror ::selection {
  background-color: hsl(var(--primary) / 0.3) !important; /* More visible browser selection */
  border-radius: 3px;
  color: hsl(var(--primary-foreground)) !important;
}

/* Floating Menu Improvements with glass effect */
.is-floating-menu {
  border-radius: 0.85rem; /* Consistent with slash command */
  background-color: hsl(var(--background) / 0.9); /* Consistent with slash command */
  backdrop-filter: blur(18px); /* Consistent with slash command */
  border: 1px solid hsl(var(--border) / 0.7); /* Consistent with slash command */
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12), 0 3px 10px rgba(0, 0, 0, 0.06); /* Adjusted shadow */
  padding: 0.7rem; /* Increased padding */
  animation: floatingFadeIn 0.3s cubic-bezier(0.16, 1, 0.3, 1) forwards; /* Consistent animation */
}

.dark .is-floating-menu {
  background-color: hsl(var(--card) / 0.9); /* Consistent dark mode */
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 3px 10px rgba(0, 0, 0, 0.2); /* Adjusted dark mode shadow */
}

@keyframes floatingFadeIn {
  from {
    opacity: 0;
    transform: translateY(8px) scale(0.97);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced responsive design for slash commands */
@media (max-width: 640px) {
  #slash-command {
    max-width: 320px;
    border-radius: 0.75rem;
  }

  #slash-command .command-list {
    padding: 0.5rem;
    max-height: 280px;
  }

  #slash-command .command-item {
    padding: 0.75rem 0.875rem;
    gap: 0.75rem;
  }

  #slash-command .command-item-icon {
    width: 2.25rem;
    height: 2.25rem;
  }

  #slash-command .command-item-title {
    font-size: 0.875rem;
  }

  #slash-command .command-item-desc {
    font-size: 0.75rem;
  }
}
