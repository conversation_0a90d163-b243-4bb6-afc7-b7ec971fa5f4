/* Modern Tersa Word Editor Styles */

/* Editor Content Area */
.ProseMirror {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  font-size: 1rem;
  line-height: 1.7;
  padding: 1.5rem;
  min-height: 500px;
  color: hsl(var(--foreground));
  outline: none !important;
}

/* Typography */
.ProseMirror h1 {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.2;
  margin-top: 2rem;
  margin-bottom: 1rem;
  color: hsl(var(--foreground));
}

.ProseMirror h2 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.3;
  margin-top: 1.75rem;
  margin-bottom: 0.75rem;
}

.ProseMirror h3 {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.4;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.ProseMirror p {
  margin-bottom: 1rem;
}

/* Lists */
.ProseMirror ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

.ProseMirror ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

.ProseMirror li {
  margin-bottom: 0.25rem;
}

/* Task lists */
.ProseMirror ul[data-type="taskList"] {
  list-style: none;
  padding: 0;
}

.ProseMirror ul[data-type="taskList"] li {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.ProseMirror ul[data-type="taskList"] input[type="checkbox"] {
  margin-right: 0.5rem;
  cursor: pointer;
}

/* Blockquotes */
.ProseMirror blockquote {
  padding-left: 1rem;
  border-left: 3px solid hsl(var(--border));
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
  color: hsl(var(--muted-foreground));
  margin-bottom: 1rem;
}

/* Code */
.ProseMirror code {
  background-color: hsl(var(--muted) / 0.5);
  border-radius: 0.25rem;
  padding: 0.125rem 0.25rem;
  font-family: monospace;
  font-size: 0.875em;
}

.ProseMirror pre {
  background-color: hsl(var(--muted) / 0.7);
  border-radius: 0.25rem;
  padding: 1rem;
  overflow-x: auto;
  margin-bottom: 1rem;
  font-family: monospace;
  font-size: 0.875em;
  line-height: 1.5;
}

/* Tables */
.ProseMirror table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  margin-bottom: 1rem;
  overflow: hidden;
  border-radius: 0.375rem;
  border: 1px solid hsl(var(--border));
}

.ProseMirror table th {
  background-color: hsl(var(--secondary));
  font-weight: 600;
  color: hsl(var(--foreground));
  font-size: 0.875rem;
  padding: 0.75rem 1rem;
  border: 1px solid hsl(var(--border));
  text-align: left;
}

.ProseMirror table td {
  padding: 0.75rem 1rem;
  border: 1px solid hsl(var(--border));
  vertical-align: top;
  font-size: 0.875rem;
}

.tableWrapper {
  padding: 0;
  overflow-x: auto;
}

/* Table Selection Styles */
.ProseMirror td.selectedCell,
.ProseMirror th.selectedCell {
  background-color: hsl(var(--primary) / 0.1);
  position: relative;
}

.ProseMirror td.selectedCell::after,
.ProseMirror th.selectedCell::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px solid hsl(var(--primary) / 0.6);
  pointer-events: none;
  box-sizing: border-box;
}

/* Links */
.ProseMirror a {
  color: hsl(var(--primary));
  text-decoration: underline;
  text-decoration-color: hsl(var(--primary) / 0.4);
  text-decoration-thickness: 1px;
  text-underline-offset: 0.2em;
  transition: all 0.2s ease;
}

.ProseMirror a:hover {
  text-decoration-color: hsl(var(--primary));
  background-color: hsl(var(--primary) / 0.05);
  border-radius: 2px;
}

/* Images */
.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 0.25rem;
}

/* Horizontal Rules */
.ProseMirror hr {
  border: none;
  border-top: 1px solid hsl(var(--border));
  margin: 2rem 0;
}

/* Selection specific styles */
.ProseMirror .is-selected {
  background-color: hsl(var(--primary) / 0.15);
  border-radius: 0.25rem;
  position: relative;
}

/* Bubble menu styles */
.bubble-menu {
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 0.25rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.125rem;
  align-items: center;
  z-index: 50;
}

.dark .bubble-menu {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Make bubble menu buttons smaller */
.bubble-menu button {
  height: 28px;
  width: 28px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bubble-menu button svg {
  width: 14px;
  height: 14px;
}

/* Placeholder text styling */
.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: hsl(var(--muted-foreground) / 0.6);
  pointer-events: none;
  height: 0;
}

/* Focus ring */
.editor-paper:focus-within {
  outline: 2px solid hsl(var(--primary) / 0.4);
  outline-offset: -1px;
  transition: outline-color 0.2s ease;
}

/* Hover highlight for headings */
.ProseMirror h1:hover,
.ProseMirror h2:hover,
.ProseMirror h3:hover {
  position: relative;
}

.ProseMirror h1:hover::before,
.ProseMirror h2:hover::before,
.ProseMirror h3:hover::before {
  content: "";
  position: absolute;
  left: -16px;
  top: 50%;
  height: 0.75rem;
  width: 0.75rem;
  transform: translateY(-50%);
  border-radius: 50%;
  background-color: hsl(var(--primary) / 0.3);
  opacity: 0;
  animation: fadeIn 0.2s ease-in forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

/* Editor footer styles */
.editor-footer {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  border-top: 1px solid hsl(var(--border));
  font-size: 0.875rem;
}

.stats-counter {
  display: flex;
  gap: 0.25rem;
  align-items: center;
}

.stats-label {
  color: hsl(var(--muted-foreground));
}

.stats-value {
  font-variant-numeric: tabular-nums;
}

/* Animation for editor entry */
.animate-editor-in {
  animation: slideInEditor 0.3s ease-out;
}

@keyframes slideInEditor {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Fullscreen mode improvements */
.editor-fullscreen .editor-card {
  height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
}

.editor-fullscreen .editor-paper {
  flex-grow: 1;
  max-height: none;
}

.editor-fullscreen .TabsContent {
  flex-grow: 1;
  display: flex;
}

.editor-fullscreen .ProseMirror {
  font-size: 1.1rem;
  line-height: 1.8;
}

/* Fix for toolbar in fullscreen */
.editor-fullscreen .editor-toolbar {
  position: sticky;
  top: 0;
  z-index: 50;
  background-color: hsl(var(--background));
}

/* Slash Commands UI Enhancement */
.slash-command-suggestions {
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  z-index: 50;
  max-width: 360px;
}

.dark .slash-command-suggestions {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.slash-command-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-radius: 0;
}

.slash-command-item:hover {
  background-color: hsl(var(--accent));
}

/* Text Selection Indicator */
.ProseMirror .is-selected {
  background-color: hsl(var(--primary) / 0.15);
  border-radius: 0.25rem;
  position: relative;
}

/* Better text selection highlighting */
.ProseMirror ::selection {
  background-color: hsl(var(--primary) / 0.25);
}

/* Command Panel UI */
#slash-command {
  width: 340px;
  max-height: 330px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* Command Panel Icons */
#slash-command .command-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 34px;
  height: 34px;
  border-radius: 6px;
  background-color: hsl(var(--muted) / 0.5);
}

#slash-command .command-item-title {
  font-size: 0.875rem;
  font-weight: 500;
}

#slash-command .command-item-description {
  font-size: 0.75rem;
  color: hsl(var(--muted-foreground));
}

/* Active command highlight */
#slash-command [data-selected="true"] {
  background-color: hsl(var(--accent));
}
