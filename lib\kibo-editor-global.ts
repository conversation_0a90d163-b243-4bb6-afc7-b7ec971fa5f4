import type { Editor } from '@tiptap/core';

// Global editor instance reference
let globalKiboEditor: Editor | null = null;

/**
 * Set the global Kibo Editor instance
 */
export function setGlobalKiboEditor(editor: Editor | null): void {
  globalKiboEditor = editor;
}

/**
 * Get the global Kibo Editor instance
 */
export function getGlobalKiboEditor(): Editor | null {
  return globalKiboEditor;
}

/**
 * Add content to the global Kibo Editor
 * This is useful for adding content from other parts of the application
 */
export function addContentToKiboEditor(content: string): boolean {
  if (!globalKiboEditor) return false;

  // Focus the editor at the end
  globalKiboEditor.commands.focus('end');

  // Add a line break if there's already content
  const currentContent = globalKiboEditor.getHTML();
  if (currentContent && !currentContent.endsWith('<p></p>')) {
    globalKiboEditor.commands.insertContent('\n\n');
  }

  // Insert the new content
  globalKiboEditor.commands.insertContent(content);
  return true;
}

/**
 * Clear all content from the global Kibo Editor
 */
export function clearKiboEditorContent(): boolean {
  if (!globalKiboEditor) return false;
  globalKiboEditor.commands.clearContent();
  return true;
}

/**
 * Get the current content from the global Kibo Editor
 */
export function getKiboEditorContent(): string | null {
  if (!globalKiboEditor) return null;
  return globalKiboEditor.getHTML();
}

/**
 * Get the current plain text from the global Kibo Editor
 */
export function getKiboEditorText(): string | null {
  if (!globalKiboEditor) return null;
  return globalKiboEditor.getText();
}
