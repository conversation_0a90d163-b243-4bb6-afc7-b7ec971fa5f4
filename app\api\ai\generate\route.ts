import { getSubscribedUser } from '@/lib/auth';
import { parseError } from '@/lib/error/parse';
import { textModels } from '@/lib/models/text';
import { createRateLimiter, slidingWindow } from '@/lib/rate-limit';
import { trackCreditUsage } from '@/lib/stripe';
import { streamText } from 'ai';

export const maxDuration = 30;

const rateLimiter = createRateLimiter({
  limiter: slidingWindow(10, '1 m'),
  prefix: 'api-generate',
});

export const POST = async (req: Request) => {
  try {
    const user = await getSubscribedUser();

    if (!user) {
      return new Response('Authentication required', { status: 401 });
    }

    // Apply rate limiting
    const { success } = await rateLimiter.limit(user.id);

    if (!success) {
      return new Response('Too many requests', { status: 429 });
    }

    const { prompt, modelId } = await req.json();

    if (!prompt || typeof prompt !== 'string') {
      return new Response('Prompt is required and must be a string', {
        status: 400,
      });
    }

    if (typeof modelId !== 'string') {
      return new Response('Model must be a string', { status: 400 });
    }

    const model = textModels
      .flatMap((m) => m.models)
      .find((m) => m.id === modelId);

    if (!model) {
      return new Response('Invalid model', { status: 400 });
    }

    // Check if we should use mock response due to invalid API keys
    const apiKey = env.OPENAI_API_KEY;
    if (shouldUseMockResponse(apiKey)) {
      const mockResponse = getMockTextResponse(prompt);

      // Return mock response as a simple text response
      return new Response(mockResponse.text, {
        status: 200,
        headers: {
          'Content-Type': 'text/plain',
        },
      });
    }

    try {
      const result = streamText({
        model: model.model,
        system:
          "You are a helpful assistant that generates text based on the user's prompt.",
        prompt,
        onFinish: async ({ usage }) => {
          await trackCreditUsage({
            userId: user.id,
            action: 'generate',
            modelId: model.id,
            cost: model.getCost({
              input: usage.promptTokens,
              output: usage.completionTokens || 0,
            }),
            promptTokens: usage.promptTokens,
            completionTokens: usage.completionTokens || 0,
            totalTokens: usage.totalTokens,
          });
        },
      });

      return result.toDataStreamResponse();
    } catch (error) {
      return new Response(
        `Error: ${error instanceof Error ? error.message : 'An error occurred'}`,
        { status: 500 }
      );
    }
  } catch (error) {
    const parsedError = parseError(error);
    return new Response(parsedError.message, {
      status: parsedError.status || 500,
    });
  }
};
