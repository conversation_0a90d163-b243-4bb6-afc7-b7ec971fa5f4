'use client';

import { useCallback, useEffect, useState } from 'react';

/**
 * Lightweight hook for tracking text selection state in the editor
 * Most of the actual selection enhancement is now handled by the BetterTextSelection extension
 * @param editorRef Reference to the editor instance
 */
export const useEnhancedSelection = (editorRef: React.RefObject<any>) => {
  const [isTextSelected, setIsTextSelected] = useState(false);

  // Track selection changes
  const handleSelectionChange = useCallback(() => {
    if (!editorRef.current) return;

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      setIsTextSelected(false);
      return;
    }

    const range = selection.getRangeAt(0);
    if (range.collapsed) {
      setIsTextSelected(false);
      return;
    }

    // We have an actual text selection
    setIsTextSelected(true);
  }, [editorRef]);

  // Set up listeners
  useEffect(() => {
    document.addEventListener('selectionchange', handleSelectionChange);
    document.addEventListener('mouseup', handleSelectionChange);

    return () => {
      document.removeEventListener('selectionchange', handleSelectionChange);
      document.removeEventListener('mouseup', handleSelectionChange);
    };
  }, [handleSelectionChange]);
  return {
    isTextSelected,
  };
};
