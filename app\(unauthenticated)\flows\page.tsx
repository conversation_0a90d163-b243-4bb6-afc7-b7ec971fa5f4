import { Skeleton } from '@/components/ui/skeleton'; // For loading state
import type { NextPage } from 'next';
import { Suspense } from 'react';
import { FlowGalleryClientPage } from './_components/flow-gallery-client-page';

const FlowsPage: NextPage = async () => {
  // The client component will handle all search parameters directly
  // This avoids the warning about awaiting searchParams before accessing properties

  // Initial data fetching on the server based on searchParams
  // The client component will handle further interactions

  return (
    <div className="container mx-auto px-4 py-8">
      <header className="mb-8 text-center">
        <h1 className="font-bold text-4xl tracking-tight lg:text-5xl">
          Flow Template Gallery
        </h1>
        <p className="mt-3 text-lg text-muted-foreground">
          Discover and import pre-built AI workflows to kickstart your projects.
        </p>
      </header>

      {/* 
        The actual fetching and display logic will be in FlowGalleryClientPage.
        We pass initial search params to it, and it can fetch initial data itself or 
        we can pass prefetched data. For simplicity and to allow client-side updates easily,
        let's have the client component fetch its initial data based on searchParams.
      */}
      <Suspense fallback={<GalleryLoadingSkeleton />}>
        <FlowGalleryClientPage />
      </Suspense>
    </div>
  );
};

const GalleryLoadingSkeleton = () => (
  <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
    {[...Array(6)].map((_, i) => (
      <div key={i} className="space-y-3 rounded-lg border p-4">
        <Skeleton className="h-6 w-3/4" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-5/6" />
        <div className="flex items-center justify-between pt-2">
          <Skeleton className="h-8 w-24" />
          <Skeleton className="h-4 w-16" />
        </div>
      </div>
    ))}
  </div>
);

export default FlowsPage;
