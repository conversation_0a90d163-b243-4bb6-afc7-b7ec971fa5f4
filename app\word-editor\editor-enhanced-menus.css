/* Enhanced Floating Menu and Enhanced Table Menu Styles */

/* Enhanced Floating Menu */
.floating-menu {
  z-index: 50 !important;
  min-width: 200px !important;
  animation: floatingMenuFadeIn 0.3s cubic-bezier(0.16, 1, 0.3, 1) forwards !important;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  border-radius: 0.75rem !important;
  overflow: hidden !important;
  backdrop-filter: blur(10px) !important;
}

@keyframes floatingMenuFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.97);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.enhanced-table-menu {
  z-index: 50;
  animation: floatingMenuFadeIn 0.2s ease-in-out;
}

/* Menu Item Hover Effects */
.floating-menu-item {
  transition: background-color 0.15s ease, color 0.15s ease;
  border-radius: 0.375rem;
  padding: 0.375rem 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  cursor: pointer;
}

.floating-menu-item:hover {
  background-color: hsl(var(--accent));
}

.floating-menu-item.active {
  background-color: hsl(var(--primary) / 0.1);
  color: hsl(var(--primary));
}

/* Submenu animations */
.submenu-container {
  overflow: hidden;
  max-height: 0;
  transition: max-height 0.2s ease-out;
}

.submenu-container.open {
  max-height: 500px;
}

.submenu-item {
  padding-left: 1.75rem;
}

/* Enhanced table menu */
.table-cell-actions {
  display: flex;
  gap: 0.25rem;
  padding: 0.25rem;
  border-radius: 0.25rem;
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Document selector styling */
.document-card {
  position: relative;
  border-radius: 0.375rem;
  border: 1px solid hsl(var(--border));
  transition: all 0.2s ease;
}

.document-card:hover {
  border-color: hsl(var(--primary) / 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.document-card.active {
  border-color: hsl(var(--primary));
  background-color: hsl(var(--primary) / 0.05);
}

.document-title {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.document-date {
  font-size: 0.75rem;
  color: hsl(var(--muted-foreground));
}

.document-actions {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  opacity: 0;
  transition: opacity 0.15s ease;
}

.document-card:hover .document-actions {
  opacity: 1;
}
