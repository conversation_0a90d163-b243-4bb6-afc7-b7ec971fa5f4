'use client';
import type { ReactNode } from 'react';

type PostHogProviderProps = {
  children: ReactNode;
};

export const PostHogProvider = ({ children }: PostHogProviderProps) => {
  // Skip PostHog initialization to prevent API errors
  // Just return children without PostHog functionality
  return <>{children}</>;
};

export const PostHogIdentifyProvider = ({ children }: PostHogProviderProps) => {
  // Mock implementation to avoid making actual API calls
  return <>{children}</>;

  // Original implementation:
  // const posthog = usePostHog();
  // const user = useUser();

  // useEffect(() => {
  //   if (posthog && user) {
  //     posthog.identify(user.id, {
  //       email: user.email,
  //       name: user.user_metadata.name,
  //     });
  //   }
  // }, [posthog, user]);

  // return children;
};
