import { currentUser } from '@/lib/auth';
import { database } from '@/lib/database';
import { projects } from '@/schema';
import { eq } from 'drizzle-orm';
import type { Metadata } from 'next';
import { redirect } from 'next/navigation';
import Home from './(unauthenticated)/home/<USER>';
import UnauthenticatedLayout from './(unauthenticated)/layout';
import { createProjectAction } from './actions/project/create';

export const metadata: Metadata = {
  title: 'Tersa',
  description: 'Visualize your AI workflows.',
};

const Index = async () => {
  const user = await currentUser();

  // Hardcoded mock project to bypass database connection issues
  const mockProject = {
    id: 'mock-project-1',
    name: 'Demo Project',
    transcriptionModel: 'openai/whisper',
    visionModel: 'openai/gpt4-vision',
    createdAt: new Date().toISOString(),
    content: {},
    userId: user.id,
    image: null,
    members: []
  };
  
  // Redirect directly to the mock project
  return redirect(`/projects/${mockProject.id}`);

  // Original code:
  // const allProjects = await database
  //   .select()
  //   .from(projects)
  //   .where(eq(projects.userId, user.id));

  // if (!allProjects.length) {
  //   const newProject = await createProjectAction('Untitled Project');

  //   if ('error' in newProject) {
  //     throw new Error(newProject.error);
  //   }

  //   return redirect(`/projects/${newProject.id}`);
  // }

  // redirect(`/projects/${allProjects[0].id}`);
};

export default Index;
