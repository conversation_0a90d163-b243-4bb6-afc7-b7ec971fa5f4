'use client';

import { PromptFlowGeneratorInput } from '@/components/canvas/PromptFlowGeneratorInput';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useReactFlow, useViewport } from '@xyflow/react';
import { Download, Upload, ZoomIn, ZoomOut } from 'lucide-react';
import type { ChangeEvent, FC } from 'react';

interface ControlsProps {
  onDownload?: () => void;
  onUpload?: (event: ChangeEvent<HTMLInputElement>) => void;
}

export const Controls: FC<ControlsProps> = ({ onDownload, onUpload }) => {
  const { zoomIn, zoomOut } = useReactFlow();
  const { zoom } = useViewport();

  return (
    <div className="absolute bottom-4 left-4 z-10">
      <div className="flex items-center gap-2 rounded-lg bg-background/80 p-2 backdrop-blur-sm">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="icon" onClick={() => zoomIn()}>
                <ZoomIn />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Zoom in</TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="icon" onClick={() => zoomOut()}>
                <ZoomOut />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Zoom out</TooltipContent>
          </Tooltip>

          {onDownload && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" onClick={onDownload}>
                  <Download />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Download Flow</TooltipContent>
            </Tooltip>
          )}

          {onUpload && (
            <>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() =>
                      document.getElementById('upload-flow-input')?.click()
                    }
                  >
                    <Upload />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Upload Flow</TooltipContent>
              </Tooltip>
              <input
                id="upload-flow-input"
                type="file"
                accept=".json"
                style={{ display: 'none' }}
                onChange={onUpload}
              />
            </>
          )}

          <Tooltip>
            <TooltipTrigger asChild>
              <PromptFlowGeneratorInput />
            </TooltipTrigger>
            <TooltipContent>Generate Flow from Prompt</TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <div className="text-muted-foreground text-xs">
          {Math.round(zoom * 100)}%
        </div>
      </div>
    </div>
  );
};
