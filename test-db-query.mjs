import { dirname, join } from 'path';
// test-db-query.mjs
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import { sql } from 'drizzle-orm';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config({
  path: join(__dirname, '.env.local'),
});

// Create a simple test function
async function testQuery() {
  try {
    console.log('Testing database setup with PostgreSQL...');

    // Import modules after env is loaded
    const { database } = await import('./lib/database.js');
    const postgres = await import('postgres');

    console.log('Database client initialized');
    console.log(
      'DATABASE_URL:',
      process.env.DATABASE_URL?.substring(0, 20) + '...'
    );

    // Simple health check query
    const result = await database.execute(sql`SELECT 1 as test`);
    console.log('Test query successful:', result);

    console.log('All checks passed!');
    process.exit(0);
  } catch (error) {
    console.error('Error in database test:', error);
    process.exit(1);
  }
}

// Run the test
testQuery().catch((err) => {
  console.error('Unhandled error:', err);
  process.exit(1);
});
