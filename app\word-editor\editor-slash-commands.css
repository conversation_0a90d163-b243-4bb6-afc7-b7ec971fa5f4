/* Specialized CSS for Slash Commands and Selection */

/* Slash Command Menu Enhanced Styling */
#slash-command {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 360px;
  width: 100%;
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  animation: fadeInMenu 0.2s ease forwards;
  z-index: 99;
}

.dark #slash-command {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
}

@keyframes fadeInMenu {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

#slash-command .command-list {
  padding: 0.25rem;
}

#slash-command .command-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

#slash-command .command-item:hover,
#slash-command .command-item[data-selected="true"] {
  background-color: hsl(var(--accent));
}

#slash-command .command-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 0.375rem;
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
  flex-shrink: 0;
}

#slash-command .command-item-title {
  font-size: 0.875rem;
  font-weight: 500;
}

#slash-command .command-item-desc {
  font-size: 0.75rem;
  color: hsl(var(--muted-foreground));
}

/* Enhanced Text Selection */
.ProseMirror .is-selected {
  background-color: hsl(var(--primary) / 0.15);
  border-radius: 2px;
  position: relative;
}

.ProseMirror ::selection {
  background-color: hsl(var(--primary) / 0.25);
}

/* Floating Menu Improvements */
.is-floating-menu {
  border-radius: 0.5rem;
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 0.5rem;
  animation: fadeInMenu 0.2s ease forwards;
}

.dark .is-floating-menu {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Table Menu Improvements */
.is-table-menu {
  border-radius: 0.5rem;
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 0.5rem;
  animation: fadeInMenu 0.2s ease forwards;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  z-index: 50;
}

.dark .is-table-menu {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Tooltip improvements for editor commands */
.tooltip-content {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  animation: fadeInMenu 0.15s ease forwards;
  z-index: 100;
}
