import { nanoid } from 'nanoid';

// Mock responses for different content types when API keys are not available

export function getMockImageResponse() {
  return {
    image: {
      base64: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', // 1x1 transparent PNG
      uint8Array: Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64'),
      mimeType: 'image/png' as const,
    },
    usage: {
      textInput: 10,
      imageInput: 0,
      output: 1,
    },
  };
}

export function getMockVideoResponse() {
  // Return a mock video URL (could be a placeholder video or empty video)
  return {
    videoUrl: '/demo/placeholder-video.mp4',
    usage: {
      duration: 5,
      frames: 120,
    },
  };
}

export function getMockSpeechResponse() {
  // Return a mock audio buffer (silent audio)
  const silentAudioBuffer = Buffer.alloc(1024); // Empty buffer for silent audio
  return {
    audio: silentAudioBuffer,
    usage: {
      characters: 100,
    },
  };
}

export function getMockTextResponse(prompt: string) {
  return {
    text: `This is a mock response for the prompt: "${prompt}". In a real environment, this would be generated by an AI model. The mock system is active because no valid API keys were found.`,
    usage: {
      promptTokens: prompt.length / 4, // Rough estimate
      completionTokens: 50,
      totalTokens: (prompt.length / 4) + 50,
    },
  };
}

export function getMockCodeResponse(prompt: string, language: string = 'javascript') {
  const codeExamples = {
    javascript: `// Mock JavaScript code for: ${prompt}
function mockFunction() {
  console.log('This is mock code generated when API keys are not available');
  return 'Mock result';
}

mockFunction();`,
    python: `# Mock Python code for: ${prompt}
def mock_function():
    print('This is mock code generated when API keys are not available')
    return 'Mock result'

mock_function()`,
    typescript: `// Mock TypeScript code for: ${prompt}
function mockFunction(): string {
  console.log('This is mock code generated when API keys are not available');
  return 'Mock result';
}

mockFunction();`,
  };

  return {
    text: codeExamples[language as keyof typeof codeExamples] || codeExamples.javascript,
    language,
    usage: {
      promptTokens: prompt.length / 4,
      completionTokens: 100,
      totalTokens: (prompt.length / 4) + 100,
    },
  };
}

// Helper function to check if an API key is valid
export function isValidApiKey(apiKey: string | undefined): boolean {
  if (!apiKey) return false;
  if (apiKey === 'dummy-key') return false;
  if (apiKey === 'mock-value') return false;
  if (apiKey.startsWith('dummy-')) return false;
  if (apiKey.startsWith('mock-')) return false;
  if (apiKey.length < 10) return false; // Most real API keys are longer
  return true;
}

// Helper function to determine if we should use mock responses
export function shouldUseMockResponse(apiKey: string | undefined): boolean {
  return !isValidApiKey(apiKey);
}
