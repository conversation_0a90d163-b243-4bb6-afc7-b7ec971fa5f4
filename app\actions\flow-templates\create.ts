'use server';

import { db } from '@/lib/db';
import { flow_templates } from '@/schema';
import { revalidatePath } from 'next/cache';
import { z } from 'zod';
import { auth } from '@clerk/nextjs/server';

const CreateFlowTemplateSchema = z.object({
  name: z.string().min(1, { message: 'Template name is required' }),
  description: z.string().optional(),
  flow_json: z.string().min(1, { message: 'Flow JSON is required' }), // Assuming flow_json is passed as a string
  category_id: z.string().optional(),
  is_public: z.boolean().default(true),
});

export async function createFlowTemplateAction(formData: FormData) {
  const { userId } = auth();
  if (!userId) {
    return {
      success: false,
      error: 'Unauthorized: You must be logged in to create a flow template.',
    };
  }
  // TODO: Add role-based access control (e.g., only admins or specific roles can create templates)

  let flowJsonParsed;
  try {
    const flowJsonString = formData.get('flow_json') as string;
    if (!flowJsonString) throw new Error('Flow JSON is missing.');
    flowJsonParsed = JSON.parse(flowJsonString);
  } catch (e) {
    return {
      success: false,
      error: 'Invalid Flow JSON provided.',
    };
  }

  const validatedFields = CreateFlowTemplateSchema.safeParse({
    name: formData.get('name'),
    description: formData.get('description'),
    flow_json: formData.get('flow_json'), // Keep as string for schema validation, use parsed for DB
    category_id: formData.get('category_id'),
    is_public: formData.get('is_public') === 'true' || formData.get('is_public') === true, // Handle string from FormData
  });

  if (!validatedFields.success) {
    return {
      success: false,
      error: 'Invalid fields for flow template creation.',
      errors: validatedFields.error.flatten().fieldErrors,
    };
  }

  const { name, description, category_id, is_public } = validatedFields.data;

  try {
    const [newTemplate] = await db
      .insert(flow_templates)
      .values({
        name,
        description,
        flow_json: flowJsonParsed, // Use the parsed JSON object here
        category_id: category_id || null, // Handle optional category_id
        user_id: userId, // Associate with the user who created it
        is_public,
        // upvotes will default to 0 as per schema
      })
      .returning();

    revalidatePath('/admin/flow-templates');
    revalidatePath('/flows'); // Revalidate public flow gallery
    if (category_id) {
      revalidatePath(`/flows/category/${category_id}`); // If category-specific pages exist
    }

    return {
      success: true,
      data: newTemplate,
      message: 'Flow template created successfully.',
    };
  } catch (error) {
    console.error('Error creating flow template:', error);
    return {
      success: false,
      error: 'Failed to create flow template. Please try again.',
    };
  }
}