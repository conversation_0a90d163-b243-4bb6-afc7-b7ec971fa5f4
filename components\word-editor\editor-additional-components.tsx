'use client';

import { Button } from '@/components/ui/button';
import { CheckSquare, Heading3, Quote } from 'lucide-react';

export const EditorNodeHeading3 = ({
  hideName = false,
}: {
  hideName?: boolean;
}) => {
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => {
        const editor = window.__KIBO_EDITOR_INSTANCE;
        if (editor) {
          editor.chain().focus().toggleHeading({ level: 3 }).run();
        }
      }}
      className="h-8 w-8 p-0"
    >
      <Heading3 size={16} />
      {!hideName && <span className="sr-only">Heading 3</span>}
    </Button>
  );
};

export const EditorNodeTaskList = ({
  hideName = false,
}: {
  hideName?: boolean;
}) => {
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => {
        const editor = window.__KIBO_EDITOR_INSTANCE;
        if (editor) {
          editor.chain().focus().toggleTaskList().run();
        }
      }}
      className="h-8 w-8 p-0"
    >
      <CheckSquare size={16} />
      {!hideName && <span className="sr-only">Task List</span>}
    </Button>
  );
};

export const EditorNodeQuote = ({
  hideName = false,
}: {
  hideName?: boolean;
}) => {
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => {
        const editor = window.__KIBO_EDITOR_INSTANCE;
        if (editor) {
          editor.chain().focus().toggleBlockquote().run();
        }
      }}
      className="h-8 w-8 p-0"
    >
      <Quote size={16} />
      {!hideName && <span className="sr-only">Blockquote</span>}
    </Button>
  );
};
