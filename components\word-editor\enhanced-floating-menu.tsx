'use client';

import { cn } from '@/lib/utils';
import type { Editor } from '@tiptap/core';
import { FloatingMenu } from '@tiptap/react';
import {
  Check,
  CheckSquare,
  ChevronRight,
  Code,
  Heading1,
  Heading2,
  Heading3,
  Image,
  List,
  ListOrdered,
  Quote,
  Sparkles,
  Table,
} from 'lucide-react';
import { useState } from 'react';
import type React from 'react';

interface FloatingMenuItem {
  name: string;
  icon: React.ReactNode;
  command: (editor: Editor) => void;
  isActive?: (editor: Editor) => boolean;
  subMenu?: FloatingMenuItem[];
}

interface EnhancedFloatingMenuProps {
  editor: Editor;
}

export function EnhancedFloatingMenu({ editor }: EnhancedFloatingMenuProps) {
  const [activeSubMenu, setActiveSubMenu] = useState<string | null>(null);

  const items: FloatingMenuItem[] = [
    {
      name: 'Text',
      icon: <ChevronRight className="h-4 w-4" />,
      command: () => {
        setActiveSubMenu(activeSubMenu === 'Text' ? null : 'Text');
      },
      subMenu: [
        {
          name: 'Heading 1',
          icon: <Heading1 className="h-4 w-4" />,
          command: (editor) =>
            editor.chain().focus().toggleHeading({ level: 1 }).run(),
          isActive: (editor) => editor.isActive('heading', { level: 1 }),
        },
        {
          name: 'Heading 2',
          icon: <Heading2 className="h-4 w-4" />,
          command: (editor) =>
            editor.chain().focus().toggleHeading({ level: 2 }).run(),
          isActive: (editor) => editor.isActive('heading', { level: 2 }),
        },
        {
          name: 'Heading 3',
          icon: <Heading3 className="h-4 w-4" />,
          command: (editor) =>
            editor.chain().focus().toggleHeading({ level: 3 }).run(),
          isActive: (editor) => editor.isActive('heading', { level: 3 }),
        },
      ],
    },
    {
      name: 'Bullet List',
      icon: <List className="h-4 w-4" />,
      command: (editor) => editor.chain().focus().toggleBulletList().run(),
      isActive: (editor) => editor.isActive('bulletList'),
    },
    {
      name: 'Numbered List',
      icon: <ListOrdered className="h-4 w-4" />,
      command: (editor) => editor.chain().focus().toggleOrderedList().run(),
      isActive: (editor) => editor.isActive('orderedList'),
    },
    {
      name: 'Task List',
      icon: <CheckSquare className="h-4 w-4" />,
      command: (editor) => editor.chain().focus().toggleTaskList().run(),
      isActive: (editor) => editor.isActive('taskList'),
    },
    {
      name: 'Quote',
      icon: <Quote className="h-4 w-4" />,
      command: (editor) => editor.chain().focus().toggleBlockquote().run(),
      isActive: (editor) => editor.isActive('blockquote'),
    },
    {
      name: 'Code Block',
      icon: <Code className="h-4 w-4" />,
      command: (editor) => editor.chain().focus().toggleCodeBlock().run(),
      isActive: (editor) => editor.isActive('codeBlock'),
    },
    {
      name: 'Table',
      icon: <Table className="h-4 w-4" />,
      command: (editor) =>
        editor
          .chain()
          .focus()
          .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
          .run(),
    },
    {
      name: 'Image',
      icon: <Image className="h-4 w-4" />,
      command: (editor) => {
        const url = window.prompt('URL');
        if (url) {
          editor.chain().focus().setImage({ src: url }).run();
        }
      },
    },
    {
      name: 'AI Assist',
      icon: <Sparkles className="h-4 w-4" />,
      command: () => {
        // This will be implemented later with the AI functionality
        alert('AI functionality coming soon!');
      },
    },
  ];
  // Handle menu item click
  const onMenuItemClick = (item: FloatingMenuItem) => {
    if (item.subMenu) {
      setActiveSubMenu(activeSubMenu === item.name ? null : item.name);
    } else {
      item.command(editor);
    }
  };

  // Render menu item
  const renderMenuItem = (item: FloatingMenuItem) => {
    const isActive = item.isActive ? item.isActive(editor) : false;
    const isSubMenuOpen = activeSubMenu === item.name;

    return (
      <div key={item.name} className="relative">
        <button
          type="button"
          onClick={() => onMenuItemClick(item)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              onMenuItemClick(item);
            }
          }}
          className={cn(
            'flex w-full cursor-pointer items-center gap-2 rounded-md px-3 py-2 text-sm transition-all',
            isActive ? 'bg-primary/10 text-primary' : 'hover:bg-muted/60',
            isSubMenuOpen && 'bg-accent/20'
          )}
        >
          <div className="flex h-5 w-5 items-center justify-center rounded-sm bg-accent/10">
            {item.icon}
          </div>
          <span className="font-medium">{item.name}</span>
          {item.subMenu && (
            <ChevronRight
              className={cn(
                'ml-auto h-4 w-4 transition-transform duration-200',
                isSubMenuOpen && 'rotate-90'
              )}
            />
          )}
          {isActive && !item.subMenu && <Check className="ml-auto h-4 w-4" />}
        </button>

        {item.subMenu && isSubMenuOpen && (
          <div className="mt-1 ml-6 flex flex-col gap-1 border-border border-l-2 pl-2">
            {item.subMenu.map((subItem) => {
              const isSubActive = subItem.isActive
                ? subItem.isActive(editor)
                : false;

              return (
                <button
                  type="button"
                  key={subItem.name}
                  onClick={() => subItem.command(editor)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      subItem.command(editor);
                    }
                  }}
                  className={cn(
                    'flex w-full cursor-pointer items-center gap-2 rounded-md px-3 py-1.5 text-sm transition-colors',
                    isSubActive
                      ? 'bg-primary/10 text-primary'
                      : 'hover:bg-muted/60'
                  )}
                >
                  <div className="flex h-4 w-4 items-center justify-center">
                    {subItem.icon}
                  </div>
                  <span>{subItem.name}</span>
                  {isSubActive && (
                    <Check className="ml-auto h-3 w-3 text-primary" />
                  )}
                </button>
              );
            })}
          </div>
        )}
      </div>
    );
  };

  return (
    <FloatingMenu
      editor={editor}
      shouldShow={({ state }) => {
        // Only show when cursor is in an empty paragraph at the beginning of a line
        const { selection } = state;
        const { $from, empty } = selection;

        if (!empty) {
          return false;
        }

        // Check if we're at the start of an empty paragraph
        const isEmptyParagraph =
          $from.parent.type.name === 'paragraph' &&
          $from.parent.content.size === 0;
        const isAtStart = $from.parentOffset === 0;

        return isEmptyParagraph && isAtStart;
      }}
      tippyOptions={{
        duration: 100,
        placement: 'left',
        offset: [0, 16],
        delay: [500, 0], // Longer delay to avoid interrupting writing flow
      }}
      className="is-floating-menu floating-menu fade-in-50 flex min-w-[220px] max-w-[250px] animate-in flex-col gap-1 overflow-hidden rounded-xl border border-border/60 bg-background/95 p-3 shadow-xl backdrop-blur-md duration-300"
    >
      <div className="mb-2 flex items-center gap-2 px-2 py-1.5 font-medium text-muted-foreground text-xs uppercase tracking-wide">
        <Sparkles className="h-4 w-4 animate-pulse text-primary" />
        Insert Content
      </div>
      <div className="space-y-1">
        {items.map((item) => renderMenuItem(item))}
      </div>
    </FloatingMenu>
  );
}
