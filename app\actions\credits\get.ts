'use server';

import { currentUserProfile } from '@/lib/auth';
import { env } from '@/lib/env';
import { parseError } from '@/lib/error/parse';
import { stripe } from '@/lib/stripe';

const HOBBY_CREDITS = 200;
const DEVELOPMENT_CREDITS = 999999; // A large number for development

export const getCredits = async (): Promise<
  | {
      credits: number;
    }
  | {
      error: string;
    }
> => {
  // If in development mode, grant a large number of credits and bypass other checks
  if (process.env.NODE_ENV === 'development') {
    return { credits: DEVELOPMENT_CREDITS };
  }

  try {
    const profile = await currentUserProfile();

    if (!profile) {
      // Should not happen if currentUserProfile throws an error as expected
      return { error: 'User profile not found after authentication.' };
    }

    // If user has no customerId or no subscriptionId, or is on the hobby plan, give HOBBY_CREDITS.
    if (
      !profile.customerId ||
      !profile.subscriptionId ||
      profile.productId === env.STRIPE_HOBBY_PRODUCT_ID
    ) {
      return { credits: HOBBY_CREDITS };
    }

    // For paid plans, proceed with Stripe invoice check to calculate remaining credits
    const upcomingInvoice = await stripe.invoices.createPreview({
      subscription: profile.subscriptionId,
    });

    const usageProductLineItem = upcomingInvoice.lines.data.find(
      (line) =>
        line.pricing?.price_details?.product === env.STRIPE_USAGE_PRODUCT_ID
    );

    if (!usageProductLineItem) {
      // This case implies a non-hobby subscription that *doesn\'t* have the expected usage product.
      // This could be a different type of plan (e.g., unlimited, or fixed credits not metered via usage product).
      // For now, we\'ll throw an error, but this might need specific handling based on other plan types.
      throw new Error(
        'Usage product line item not found for this subscription, and not a hobby plan.'
      );
    }

    if (!usageProductLineItem.pricing?.price_details?.price) {
      throw new Error('Usage product line item price not found');
    }

    const usagePrice = await stripe.prices.retrieve(
      usageProductLineItem.pricing.price_details.price,
      { expand: ['tiers'] }
    );

    if (!usagePrice.tiers?.length) {
      throw new Error('Usage price tiers not found');
    }

    let creditsLimit = 0;
    // up_to can be null for 'infinity' tier
    if (usagePrice.tiers[0].up_to === null) {
      creditsLimit = Number.POSITIVE_INFINITY;
    } else {
      creditsLimit = usagePrice.tiers[0].up_to;
    }

    const usage = usageProductLineItem?.quantity ?? 0;

    if (creditsLimit === Number.POSITIVE_INFINITY) {
      return { credits: Number.POSITIVE_INFINITY };
    }

    return {
      credits: creditsLimit - usage,
    };
  } catch (error) {
    // parseError now returns a string directly
    const errorMessage = parseError(error);
    return { error: errorMessage };
  }
};
