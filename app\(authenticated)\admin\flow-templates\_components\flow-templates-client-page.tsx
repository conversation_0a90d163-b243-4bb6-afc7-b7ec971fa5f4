'use client';

import { useState, useEffect } from 'react';
import { flow_templates, categories as categoriesSchema } from '@/schema';
import type { InferSelectModel } from 'drizzle-orm';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { PlusCircle, Edit, Trash2, MoreHorizontal, Eye, EyeOff } from 'lucide-react';
import { FlowTemplateFormModal } from './flow-template-form-modal'; // To be created
import { DeleteConfirmationDialog } from '@/components/ui/delete-confirmation-dialog';
import {
  createFlowTemplateAction,
  updateFlowTemplateAction,
  deleteFlowTemplateAction,
  getFlowTemplatesAction,
} from '@/app/actions/flow-templates';
import { toast } from 'sonner';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { PaginationControls } from '@/components/ui/pagination-controls'; // To be created

// Types inferred from schema
type FlowTemplate = InferSelectModel<typeof flow_templates> & { categoryName?: string | null };
type Category = InferSelectModel<typeof categoriesSchema>;

interface FlowTemplatesClientPageProps {
  initialTemplates: FlowTemplate[];
  initialCategories: Category[];
  initialPagination: any; // Define a proper pagination type later
}

export function FlowTemplatesClientPage({
  initialTemplates,
  initialCategories,
  initialPagination,
}: FlowTemplatesClientPageProps) {
  const [templates, setTemplates] = useState<FlowTemplate[]>(initialTemplates);
  const [categories] = useState<Category[]>(initialCategories); // Categories are less likely to change client-side here
  const [pagination, setPagination] = useState(initialPagination);
  const [isLoading, setIsLoading] = useState(false);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<FlowTemplate | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [templateToDelete, setTemplateToDelete] = useState<FlowTemplate | null>(null);

  const fetchTemplates = async (page = 1, limit = 10) => {
    setIsLoading(true);
    const result = await getFlowTemplatesAction({ adminMode: true, page, limit });
    if (result.success && result.data) {
      setTemplates(result.data as FlowTemplate[]); // Cast needed if type from action is slightly different
      setPagination(result.pagination);
    } else {
      toast.error(result.error || 'Failed to fetch templates');
    }
    setIsLoading(false);
  };

  useEffect(() => {
    // Initial load is handled by props, this is for subsequent client-side changes if any
  }, []);

  const handleOpenCreateModal = () => {
    setEditingTemplate(null);
    setIsModalOpen(true);
  };

  const handleOpenEditModal = (template: FlowTemplate) => {
    setEditingTemplate(template);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingTemplate(null);
  };

  const handleOpenDeleteDialog = (template: FlowTemplate) => {
    setTemplateToDelete(template);
    setIsDeleteDialogOpen(true);
  };

  const handleCloseDeleteDialog = () => {
    setTemplateToDelete(null);
    setIsDeleteDialogOpen(false);
  };

  const handleSubmitTemplate = async (formData: FormData) => {
    const action = editingTemplate ? updateFlowTemplateAction : createFlowTemplateAction;
    if (editingTemplate) {
      formData.append('id', editingTemplate.id);
    }

    // Ensure is_public is correctly formatted if present
    if (formData.has('is_public')) {
        formData.set('is_public', String(formData.get('is_public') === 'true' || formData.get('is_public') === true));
    }

    const result = await action(formData);
    if (result.success && result.data) {
      toast.success(editingTemplate ? 'Template updated successfully!' : 'Template created successfully!');
      fetchTemplates(pagination.page, pagination.limit); // Refresh list
      handleCloseModal();
    } else {
      toast.error(result.error || 'An unknown error occurred.');
    }
  };

  const handleDeleteTemplate = async () => {
    if (!templateToDelete) return;
    const formData = new FormData();
    formData.append('id', templateToDelete.id);

    const result = await deleteFlowTemplateAction(formData);
    if (result.success) {
      toast.success('Template deleted successfully!');
      fetchTemplates(pagination.page, pagination.limit); // Refresh list
      handleCloseDeleteDialog();
    } else {
      toast.error(result.error || 'Failed to delete template.');
    }
  };
  
  const handlePageChange = (newPage: number) => {
    fetchTemplates(newPage, pagination.limit);
  };

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Manage Flow Templates</CardTitle>
            <CardDescription>View, create, edit, and delete flow templates for the gallery.</CardDescription>
          </div>
          <Button onClick={handleOpenCreateModal} size="sm">
            <PlusCircle className="mr-2 h-4 w-4" /> Add Template
          </Button>
        </CardHeader>
        <CardContent>
          {isLoading && <p>Loading templates...</p>} 
          {!isLoading && templates.length === 0 ? (
            <p className="text-muted-foreground">No flow templates found. Get started by adding one!</p>
          ) : (
            <>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Upvotes</TableHead>
                  <TableHead>Created At</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {templates.map((template) => (
                  <TableRow key={template.id}>
                    <TableCell className="font-medium">{template.name}</TableCell>
                    <TableCell>{template.categoryName || <span className="text-muted-foreground">N/A</span>}</TableCell>
                    <TableCell>
                        <Badge variant={template.is_public ? 'default' : 'secondary'}>
                            {template.is_public ? <Eye className="mr-1 h-3 w-3" /> : <EyeOff className="mr-1 h-3 w-3" />}
                            {template.is_public ? 'Public' : 'Private'}
                        </Badge>
                    </TableCell>
                    <TableCell>{template.upvotes}</TableCell>
                    <TableCell>{new Date(template.created_at).toLocaleDateString()}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleOpenEditModal(template)}>
                            <Edit className="mr-2 h-4 w-4" /> Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleOpenDeleteDialog(template)} className="text-destructive">
                            <Trash2 className="mr-2 h-4 w-4" /> Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            <PaginationControls 
                currentPage={pagination.page}
                totalPages={pagination.totalPages}
                onPageChange={handlePageChange}
             />
            </>
          )}
        </CardContent>
      </Card>

      {isModalOpen && (
        <FlowTemplateFormModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          onSubmit={handleSubmitTemplate}
          template={editingTemplate}
          categories={categories}
        />
      )}

      {templateToDelete && (
        <DeleteConfirmationDialog
          isOpen={isDeleteDialogOpen}
          onClose={handleCloseDeleteDialog}
          onConfirm={handleDeleteTemplate}
          title="Delete Flow Template"
          description={`Are you sure you want to delete the template "${templateToDelete.name}"? This action cannot be undone.`}
        />
      )}
    </>
  );
}