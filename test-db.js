// Test database script
// Save this as test-db.js and run with: node test-db.js
require('dotenv').config({ path: '.env.local' });

const postgres = require('postgres');
const { drizzle } = require('drizzle-orm/postgres-js');
const schema = require('./schema');

async function main() {
  try {
    // Create PostgreSQL client
    const connectionString = process.env.DATABASE_URL;
    if (!connectionString) {
      throw new Error('DATABASE_URL not found in environment variables');
    }

    console.log(
      `Connecting to database: ${connectionString.substring(0, 20)}...`
    );
    const client = postgres(connectionString, {
      max: 10,
      idle_timeout: 20,
      connect_timeout: 20,
    });

    // Create drizzle instance
    const db = drizzle(client, { schema });

    // Test a simple query
    console.log('Testing database connection...');
    const result = await client`SELECT current_timestamp;`;
    console.log('Connection successful:', result);

    console.log('Done!');
    process.exit(0);
  } catch (error) {
    console.error('Database test error:', error);
    process.exit(1);
  }
}

main();
