/*
  Enhanced <PERSON>vas Editor Styles for Tersa Word Editor
  Optimized for the canvas editor panel with modern, beautiful styling
*/

/* ===== CANVAS EDITOR CONTAINER ===== */

.canvas-editor-enhanced {
  background: linear-gradient(135deg, 
    hsl(var(--background) / 0.98), 
    hsl(var(--background) / 0.95)) !important;
  backdrop-filter: blur(10px) saturate(120%) !important;
  border: 1px solid hsl(var(--border) / 0.6) !important;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1) !important;
}

.canvas-editor-enhanced:hover {
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.12),
    0 6px 20px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

/* ===== CANVAS EDITOR CONTENT ===== */

.prose-writer-canvas {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, 
    "Helvetica Neue", Arial, sans-serif !important;
  font-size: 1rem !important;
  line-height: 1.7 !important;
  color: hsl(var(--foreground)) !important; /* Full opacity for better visibility */
  padding: 1.5rem 2rem !important;
  min-height: 400px !important;
  max-width: none !important;
  
  /* Typography optimizations */
  font-feature-settings: "liga" 1, "kern" 1, "calt" 1 !important;
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  
  /* Better spacing for readability */
  word-spacing: 0.02em !important;
  letter-spacing: -0.008em !important;
  
  /* Smooth transitions */
  transition: all 0.2s cubic-bezier(0.16, 1, 0.3, 1) !important;
  cursor: text !important;
}

/* Enhanced focus state */
.prose-writer-canvas:focus {
  outline: none !important;
  background-color: hsl(var(--background)) !important;
  box-shadow: inset 0 0 0 1px hsl(var(--primary) / 0.2) !important;
}

/* ===== ENHANCED TYPOGRAPHY ===== */

/* Better paragraph spacing */
.prose-writer-canvas p {
  margin-top: 1em !important;
  margin-bottom: 1em !important;
  line-height: 1.7 !important;
  color: hsl(var(--foreground)) !important; /* Full opacity */
}

/* Enhanced headings for better hierarchy */
.prose-writer-canvas h1 {
  font-size: 1.875rem !important;
  font-weight: 700 !important;
  line-height: 1.2 !important;
  margin-top: 2rem !important;
  margin-bottom: 1rem !important;
  color: hsl(var(--foreground)) !important;
  letter-spacing: -0.02em !important;
}

.prose-writer-canvas h2 {
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  line-height: 1.3 !important;
  margin-top: 1.75rem !important;
  margin-bottom: 0.75rem !important;
  color: hsl(var(--foreground)) !important;
  letter-spacing: -0.015em !important;
}

.prose-writer-canvas h3 {
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  line-height: 1.4 !important;
  margin-top: 1.5rem !important;
  margin-bottom: 0.5rem !important;
  color: hsl(var(--foreground)) !important;
  letter-spacing: -0.01em !important;
}

/* ===== ENHANCED SELECTION ===== */

.prose-writer-canvas ::selection {
  background: linear-gradient(135deg, 
    hsl(var(--primary) / 0.25), 
    hsl(var(--primary) / 0.15)) !important;
  color: hsl(var(--primary-foreground)) !important;
  border-radius: 2px !important;
}

.prose-writer-canvas:focus ::selection {
  background: linear-gradient(135deg, 
    hsl(var(--primary) / 0.35), 
    hsl(var(--primary) / 0.25)) !important;
}

/* ===== IMPROVED CURSOR ===== */

.prose-writer-canvas .ProseMirror-cursor {
  border-left: 2px solid hsl(var(--primary)) !important;
  animation: canvas-blink 1.2s infinite !important;
}

@keyframes canvas-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* ===== BETTER LIST STYLING ===== */

.prose-writer-canvas ul {
  margin-top: 1em !important;
  margin-bottom: 1em !important;
  padding-left: 1.25rem !important;
}

.prose-writer-canvas ol {
  margin-top: 1em !important;
  margin-bottom: 1em !important;
  padding-left: 1.25rem !important;
}

.prose-writer-canvas li {
  margin-top: 0.25em !important;
  margin-bottom: 0.25em !important;
  line-height: 1.7 !important;
}

/* ===== ENHANCED BLOCKQUOTES ===== */

.prose-writer-canvas blockquote {
  border-left: 3px solid hsl(var(--primary) / 0.4) !important;
  margin: 1.25rem 0 !important;
  font-style: italic !important;
  color: hsl(var(--foreground) / 0.85) !important;
  background: hsl(var(--muted) / 0.3) !important;
  border-radius: 0 6px 6px 0 !important;
  padding: 0.75rem 1.25rem !important;
}

/* ===== IMPROVED CODE STYLING ===== */

.prose-writer-canvas code {
  background: hsl(var(--muted) / 0.6) !important;
  padding: 0.15em 0.3em !important;
  border-radius: 3px !important;
  font-size: 0.9em !important;
  font-family: "JetBrains Mono", "Fira Code", Consolas, monospace !important;
  color: hsl(var(--foreground) / 0.9) !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
}

.prose-writer-canvas pre {
  background: hsl(var(--muted) / 0.4) !important;
  padding: 1rem !important;
  border-radius: 6px !important;
  overflow-x: auto !important;
  margin: 1rem 0 !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
}

/* ===== PLACEHOLDER STYLING ===== */

.prose-writer-canvas .ProseMirror p.is-editor-empty:first-child::before {
  color: hsl(var(--muted-foreground) / 0.6) !important;
  content: attr(data-placeholder) !important;
  float: left !important;
  height: 0 !important;
  pointer-events: none !important;
  font-style: italic !important;
}

/* ===== RESPONSIVE IMPROVEMENTS ===== */

@media (max-width: 768px) {
  .prose-writer-canvas {
    font-size: 0.95rem !important;
    line-height: 1.6 !important;
    padding: 1rem 1.25rem !important;
  }
  
  .prose-writer-canvas h1 {
    font-size: 1.5rem !important;
    margin-top: 1.5rem !important;
    margin-bottom: 0.75rem !important;
  }
  
  .prose-writer-canvas h2 {
    font-size: 1.25rem !important;
    margin-top: 1.25rem !important;
    margin-bottom: 0.5rem !important;
  }
  
  .prose-writer-canvas h3 {
    font-size: 1.125rem !important;
    margin-top: 1rem !important;
    margin-bottom: 0.5rem !important;
  }
}

/* ===== DARK MODE ENHANCEMENTS ===== */

.dark .canvas-editor-enhanced {
  background: linear-gradient(135deg, 
    hsl(var(--background) / 0.95), 
    hsl(var(--background) / 0.9)) !important;
  border: 1px solid hsl(var(--border) / 0.4) !important;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
}

.dark .prose-writer-canvas {
  color: hsl(var(--foreground)) !important; /* Full opacity for dark mode */
}

.dark .prose-writer-canvas p {
  color: hsl(var(--foreground)) !important; /* Full opacity */
}

.dark .prose-writer-canvas blockquote {
  background: hsl(var(--muted) / 0.2) !important;
  color: hsl(var(--foreground) / 0.8) !important;
}

.dark .prose-writer-canvas code {
  background: hsl(var(--muted) / 0.4) !important;
  color: hsl(var(--foreground) / 0.9) !important;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

.prose-writer-canvas {
  will-change: contents !important;
  contain: layout style !important;
}

.prose-writer-canvas * {
  scroll-behavior: smooth !important;
}

/* ===== ENHANCED LINKS ===== */

.prose-writer-canvas a {
  color: hsl(var(--primary)) !important;
  text-decoration: underline !important;
  text-decoration-color: hsl(var(--primary) / 0.3) !important;
  text-underline-offset: 2px !important;
  transition: all 0.2s ease !important;
}

.prose-writer-canvas a:hover {
  text-decoration-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary) / 0.8) !important;
}

/* ===== ENHANCED TABLES ===== */

.prose-writer-canvas table {
  border-collapse: collapse !important;
  margin: 1rem 0 !important;
  width: 100% !important;
}

.prose-writer-canvas th,
.prose-writer-canvas td {
  border: 1px solid hsl(var(--border) / 0.3) !important;
  padding: 0.5rem !important;
  text-align: left !important;
}

.prose-writer-canvas th {
  background: hsl(var(--muted) / 0.3) !important;
  font-weight: 600 !important;
}
