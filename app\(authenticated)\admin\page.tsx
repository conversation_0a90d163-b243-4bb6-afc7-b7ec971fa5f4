import { NextPage } from 'next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';
import { File<PERSON>son, Palette } from 'lucide-react'; // Example icons

const AdminDashboardPage: NextPage = () => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Admin Dashboard</CardTitle>
          <CardDescription>
            Welcome to the Tersa Admin Panel. Manage your application's flow templates, categories, and more.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="mb-4">This is the central hub for administrative tasks. Use the sidebar to navigate to different management sections.</p>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Link href="/admin/flow-templates" className="block">
              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Flow Templates</CardTitle>
                  <FileJson className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">Manage</div>
                  <p className="text-xs text-muted-foreground">
                    Create, edit, and delete flow templates.
                  </p>
                </CardContent>
              </Card>
            </Link>
            <Link href="/admin/categories" className="block">
              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Categories</CardTitle>
                  <Palette className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">Organize</div>
                  <p className="text-xs text-muted-foreground">
                    Manage categories for flow templates.
                  </p>
                </CardContent>
              </Card>
            </Link>
            {/* Add more quick access cards as needed */}
          </div>
        </CardContent>
      </Card>
      {/* You can add more sections or stats to the dashboard here */}
    </div>
  );
};

export default AdminDashboardPage;