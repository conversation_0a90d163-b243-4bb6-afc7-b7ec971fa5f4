'use server';

import { db } from '@/lib/db';
import { categories } from '@/schema';
import { revalidatePath } from 'next/cache';
import { z } from 'zod';
import { auth } from '@clerk/nextjs/server';

const CreateCategorySchema = z.object({
  name: z.string().min(1, { message: 'Category name is required' }),
  description: z.string().optional(),
});

export async function createCategoryAction(formData: FormData) {
  const { userId } = auth();
  if (!userId) {
    return {
      success: false,
      error: 'Unauthorized: You must be logged in to create a category.',
    };
  }

  // TODO: Add role-based access control to ensure only admins can create categories
  // For now, any authenticated user can create a category.

  const validatedFields = CreateCategorySchema.safeParse({
    name: formData.get('name'),
    description: formData.get('description'),
  });

  if (!validatedFields.success) {
    return {
      success: false,
      error: 'Invalid fields for category creation.',
      errors: validatedFields.error.flatten().fieldErrors,
    };
  }

  const { name, description } = validatedFields.data;

  try {
    const [newCategory] = await db
      .insert(categories)
      .values({
        name,
        description,
      })
      .returning();

    revalidatePath('/admin/flow-templates'); // Or a more specific path if available
    revalidatePath('/flows'); // Revalidate public flow gallery if it uses categories

    return {
      success: true,
      data: newCategory,
      message: 'Category created successfully.',
    };
  } catch (error) {
    console.error('Error creating category:', error);
    if (error instanceof Error && error.message.includes('duplicate key value violates unique constraint')) {
        return {
            success: false,
            error: 'A category with this name already exists.',
        };
    }
    return {
      success: false,
      error: 'Failed to create category. Please try again.',
    };
  }
}