import { vercel } from '@t3-oss/env-core/presets-zod';
import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

// Create environment validation that requires real API keys
const optionalString = z.string().optional();
const mockUrl = z.string().optional().default('https://example.com');

export const env = createEnv({
  extends: [vercel()],
  server: {
    DATABASE_URL: mockUrl,

    UPSTASH_REDIS_REST_URL: mockUrl,
    UPSTASH_REDIS_REST_TOKEN: optionalString,

    RESEND_TOKEN: optionalString,
    RESEND_EMAIL: z.string().optional().default('<EMAIL>'),

    STRIPE_SECRET_KEY: optionalString,
    STRIPE_HOBBY_PRODUCT_ID: z.string().optional().default('prod_mock_hobby'),
    STRIPE_PRO_PRODUCT_ID: z.string().optional().default('prod_mock_pro'),
    STRIPE_USAGE_PRODUCT_ID: z.string().optional().default('prod_mock_usage'),
    STRIPE_CREDITS_METER_ID: optionalString,
    STRIPE_CREDITS_METER_NAME: optionalString,
    STRIPE_WEBHOOK_SECRET: optionalString,

    SUPABASE_SERVICE_ROLE_KEY: optionalString,
    SUPABASE_AUTH_HOOK_SECRET: optionalString,

    // AI SDK - All API keys should be validated if provided
    OPENAI_API_KEY: z.string().min(1),
    GOOGLE_GENERATIVE_AI_API_KEY: z.string().min(1),
    GROQ_API_KEY: z.string().min(1),
    DEEPSEEK_API_KEY: optionalString,
    ANTHROPIC_API_KEY: optionalString,
    XAI_API_KEY: optionalString,
    AWS_ACCESS_KEY_ID: optionalString,
    AWS_SECRET_ACCESS_KEY: optionalString,
    AWS_REGION: optionalString,
    FAL_API_KEY: optionalString,
    TOGETHER_AI_API_KEY: optionalString,

    // Other Models
    MINIMAX_GROUP_ID: optionalString,
    MINIMAX_API_KEY: optionalString,
    RUNWAYML_API_SECRET: optionalString,
    LUMAAI_API_KEY: optionalString,
  },
  client: {
    NEXT_PUBLIC_SUPABASE_URL: mockUrl,
    NEXT_PUBLIC_SUPABASE_ANON_KEY: optionalString,
    NEXT_PUBLIC_TURNSTILE_SITE_KEY: optionalString,
    NEXT_PUBLIC_POSTHOG_KEY: optionalString,
    NEXT_PUBLIC_POSTHOG_HOST: mockUrl,
  },
  runtimeEnv: {
    OPENAI_API_KEY: process.env.OPENAI_API_KEY,
    GOOGLE_GENERATIVE_AI_API_KEY: process.env.GOOGLE_GENERATIVE_AI_API_KEY,
    GROQ_API_KEY: process.env.GROQ_API_KEY,
    DEEPSEEK_API_KEY: process.env.DEEPSEEK_API_KEY,
    ANTHROPIC_API_KEY: process.env.ANTHROPIC_API_KEY,
    XAI_API_KEY: process.env.XAI_API_KEY,
    AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,
    AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,
    AWS_REGION: process.env.AWS_REGION || 'us-west-2',
    FAL_API_KEY: process.env.FAL_API_KEY,
    TOGETHER_AI_API_KEY: process.env.TOGETHER_AI_API_KEY || 'dummy-key',
    DATABASE_URL:
      process.env.DATABASE_URL || 'postgres://dummy:dummy@localhost:5432/dummy',
    NEXT_PUBLIC_SUPABASE_URL:
      process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://example.supabase.co',
    NEXT_PUBLIC_SUPABASE_ANON_KEY:
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'dummy-key',
    NEXT_PUBLIC_TURNSTILE_SITE_KEY:
      process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY || 'dummy-key',
    MINIMAX_GROUP_ID: process.env.MINIMAX_GROUP_ID || 'dummy-key',
    MINIMAX_API_KEY: process.env.MINIMAX_API_KEY || 'dummy-key',
    UPSTASH_REDIS_REST_URL:
      process.env.UPSTASH_REDIS_REST_URL || 'https://dummy.upstash.io',
    UPSTASH_REDIS_REST_TOKEN:
      process.env.UPSTASH_REDIS_REST_TOKEN || 'dummy-key',
    RESEND_TOKEN: process.env.RESEND_TOKEN || 'dummy-key',
    RESEND_EMAIL: process.env.RESEND_EMAIL || '<EMAIL>',
    STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY || 'sk_test_dummy',
    STRIPE_HOBBY_PRODUCT_ID:
      process.env.STRIPE_HOBBY_PRODUCT_ID || 'prod_mock_hobby',
    STRIPE_PRO_PRODUCT_ID: process.env.STRIPE_PRO_PRODUCT_ID || 'prod_mock_pro',
    STRIPE_USAGE_PRODUCT_ID:
      process.env.STRIPE_USAGE_PRODUCT_ID || 'prod_mock_usage',
    STRIPE_CREDITS_METER_ID:
      process.env.STRIPE_CREDITS_METER_ID || 'dummy-meter-id',
    STRIPE_CREDITS_METER_NAME:
      process.env.STRIPE_CREDITS_METER_NAME || 'dummy-meter',
    STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET || 'whsec_dummy',
    SUPABASE_SERVICE_ROLE_KEY:
      process.env.SUPABASE_SERVICE_ROLE_KEY || 'dummy-key',
    SUPABASE_AUTH_HOOK_SECRET:
      process.env.SUPABASE_AUTH_HOOK_SECRET || 'v1,whsec_dummy',
    RUNWAYML_API_SECRET: process.env.RUNWAYML_API_SECRET || 'dummy-key',
    LUMAAI_API_KEY: process.env.LUMAAI_API_KEY || 'dummy-key',
    NEXT_PUBLIC_POSTHOG_KEY: process.env.NEXT_PUBLIC_POSTHOG_KEY || 'dummy-key',
    NEXT_PUBLIC_POSTHOG_HOST:
      process.env.NEXT_PUBLIC_POSTHOG_HOST || 'https://app.posthog.com',
  },
});
