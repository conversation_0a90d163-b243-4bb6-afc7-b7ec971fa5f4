'use client';

import { useEffect, useRef } from 'react';

/**
 * A safe HTML preview component that uses a ref to update content
 * instead of dangerouslySetInnerHTML
 */
export function SafeHTMLPreview({
  content,
  className,
}: { content: string; className?: string }) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.innerHTML = content;
    }
  }, [content]);

  return <div ref={containerRef} className={className} />;
}
