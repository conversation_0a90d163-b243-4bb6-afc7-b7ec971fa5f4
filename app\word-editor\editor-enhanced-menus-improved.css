/* Enhanced floating menu styling */
.floating-menu {
  border-radius: 0.75rem !important;
  background-color: hsl(var(--background) / 0.85) !important;
  backdrop-filter: blur(16px) !important;
  border: 1px solid hsl(var(--border)) !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05) !important;
  padding: 0.5rem !important;
  animation: floatingMenuIn 0.25s cubic-bezier(0.16, 1, 0.3, 1) forwards !important;
  transform-origin: top center !important;
  max-width: 280px !important;
}

.dark .floating-menu {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25), 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  background-color: hsl(var(--card) / 0.85) !important;
}

@keyframes floatingMenuIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.97);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.floating-menu [class*="flex cursor-pointer"] {
  border-radius: 0.5rem !important;
  padding: 0.6rem 0.7rem !important;
  margin-bottom: 2px !important;
  transition: all 0.2s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.floating-menu [class*="flex cursor-pointer"]:hover {
  background-color: hsl(var(--accent) / 0.15) !important;
  transform: translateX(2px);
}

.floating-menu [class*="flex cursor-pointer"]:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background-color: hsl(var(--primary));
  opacity: 0;
  transition: opacity 0.2s ease;
}

.floating-menu [class*="flex cursor-pointer"][class*="bg-primary/10"]:before {
  opacity: 1;
}

.floating-menu [class*="flex cursor-pointer"] [class*="flex h-5 w-5"] {
  background-color: hsl(var(--accent) / 0.1) !important;
  border-radius: 0.375rem !important;
  padding: 0.25rem !important;
  transition: all 0.2s ease !important;
}

.floating-menu [class*="flex cursor-pointer"]:hover [class*="flex h-5 w-5"] {
  background-color: hsl(var(--accent) / 0.2) !important;
  transform: scale(1.05);
}

.floating-menu [class*="flex cursor-pointer"] > span {
  font-size: 0.875rem !important;
  font-weight: 500 !important;
}

/* Enhanced table menu styling */
.enhanced-table-menu {
  border-radius: 0.75rem !important;
  background-color: hsl(var(--background) / 0.9) !important;
  backdrop-filter: blur(16px) !important;
  border: 1px solid hsl(var(--border)) !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05) !important;
  padding: 0.5rem 0.75rem !important;
  animation: tableMenuIn 0.2s cubic-bezier(0.16, 1, 0.3, 1) forwards !important;
}

.dark .enhanced-table-menu {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25), 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  background-color: hsl(var(--card) / 0.9) !important;
}

@keyframes tableMenuIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
