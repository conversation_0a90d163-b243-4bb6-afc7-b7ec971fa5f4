/* Header and layout fixes for the editor */

/* Fixed header styles */
.editor-header-container {
  height: var(--header-height, 60px);
  box-sizing: border-box;
  z-index: 100;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.98),
    rgba(255, 255, 255, 0.95)
  );
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(0, 0, 0, 0.03);
  /* Make sure the header stays fixed */
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  display: flex;
  align-items: center;
  padding: 0 1rem;
}

.dark .editor-header-container {
  background: linear-gradient(
    to bottom,
    rgba(15, 23, 42, 0.95),
    rgba(15, 23, 42, 0.9)
  );
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
}

/* Add proper padding to main content to prevent header overlap */
.editor-main-container {
  padding-top: calc(var(--header-height, 60px) + 20px) !important;
  min-height: 100vh;
  position: relative;
  z-index: 1; /* Ensure main content is below header */
}

/* Card container fix to add space at the top */
.editor-card {
  margin-top: 1.25rem !important;
  border-radius: 12px;
  overflow: hidden;
}

/* Fix for editor content to ensure proper spacing from top */
.editor-content {
  padding-top: 0.75rem !important;
}

/* Ensure first elements in the editor have proper spacing */
.ProseMirror > h1:first-child,
.ProseMirror > h2:first-child,
.ProseMirror > h3:first-child,
.ProseMirror > p:first-child {
  margin-top: 0 !important;
  padding-top: 0.5rem !important;
}

/* Custom scrollbar for editor */
.editor-paper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.editor-paper::-webkit-scrollbar-track {
  background: transparent;
}

.editor-paper::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  border: 2px solid transparent;
}

.dark .editor-paper::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.1);
}

.editor-paper::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.2);
}

.dark .editor-paper::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* TabsContent padding adjustments */
.editor-tabs + [role="tabpanel"] {
  padding-top: 1rem !important;
}
