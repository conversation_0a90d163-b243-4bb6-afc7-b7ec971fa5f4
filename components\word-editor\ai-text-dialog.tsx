'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import type { Editor } from '@tiptap/core';
import { FileText, MessageSquare, Sparkles, Wand2 } from 'lucide-react';
import { useState } from 'react';

interface AITextDialogProps {
  open: boolean;
  onClose: () => void;
  editor: Editor | null;
}

type AIAction = 'generate' | 'improve' | 'summarize' | 'chat';

export function AITextDialog({ open, onClose, editor }: AITextDialogProps) {
  const [activeTab, setActiveTab] = useState<AIAction>('generate');
  const [prompt, setPrompt] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [aiResult, setAIResult] = useState('');

  // Mock AI processing function
  const processWithAI = async () => {
    if (!prompt.trim() || !editor) return;

    setIsProcessing(true);

    // Simulate AI processing with a delay
    await new Promise((resolve) => setTimeout(resolve, 1500));

    // Mock responses based on the action type
    let result = '';

    switch (activeTab) {
      case 'generate':
        result = `Here's some generated content based on your prompt: "${prompt}"\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisi vel consectetur euismod, nisi nisl consectetur nisi, vel consectetur nisi nisl consectetur nisi.`;
        break;
      case 'improve':
        result = `I've improved your text:\n\n${editor.getSelectedText() || 'Your selected text'}\n\nEnhanced version:\n\nThe text has been refined to be more engaging and clear, with improved sentence structure and vocabulary.`;
        break;
      case 'summarize':
        result = `Summary of your content:\n\nThis text discusses key points about the topic with insightful analysis of the main themes.`;
        break;
      case 'chat':
        result = `AI Assistant: Thanks for your question about "${prompt}". Here's what I found:\n\nThis is a detailed answer that provides information relevant to your query with supporting facts and references.`;
        break;
    }

    setAIResult(result);
    setIsProcessing(false);
  };

  const insertContentToEditor = () => {
    if (!editor || !aiResult) return;

    editor.chain().focus().insertContent(aiResult).run();
    resetAndClose();
  };

  const resetAndClose = () => {
    setPrompt('');
    setAIResult('');
    setActiveTab('generate');
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-primary" />
            AI Text Assistant
          </DialogTitle>
          <DialogDescription>
            Use AI to enhance your writing with different tools
          </DialogDescription>
        </DialogHeader>

        <Tabs
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as AIAction)}
          className="w-full"
        >
          <TabsList className="mb-4 grid grid-cols-4">
            <TabsTrigger value="generate" className="flex items-center gap-2">
              <Wand2 className="h-4 w-4" />
              <span className="hidden sm:inline">Generate</span>
            </TabsTrigger>
            <TabsTrigger value="improve" className="flex items-center gap-2">
              <Sparkles className="h-4 w-4" />
              <span className="hidden sm:inline">Improve</span>
            </TabsTrigger>
            <TabsTrigger value="summarize" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              <span className="hidden sm:inline">Summarize</span>
            </TabsTrigger>
            <TabsTrigger value="chat" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              <span className="hidden sm:inline">Chat</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="generate">
            <p className="mb-2 text-muted-foreground text-sm">
              Generate new content based on your prompt
            </p>
          </TabsContent>

          <TabsContent value="improve">
            <p className="mb-2 text-muted-foreground text-sm">
              Enhance selected text with better wording and structure
            </p>
          </TabsContent>

          <TabsContent value="summarize">
            <p className="mb-2 text-muted-foreground text-sm">
              Create a concise summary of your document or selection
            </p>
          </TabsContent>

          <TabsContent value="chat">
            <p className="mb-2 text-muted-foreground text-sm">
              Ask questions about your document or get writing assistance
            </p>
          </TabsContent>
        </Tabs>

        <div className="mt-2 flex flex-col gap-4">
          <Textarea
            placeholder={`Enter your ${
              activeTab === 'generate'
                ? 'prompt for new content'
                : activeTab === 'improve'
                  ? 'instructions for improvement'
                  : activeTab === 'summarize'
                    ? 'summarization preferences'
                    : 'question or request'
            }`}
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            rows={3}
            className="resize-none"
            disabled={isProcessing}
          />

          {aiResult && (
            <div className="max-h-[200px] overflow-y-auto rounded-md border bg-muted/30 p-3">
              <pre className="whitespace-pre-wrap text-sm">{aiResult}</pre>
            </div>
          )}
        </div>

        <DialogFooter className="flex items-center justify-between">
          <Button variant="outline" onClick={resetAndClose}>
            Cancel
          </Button>

          <div className="flex gap-2">
            {aiResult ? (
              <Button
                onClick={insertContentToEditor}
                className="flex items-center gap-1"
              >
                <FileText className="h-4 w-4" />
                Insert to Document
              </Button>
            ) : (
              <Button
                onClick={processWithAI}
                disabled={!prompt.trim() || isProcessing}
                className="flex items-center gap-1"
              >
                {isProcessing ? (
                  <>
                    <span className="mr-1 animate-spin">⏳</span>
                    Processing...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4" />
                    Process with AI
                  </>
                )}
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
