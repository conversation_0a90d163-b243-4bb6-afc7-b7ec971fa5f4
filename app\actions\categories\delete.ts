'use server';

import { db } from '@/lib/db';
import { categories, flow_templates } from '@/schema'; // Ensure flow_templates is imported if you handle related templates
import { revalidatePath } from 'next/cache';
import { z } from 'zod';
import { auth } from '@clerk/nextjs/server';
import { eq } from 'drizzle-orm';

const DeleteCategorySchema = z.object({
  id: z.string().min(1, { message: 'Category ID is required' }),
});

export async function deleteCategoryAction(formData: FormData) {
  const { userId } = auth();
  if (!userId) {
    return {
      success: false,
      error: 'Unauthorized: You must be logged in to delete a category.',
    };
  }

  // TODO: Add role-based access control to ensure only admins can delete categories

  const validatedFields = DeleteCategorySchema.safeParse({
    id: formData.get('id'),
  });

  if (!validatedFields.success) {
    return {
      success: false,
      error: 'Invalid category ID for deletion.',
      errors: validatedFields.error.flatten().fieldErrors,
    };
  }

  const { id } = validatedFields.data;

  try {
    // Optional: Decide how to handle flow_templates associated with this category.
    // Option 1: Set category_id to null (as defined in schema `onDelete: 'set null'`)
    // Option 2: Delete flow_templates (would require a transaction and careful consideration)
    // Option 3: Prevent deletion if category is in use (check if any flow_templates reference it)

    // For now, relying on the schema's `onDelete: 'set null'` behavior.

    const [deletedCategory] = await db
      .delete(categories)
      .where(eq(categories.id, id))
      .returning();

    if (!deletedCategory) {
      return {
        success: false,
        error: 'Category not found.',
      };
    }

    revalidatePath('/admin/flow-templates');
    revalidatePath('/flows');
    // Potentially revalidate other paths where categories might be displayed

    return {
      success: true,
      message: 'Category deleted successfully.',
    };
  } catch (error) {
    console.error('Error deleting category:', error);
    return {
      success: false,
      error: 'Failed to delete category. Please try again.',
    };
  }
}