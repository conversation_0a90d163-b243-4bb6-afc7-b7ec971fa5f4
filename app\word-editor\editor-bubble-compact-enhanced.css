/*
   Enhanced CSS to make the editor bubble menu more attractive and functional
   Modern glass-morphism design with improved accessibility and animations
*/

/* More visually appealing bubble menu with modern glass-morphism styling */
.bubble-menu {
  padding: 0.5rem !important;
  border-radius: 1rem !important;
  backdrop-filter: blur(20px) saturate(180%);
  background-color: hsl(var(--background) / 0.8) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  border: 1px solid hsl(var(--border) / 0.6) !important;
  animation: bubbleIn 0.3s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  transform-origin: bottom center;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  position: relative;
  z-index: 1000;
}

.dark .bubble-menu {
  background-color: hsl(var(--background) / 0.9) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(0, 0, 0, 0.3), inset
    0 1px 0 rgba(255, 255, 255, 0.05) !important;
  border: 1px solid hsl(var(--border) / 0.8) !important;
}

@keyframes bubbleIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(8px);
    filter: blur(4px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
    filter: blur(0);
  }
}

/* Enhanced buttons in the bubble menu with modern styling */
.bubble-menu button {
  padding: 0.5rem 0.75rem !important;
  gap: 0.5rem !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  height: 36px !important;
  border-radius: 0.75rem !important;
  transition: all 0.2s cubic-bezier(0.16, 1, 0.3, 1) !important;
  position: relative;
  overflow: hidden;
  background-color: transparent !important;
  color: hsl(var(--foreground) / 0.9) !important;
  border: 1px solid transparent !important;
  backdrop-filter: blur(8px);
}

/* Enhanced hover effect for buttons */
.bubble-menu button:hover {
  background-color: hsl(var(--accent) / 0.15) !important;
  border-color: hsl(var(--accent) / 0.3) !important;
  transform: translateY(-1px) scale(1.02);
  color: hsl(var(--accent-foreground)) !important;
  box-shadow: 0 4px 12px hsl(var(--accent) / 0.2), 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Enhanced active state for buttons */
.bubble-menu button[data-state="on"],
.bubble-menu button.active {
  background-color: hsl(var(--accent)) !important;
  border-color: hsl(var(--accent)) !important;
  color: hsl(var(--accent-foreground)) !important;
  box-shadow: 0 4px 12px hsl(var(--accent) / 0.4), 0 2px 4px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* Icons in the bubble menu buttons */
.bubble-menu button svg {
  width: 16px !important;
  height: 16px !important;
  transition: transform 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  flex-shrink: 0;
}

/* Refined icon animation on hover */
.bubble-menu button:hover svg {
  transform: scale(1.1);
}

.bubble-menu button[data-state="on"] svg,
.bubble-menu button.active svg {
  transform: scale(1.05);
}

/* Enhanced separator styling */
.bubble-menu .h-4.w-px.bg-border,
.bubble-menu [role="separator"] {
  height: 24px !important;
  width: 1px !important;
  opacity: 0.4;
  margin: 0 6px !important;
  background: linear-gradient(
    to bottom,
    transparent,
    hsl(var(--border) / 0.8),
    transparent
  ) !important;
  border-radius: 1px;
}

/* Enhanced tooltips for the bubble menu */
.bubble-menu [data-state="delayed-open"][data-side="top"] {
  animation: tooltipIn 0.25s cubic-bezier(0.16, 1, 0.3, 1);
  background-color: hsl(var(--popover)) !important;
  color: hsl(var(--popover-foreground)) !important;
  border-radius: 0.5rem !important;
  padding: 0.375rem 0.75rem !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid hsl(var(--border) / 0.5) !important;
}

@keyframes tooltipIn {
  from {
    opacity: 0;
    transform: translateY(4px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Focus styles for accessibility */
.bubble-menu button:focus-visible {
  outline: 2px solid hsl(var(--primary)) !important;
  outline-offset: 2px !important;
  background-color: hsl(var(--accent) / 0.1) !important;
}

/* Improved responsive behavior */
@media (max-width: 640px) {
  .bubble-menu {
    padding: 0.375rem !important;
    border-radius: 0.75rem !important;
  }

  .bubble-menu button {
    padding: 0.375rem 0.5rem !important;
    height: 32px !important;
    font-size: 0.8rem !important;
  }

  .bubble-menu button svg {
    width: 14px !important;
    height: 14px !important;
  }
}
