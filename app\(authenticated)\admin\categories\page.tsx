import { NextPage } from 'next';
import { getCategoriesAction } from '@/app/actions/categories';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { PlusCircle, Edit, Trash2 } from 'lucide-react';
import { CategoryClientPage } from './_components/category-client-page'; // Client component for interactions

// Re-exporting for potential use in other server components if needed, or direct import
export { getCategoriesAction }; 

const AdminCategoriesPage: NextPage = async () => {
  const result = await getCategoriesAction();

  if (!result.success || !result.data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Manage Categories</CardTitle>
          <CardDescription>Error loading categories.</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-destructive">{result.error || 'Could not fetch categories. Please try again.'}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <CategoryClientPage initialCategories={result.data} />
  );
};

export default AdminCategoriesPage;