import { PostHogIdentifyProvider } from '@/providers/posthog-provider';
import type { ReactNode } from 'react';

type ProjectsLayoutProps = {
  children: ReactNode;
};

const ProjectsLayout = async ({ children }: ProjectsLayoutProps) => {
  // Skip authentication check and always render children
  return <PostHogIdentifyProvider>{children}</PostHogIdentifyProvider>;

  // Original implementation:
  // const supabase = await createClient();
  // const { data, error } = await supabase.auth.getUser();

  // if (error || !data?.user) {
  //   redirect('/auth/login');
  // }

  // return <PostHogIdentifyProvider>{children}</PostHogIdentifyProvider>;
};

export default ProjectsLayout;
