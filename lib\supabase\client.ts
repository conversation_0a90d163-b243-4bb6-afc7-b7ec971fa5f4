import { createBrowserClient } from '@supabase/ssr';
import { env } from '../env';

// Mock Supabase client for browser (development)
const mockSupabaseClient = {
  auth: {
    getUser: async () => ({
      data: {
        user: {
          id: 'mock-user-id',
          email: '<EMAIL>',
          app_metadata: {},
          user_metadata: {},
          created_at: new Date().toISOString(),
        },
      },
      error: null,
    }),
    signInWithPassword: async () => ({
      data: {
        user: {
          id: 'mock-user-id',
          email: '<EMAIL>',
        },
        session: {
          access_token: 'mock-access-token',
        },
      },
      error: null,
    }),
    signUp: async () => ({
      data: {
        user: {
          id: 'mock-user-id',
          email: '<EMAIL>',
        },
        session: null,
      },
      error: null,
    }),
    resetPasswordForEmail: async () => ({ error: null }),
    updateUser: async () => ({ error: null }),
    signInWithOAuth: async () => ({ error: null }),
  },
};

export function createClient() {
  // Use mock client in development
  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return mockSupabaseClient as unknown as ReturnType<
      typeof createBrowserClient
    >;
  }
  // Use correct arguments for createBrowserClient
  return createBrowserClient(
    env.NEXT_PUBLIC_SUPABASE_URL,
    env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    {
      // Optionally, add cookie options or other config if needed
    }
  );
}
