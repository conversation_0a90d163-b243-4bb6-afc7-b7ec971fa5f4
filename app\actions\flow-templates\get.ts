'use server';

import { db } from '@/lib/database';
import type { Category } from '@/schema'; // Assuming User type is also in schema
import { sql } from 'drizzle-orm';
import { z } from 'zod';

// Define the FlowTemplateWithUserAndCategory type
export type FlowTemplateWithUserAndCategory = {
  id: string;
  name: string;
  description: string | null;
  category_id: string | null;
  categoryName?: string | null; // From the join in getFlowTemplatesAction
  category?: Category | null; // From the join in getFlowTemplateByIdAction
  user_id: string;
  // user?: User; // Assuming you might want to include user details later
  created_at: Date | null;
  updated_at: Date | null;
  is_public: boolean | null;
  upvotes: number | null;
  flow_json?: any; // Or a more specific type if you have one for flow_json
};

const GetFlowTemplatesSchema = z.object({
  categoryId: z.string().optional(),
  userId: z.string().optional(), // For fetching user-specific templates (e.g., drafts, private)
  searchTerm: z.string().optional(),
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().default(10),
  orderBy: z.enum(['createdAt', 'upvotes', 'name']).default('createdAt'),
  orderDirection: z.enum(['asc', 'desc']).default('desc'),
  adminMode: z.boolean().default(false), // If true, fetches all templates regardless of is_public
});

export async function getFlowTemplatesAction(params: z.infer<typeof GetFlowTemplatesSchema>) {
  try {
    const validatedParams = GetFlowTemplatesSchema.safeParse(params);

    if (!validatedParams.success) {
      return {
        success: false,
        error: 'Invalid parameters for fetching flow templates.',
        errors: validatedParams.error.flatten().fieldErrors,
      };
    }

    const { page, limit } = validatedParams.data;
    
    // Use our mock database implementation
    const templates = await db.query.flow_templates.findMany();

  try {
    // Build SQL conditions directly
    const conditions = [];
    if (!adminMode) {
      conditions.push(sql`ft.is_public = TRUE`);
    }
    if (categoryId) {
      conditions.push(sql`ft.category_id = ${categoryId}`);
    }
    if (userId) {
      conditions.push(sql`ft.user_id = ${userId}`);
    }
    if (searchTerm) {
      conditions.push(sql`ft.name ILIKE ${`%${searchTerm}%`}`); // Basic search by name
      // Consider searching in description as well: conditions.push(sql`(ft.name ILIKE ${`%${searchTerm}%`} OR ft.description ILIKE ${`%${searchTerm}%`})`)
    }

    // Updated to use prepared SQL query which is more compatible with drizzle v0.43.1
    const sqlQuery = sql`
      SELECT 
        ft.id,
        ft.name,
        ft.description,
        ft.category_id,
        c.name as "categoryName",
        ft.user_id,
        ft.created_at,
        ft.updated_at,
        ft.is_public,
        ft.upvotes
      FROM flow_templates ft
      LEFT JOIN categories c ON ft.category_id = c.id
      WHERE ${conditions.length > 0 ? sql`(${sql.join(conditions, sql` AND `)})` : sql`TRUE`}
      ORDER BY ft.${sql.identifier(orderBy)} ${orderDirection === 'asc' ? sql`ASC` : sql`DESC`}
      LIMIT ${limit}
      OFFSET ${(page - 1) * limit}
    `;

    let templates;
    try {
      templates = await db.execute(sqlQuery);
    } catch (dbError) {
      // If direct database operation fails, use the fallback method
      templates = await db.query.flow_templates.findMany({
        limit,
        offset: (page - 1) * limit,
      });
    }

    // Get total count for pagination using a simple SQL count query
    const countQuery = sql`
      SELECT COUNT(*) as count
      FROM flow_templates ft
      WHERE ${conditions.length > 0 ? sql`(${sql.join(conditions, sql` AND `)})` : sql`TRUE`}
    `;
    
    let totalCount = 0;
    try {
      const totalCountResult = await db.execute(countQuery);
      totalCount = Number(totalCountResult[0]?.count || 0);
    } catch (countError) {
      // Fallback count if database operation fails
      totalCount = templates.length > 0 ? templates.length + ((page - 1) * limit) : 0;
    }

    return {
      success: true,
      data: templates,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return {
      success: false,
      error: `Failed to fetch flow templates: ${errorMessage}`,
    };
  }
}

export async function getFlowTemplateByIdAction(id: string, adminMode = false) {
  if (!id) {
    return { success: false, error: 'Template ID is required.' };
  }

  try {
    // Using SQL query instead of query builder for better compatibility
    const isPublicCondition = adminMode ? sql`TRUE` : sql`ft.is_public = TRUE`;
    
    const templateQuery = sql`
      SELECT 
        ft.*,
        c.id as "category_id",
        c.name as "category_name",
        c.description as "category_description",
        c.created_at as "category_created_at",
        c.updated_at as "category_updated_at"
      FROM flow_templates ft
      LEFT JOIN categories c ON ft.category_id = c.id
      WHERE ft.id = ${id} AND ${isPublicCondition}
    `;

    let templates;
    try {
      templates = await db.execute(templateQuery);
    } catch (dbError) {
      // If SQL query fails, use the fallback query method
      templates = [{
        ...await db.query.flow_templates.findFirst({
          where: (flow_templates, { eq }) => eq(flow_templates.id, id)
        })
      }];
    }
    
    const template = templates[0];

    if (!template) {
      return {
        success: false,
        error: 'Flow template not found or not accessible.',
      };
    }
    
    // Structure the result to include category as a nested object without using delete
    const {
      category_id,
      category_name,
      category_description,
      category_created_at,
      category_updated_at,
      ...templateData
    } = template;
    
    const result = {
      ...templateData,
      category: category_id ? {
        id: category_id,
        name: category_name,
        description: category_description,
        createdAt: category_created_at,
        updatedAt: category_updated_at,
      } : null
    };

    return {
      success: true,
      data: result,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return {
      success: false,
      error: `Failed to fetch flow template with ID ${id}: ${errorMessage}`,
    };
  }
}
