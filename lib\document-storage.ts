// Tersa Word Editor localStorage utilities

// Types for localStorage document storage
export interface StoredDocument {
  id: string;
  title: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  wordCount: number;
  charCount: number;
}

// Constants
const STORAGE_KEY_PREFIX = 'tersa_document_';
const RECENT_DOCS_KEY = 'tersa_recent_documents';
const MAX_RECENT_DOCS = 10;

// Utility to generate a unique ID
export const generateDocumentId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

// Save a document to localStorage
export const saveDocumentToStorage = (document: StoredDocument): void => {
  try {
    // Save the document data
    localStorage.setItem(
      `${STORAGE_KEY_PREFIX}${document.id}`,
      JSON.stringify(document)
    );

    // Update recent documents list
    const recentDocs = getRecentDocuments();

    // Check if document already exists in the list
    const existingIndex = recentDocs.findIndex((doc) => doc.id === document.id);

    if (existingIndex !== -1) {
      // Update existing document in list
      recentDocs[existingIndex] = {
        id: document.id,
        title: document.title,
        updatedAt: document.updatedAt,
      };
    } else {
      // Add new document to list
      recentDocs.unshift({
        id: document.id,
        title: document.title,
        updatedAt: document.updatedAt,
      });

      // Keep only the most recent documents
      if (recentDocs.length > MAX_RECENT_DOCS) {
        recentDocs.pop();
      }
    }

    // Save updated recent documents list
    localStorage.setItem(RECENT_DOCS_KEY, JSON.stringify(recentDocs));
  } catch (error) {
    console.error('Error saving document to storage:', error);
  }
};

// Get a document from localStorage
export const getDocumentFromStorage = (id: string): StoredDocument | null => {
  try {
    const docString = localStorage.getItem(`${STORAGE_KEY_PREFIX}${id}`);
    if (!docString) return null;
    return JSON.parse(docString);
  } catch (error) {
    console.error('Error getting document from storage:', error);
    return null;
  }
};

// Get recent documents list
export const getRecentDocuments = (): {
  id: string;
  title: string;
  updatedAt: string;
}[] => {
  try {
    const recentDocsString = localStorage.getItem(RECENT_DOCS_KEY);
    if (!recentDocsString) return [];
    return JSON.parse(recentDocsString);
  } catch (error) {
    console.error('Error getting recent documents:', error);
    return [];
  }
};

// Delete a document from localStorage
export const deleteDocumentFromStorage = (id: string): void => {
  try {
    // Remove the document data
    localStorage.removeItem(`${STORAGE_KEY_PREFIX}${id}`);

    // Update recent documents list
    const recentDocs = getRecentDocuments();
    const updatedDocs = recentDocs.filter((doc) => doc.id !== id);
    localStorage.setItem(RECENT_DOCS_KEY, JSON.stringify(updatedDocs));
  } catch (error) {
    console.error('Error deleting document from storage:', error);
  }
};

// Clear all documents from localStorage
export const clearAllDocuments = (): void => {
  try {
    // Get all keys from localStorage
    const keys = Object.keys(localStorage);

    // Remove all document entries
    keys.forEach((key) => {
      if (key.startsWith(STORAGE_KEY_PREFIX) || key === RECENT_DOCS_KEY) {
        localStorage.removeItem(key);
      }
    });
  } catch (error) {
    console.error('Error clearing documents from storage:', error);
  }
};

// Get all documents from localStorage
export const getAllDocuments = (): StoredDocument[] => {
  try {
    const documents: StoredDocument[] = [];
    const keys = Object.keys(localStorage);

    keys.forEach((key) => {
      if (key.startsWith(STORAGE_KEY_PREFIX)) {
        const docString = localStorage.getItem(key);
        if (docString) {
          try {
            const doc = JSON.parse(docString);
            documents.push(doc);
          } catch (e) {
            console.error(`Error parsing document ${key}:`, e);
          }
        }
      }
    });

    // Sort by updated date (newest first)
    return documents.sort(
      (a, b) =>
        new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
    );
  } catch (error) {
    console.error('Error getting all documents:', error);
    return [];
  }
};
