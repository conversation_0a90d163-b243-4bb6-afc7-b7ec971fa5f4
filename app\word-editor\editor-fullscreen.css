.editor-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background-color: rgba(255, 255, 255, 0.98);
  padding: 2rem;
  overflow-y: auto;
  backdrop-filter: blur(15px);
  animation: fadeInFullscreen 0.3s ease-out;
}

@keyframes fadeInFullscreen {
  from {
    opacity: 0;
    transform: scale(0.98);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.dark .editor-fullscreen {
  background-color: rgba(15, 23, 42, 0.98); /* dark background with opacity */
  color: white;
}

.editor-fullscreen .card-container {
  max-width: 900px !important;
  margin: 0 auto !important;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;
  border-radius: 16px !important;
  overflow: hidden !important;
  animation: floatIn 0.5s ease-out forwards;
  border: 1px solid rgba(0, 0, 0, 0.05) !important;
}

.dark .editor-fullscreen .card-container {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3) !important;
  border: 1px solid rgba(255, 255, 255, 0.05) !important;
}

@keyframes floatIn {
  0% {
    transform: translateY(10px);
    opacity: 0.8;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.editor-content {
  max-width: 900px;
  margin: 0 auto;
  padding: 2.5rem;
}

/* Styles for editor components in fullscreen mode */
.editor-fullscreen .ProseMirror {
  min-height: 75vh;
  outline: none;
  font-size: 1.15rem;
  line-height: 1.8;
}

/* Hide distracting elements in fullscreen */
.editor-fullscreen .editor-footer {
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.editor-fullscreen .editor-footer:hover {
  opacity: 1;
}

/* Improve visibility of the bubble menu in dark mode */
.dark .bubble-menu {
  background-color: #1e293b;
  border-color: #334155;
}

/* Typography enhancements */
.ProseMirror h1 {
  margin-bottom: 1rem;
  margin-top: 2rem;
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.ProseMirror h2 {
  margin-bottom: 0.75rem;
  margin-top: 1.5rem;
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.ProseMirror p {
  margin-bottom: 1rem;
  line-height: 1.75;
}

.ProseMirror ul,
.ProseMirror ol {
  margin-left: 1.5rem;
  margin-bottom: 1rem;
}

.ProseMirror blockquote {
  border-left: 3px solid #cbd5e1;
  padding-left: 1rem;
  font-style: italic;
}
