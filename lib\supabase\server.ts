// Mock Supabase client to avoid actual connections
const mockSupabaseClient = {
  auth: {
    getUser: async () => ({
      data: {
        user: {
          id: 'mock-user-id',
          email: '<EMAIL>',
          app_metadata: {},
          user_metadata: {},
          created_at: new Date().toISOString(),
        },
      },
      error: null,
    }),
    signInWithPassword: async () => ({
      data: {
        user: {
          id: 'mock-user-id',
          email: '<EMAIL>',
        },
        session: {
          access_token: 'mock-access-token',
        },
      },
      error: null,
    }),
    signUp: async () => ({
      data: {
        user: {
          id: 'mock-user-id',
          email: '<EMAIL>',
        },
        session: null,
      },
      error: null,
    }),
  },
  from: () => ({
    select: () => ({
      eq: () => ({
        single: () => ({
          data: null,
          error: null,
        }),
      }),
    }),
    insert: () => ({
      values: () => ({
        select: () => ({
          single: () => ({
            data: { id: 'mock-id' },
            error: null,
          }),
        }),
      }),
    }),
  }),
};

export async function createClient() {
  // Return the mock client instead of connecting to Supabase
  return mockSupabaseClient as any;
}
