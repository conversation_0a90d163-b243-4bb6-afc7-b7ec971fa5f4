{"name": "tersa", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:email": "concurrently -n \"Next.js,Email\" \"next dev --turbopack\" \"email dev --port 3001\"", "dev:full": "concurrently -n \"Next.js,Supabase,Email,Stripe\" \"next dev --turbopack\" \"npx supabase start\" \"email dev --port 3001\" \"stripe listen --forward-to localhost:3000/api/webhooks/stripe\"", "build": "next build --turbopack", "start": "next start", "lint": "next lint", "export": "email export"}, "dependencies": {"@ai-sdk/amazon-bedrock": "^2.2.9", "@ai-sdk/anthropic": "^1.2.11", "@ai-sdk/cerebras": "^0.2.14", "@ai-sdk/deepinfra": "^0.2.15", "@ai-sdk/deepseek": "^0.2.14", "@ai-sdk/fal": "^0.1.11", "@ai-sdk/fireworks": "^0.2.14", "@ai-sdk/google": "^1.2.18", "@ai-sdk/groq": "^1.2.9", "@ai-sdk/hume": "^0.0.2", "@ai-sdk/lmnt": "^0.0.2", "@ai-sdk/luma": "^0.1.8", "@ai-sdk/mistral": "^1.2.8", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.12", "@ai-sdk/replicate": "^0.2.8", "@ai-sdk/togetherai": "^0.2.14", "@ai-sdk/xai": "^1.2.16", "@clerk/nextjs": "^6.21.0", "@google/genai": "^1.3.0", "@hookform/resolvers": "^5.0.1", "@icons-pack/react-simple-icons": "^12.8.0", "@marsidev/react-turnstile": "^1.1.0", "@monaco-editor/react": "4.7.0", "@number-flow/react": "^0.5.9", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-aspect-ratio": "^1.1.6", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@radix-ui/react-use-controllable-state": "^1.2.2", "@react-email/components": "0.0.41", "@reduxjs/toolkit": "^2.8.2", "@runwayml/sdk": "^2.0.3", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@t3-oss/env-core": "^0.13.4", "@t3-oss/env-nextjs": "^0.13.4", "@tiptap/core": "^2.12.0", "@tiptap/extension-bullet-list": "^2.12.0", "@tiptap/extension-character-count": "^2.12.0", "@tiptap/extension-code-block-lowlight": "^2.12.0", "@tiptap/extension-color": "^2.12.0", "@tiptap/extension-font-family": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-subscript": "^2.12.0", "@tiptap/extension-superscript": "^2.12.0", "@tiptap/extension-table": "^2.12.0", "@tiptap/extension-table-cell": "^2.12.0", "@tiptap/extension-table-header": "^2.12.0", "@tiptap/extension-table-row": "^2.12.0", "@tiptap/extension-task-item": "^2.12.0", "@tiptap/extension-task-list": "^2.12.0", "@tiptap/extension-text-style": "^2.12.0", "@tiptap/extension-typography": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@tiptap/suggestion": "^2.12.0", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.9", "@vercel/analytics": "^1.5.0", "@xyflow/react": "^12.6.1", "ai": "^4.3.15", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "docx": "^9.5.0", "dotenv": "^16.5.0", "draft-js": "^0.11.7", "draft-js-export-html": "^1.4.1", "draft-js-import-html": "^1.4.1", "draft-js-markdown-plugin": "^3.0.5", "draft-js-plugins-editor": "^3.0.0", "drizzle-orm": "^0.43.1", "embla-carousel-react": "^8.6.0", "fuse.js": "^7.1.0", "html2pdf.js": "^0.10.3", "input-otp": "^1.4.2", "lodash": "^4.17.21", "lowlight": "^3.3.0", "lucide-react": "^0.510.0", "lumaai": "^1.9.0", "modern-screenshot": "^4.6.0", "motion": "^12.11.0", "nanoid": "^5.1.5", "next": "15.3.2", "next-themes": "^0.4.6", "novel": "^1.0.2", "openai": "^4.98.0", "perfect-cursors": "^1.0.5", "postgres": "^3.4.5", "posthog-js": "^1.242.0", "posthog-node": "^4.17.1", "react": "^19.1.0", "react-day-picker": "8.10.1", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-fast-marquee": "^1.6.5", "react-hook-form": "^7.56.3", "react-hotkeys-hook": "^5.0.1", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-resizable-panels": "^3.0.2", "react-tweet": "^3.2.2", "recharts": "^2.15.3", "resend": "^4.5.1", "sonner": "^2.0.3", "standardwebhooks": "^1.0.0", "stripe": "^18.1.0", "swr": "^2.3.3", "tailwind-merge": "^3.3.0", "tippy.js": "^6.3.7", "tw-animate-css": "^1.2.9", "use-debounce": "^10.0.4", "vaul": "^1.1.2", "zod": "^3.24.4"}, "devDependencies": {"@auto-it/first-time-contributor": "^11.3.0", "@auto-it/git-tag": "^11.3.0", "@biomejs/biome": "1.9.4", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.1.2", "drizzle-kit": "^0.31.1", "prettier": "^3.5.3", "react-email": "4.0.13", "tailwindcss": "^4", "typescript": "^5", "ultracite": "4.2.4"}}