export const languages = [
  { id: 'plaintext', label: 'Plain Text' },
  { id: 'abap', label: 'ABAP' },
  { id: 'apex', label: 'Apex' },
  { id: 'azcli', label: 'Azure CLI' },
  { id: 'bat', label: 'Batch' },
  { id: 'bicep', label: 'Bicep' },
  { id: 'cameligo', label: 'CameLIGO' },
  { id: 'clojure', label: 'Clojure' },
  { id: 'coffeescript', label: 'CoffeeScript' },
  { id: 'c', label: 'C' },
  { id: 'cpp', label: 'C++' },
  { id: 'csharp', label: 'C#' },
  { id: 'csp', label: 'CSP' },
  { id: 'css', label: 'CSS' },
  { id: 'cypher', label: 'Cypher' },
  { id: 'dart', label: 'Dart' },
  { id: 'dockerfile', label: 'Dockerfile' },
  { id: 'ecl', label: 'ECL' },
  { id: 'elixir', label: 'Elixir' },
  { id: 'flow9', label: 'Flow9' },
  { id: 'fsharp', label: 'F#' },
  { id: 'freemarker2', label: 'FreeMarker2' },
  { id: 'go', label: 'Go' },
  { id: 'graphql', label: 'GraphQL' },
  { id: 'handlebars', label: 'Handlebars' },
  { id: 'hcl', label: 'HCL' },
  { id: 'html', label: 'HTML' },
  { id: 'ini', label: 'INI' },
  { id: 'java', label: 'Java' },
  { id: 'javascript', label: 'JavaScript' },
  { id: 'julia', label: 'Julia' },
  { id: 'kotlin', label: 'Kotlin' },
  { id: 'less', label: 'LESS' },
  { id: 'lexon', label: 'Lexon' },
  { id: 'lua', label: 'Lua' },
  { id: 'liquid', label: 'Liquid' },
  { id: 'm3', label: 'M3' },
  { id: 'markdown', label: 'Markdown' },
  { id: 'mdx', label: 'MDX' },
  { id: 'mips', label: 'MIPS' },
  { id: 'msdax', label: 'MSDAX' },
  { id: 'mysql', label: 'MySQL' },
  { id: 'objective-c', label: 'Objective-C' },
  { id: 'pascal', label: 'Pascal' },
  { id: 'pascaligo', label: 'PascaLIGO' },
  { id: 'perl', label: 'Perl' },
  { id: 'pgsql', label: 'PostgreSQL' },
  { id: 'php', label: 'PHP' },
  { id: 'pla', label: 'PLA' },
  { id: 'postiats', label: 'PostiATS' },
  { id: 'powerquery', label: 'Power Query' },
  { id: 'powershell', label: 'PowerShell' },
  { id: 'proto', label: 'Protocol Buffers' },
  { id: 'pug', label: 'Pug' },
  { id: 'python', label: 'Python' },
  { id: 'qsharp', label: 'Q#' },
  { id: 'r', label: 'R' },
  { id: 'razor', label: 'Razor' },
  { id: 'redis', label: 'Redis' },
  { id: 'redshift', label: 'Redshift' },
  { id: 'restructuredtext', label: 'reStructuredText' },
  { id: 'ruby', label: 'Ruby' },
  { id: 'rust', label: 'Rust' },
  { id: 'sb', label: 'SB' },
  { id: 'scala', label: 'Scala' },
  { id: 'scheme', label: 'Scheme' },
  { id: 'scss', label: 'SCSS' },
  { id: 'shell', label: 'Shell' },
  { id: 'sol', label: 'Solidity' },
  { id: 'aes', label: 'AES' },
  { id: 'sparql', label: 'SPARQL' },
  { id: 'sql', label: 'SQL' },
  { id: 'st', label: 'Structured Text' },
  { id: 'swift', label: 'Swift' },
  { id: 'systemverilog', label: 'SystemVerilog' },
  { id: 'verilog', label: 'Verilog' },
  { id: 'tcl', label: 'Tcl' },
  { id: 'twig', label: 'Twig' },
  { id: 'typescript', label: 'TypeScript' },
  { id: 'typespec', label: 'TypeSpec' },
  { id: 'vb', label: 'Visual Basic' },
  { id: 'wgsl', label: 'WGSL' },
  { id: 'xml', label: 'XML' },
  { id: 'yaml', label: 'YAML' },
  { id: 'json', label: 'JSON' },
];
