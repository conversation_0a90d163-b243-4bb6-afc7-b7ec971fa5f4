/* Additional modern styling for the Kibo editor */

/* Card styling improvements */
.editor-card {
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03), 0 12px 25px rgba(0, 0, 0, 0.06) !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  background-color: var(--card-bg, rgba(255, 255, 255, 0.8)) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(0, 0, 0, 0.04) !important;
}

.dark .editor-card {
  --card-bg: rgba(15, 23, 42, 0.75);
  border: 1px solid rgba(255, 255, 255, 0.05) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 12px 25px rgba(0, 0, 0, 0.2) !important;
}

.editor-header {
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0.8),
    rgba(255, 255, 255, 0.95)
  ) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
  padding: 1rem 1.5rem !important;
}

.dark .editor-header {
  background: linear-gradient(
    to right,
    rgba(15, 23, 42, 0.85),
    rgba(15, 23, 42, 0.95)
  ) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
}

.editor-title {
  font-size: 1.4rem !important;
  font-weight: 600 !important;
  background: linear-gradient(
    45deg,
    var(--primary),
    var(--primary-light, var(--primary))
  ) !important;
  background-clip: text !important;
  -webkit-background-clip: text !important;
  color: transparent !important;
  letter-spacing: -0.02em !important;
}

/* Custom tabs */
.editor-tabs {
  background-color: rgba(0, 0, 0, 0.02) !important;
  border-radius: 0 !important;
  padding: 0.5rem 1rem 0 !important;
}

.dark .editor-tabs {
  background-color: rgba(255, 255, 255, 0.02) !important;
}

.editor-tab {
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
  border-radius: 8px 8px 0 0 !important;
  border: 1px solid transparent !important;
  border-bottom: none !important;
}

.editor-tab[data-state="active"] {
  background-color: var(--background) !important;
  border-color: rgba(0, 0, 0, 0.05) !important;
  font-weight: 600 !important;
}

.dark .editor-tab[data-state="active"] {
  border-color: rgba(255, 255, 255, 0.05) !important;
}

/* Footer styling */
.editor-footer {
  border-top: 1px solid rgba(0, 0, 0, 0.05) !important;
  padding: 0.75rem 1.5rem !important;
  background-color: rgba(0, 0, 0, 0.01) !important;
}

.dark .editor-footer {
  border-top: 1px solid rgba(255, 255, 255, 0.05) !important;
  background-color: rgba(255, 255, 255, 0.01) !important;
}

/* Stats counter styling */
.stats-counter {
  display: inline-flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.03);
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  margin-right: 0.75rem;
  font-size: 0.75rem;
}

.dark .stats-counter {
  background-color: rgba(255, 255, 255, 0.05);
}

.stats-label {
  font-weight: 500;
  margin-right: 0.25rem;
  color: var(--muted-foreground);
}

.stats-value {
  font-variant-numeric: tabular-nums;
  color: var(--foreground);
}

/* Custom buttons */
.editor-action-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  transition: all 0.15s ease;
  background-color: var(--button-bg, rgba(0, 0, 0, 0.02));
  color: var(--muted-foreground);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.editor-action-button:hover {
  background-color: var(--button-hover-bg, rgba(0, 0, 0, 0.04));
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.dark .editor-action-button {
  --button-bg: rgba(255, 255, 255, 0.05);
  --button-hover-bg: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.editor-primary-button {
  background-color: var(--primary);
  color: white;
  border: none;
}

.editor-primary-button:hover {
  background-color: var(--primary-dark, var(--primary));
  box-shadow: 0 2px 5px rgba(var(--primary-rgb), 0.2);
}

/* Animation for editor entrance */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-editor-in {
  animation: slideIn 0.4s ease-out forwards;
}

/* Table styles */
.ProseMirror table {
  border-collapse: collapse;
  margin: 1.5rem 0;
  overflow: hidden;
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04);
}

.ProseMirror table th {
  background-color: rgba(0, 0, 0, 0.02);
  font-weight: 600;
  padding: 0.75rem;
  text-align: left;
  border-bottom: 2px solid rgba(0, 0, 0, 0.1);
}

.dark .ProseMirror table th {
  background-color: rgba(255, 255, 255, 0.05);
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.ProseMirror table td {
  padding: 0.75rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.dark .ProseMirror table td {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.ProseMirror table tr:last-child td {
  border-bottom: none;
}

/* List styling */
.ProseMirror ul li::marker {
  color: var(--primary);
}

.ProseMirror ol {
  counter-reset: list-counter;
  list-style-type: none;
  padding-left: 1.5rem;
}

.ProseMirror ol li {
  counter-increment: list-counter;
  position: relative;
  padding-left: 0.5rem;
}

.ProseMirror ol li::before {
  content: counter(list-counter) ".";
  position: absolute;
  left: -1.5rem;
  color: var(--primary);
  font-weight: 500;
}
