'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  EditorBubbleMenu,
  EditorCharacterCount,
  EditorContent,
  EditorFloatingMenu,
  EditorFormatBold,
  EditorFormatCode,
  EditorFormatItalic,
  EditorNodeBulletList,
  EditorNodeCodeBlock,
  EditorNodeHeading1,
  EditorNodeHeading2,
  EditorNodeHeading3,
  EditorNodeOrderedList,
  EditorNodeQuote,
  EditorProvider,
} from '@/components/ui/kibo-ui/editor';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export function KiboEditorDocs() {
  return (
    <div className="container py-10">
      <div className="mx-auto max-w-4xl space-y-8">
        <div>
          <h1 className="mb-2 font-bold text-3xl">
            Kibo UI Editor Documentation
          </h1>
          <p className="text-muted-foreground">
            A comprehensive guide to using the Kibo UI Editor in the Tersa AI
            Workflow Builder.
          </p>
        </div>

        <Tabs defaultValue="overview">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="components">Components</TabsTrigger>
            <TabsTrigger value="example">Example</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>About Kibo UI Editor</CardTitle>
                <CardDescription>
                  A rich text editor built on top of TipTap and ProseMirror
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="mb-4 leading-relaxed">
                  The Kibo UI Editor is a powerful, fully-featured rich text
                  editor that provides a word processor-like experience directly
                  in your browser. It features slash commands, floating menus,
                  bubble menus, tables, task lists, and much more.
                </p>
                <p className="mb-4 leading-relaxed">
                  This editor was selected as the primary documentation editor
                  for Tersa because of its comprehensive feature set and
                  excellent user experience.
                </p>
                <div className="flex flex-col gap-2">
                  <h3 className="font-semibold">Key Features:</h3>
                  <ul className="list-disc space-y-1 pl-6">
                    <li>Slash commands for quick formatting</li>
                    <li>Context-aware floating menu</li>
                    <li>Selection-based bubble menu</li>
                    <li>Table support with advanced controls</li>
                    <li>Task lists with checkboxes</li>
                    <li>Code blocks with syntax highlighting</li>
                    <li>Export to various formats (PDF, DOCX)</li>
                    <li>Fullscreen mode</li>
                    <li>Word and character counts</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="components" className="mt-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Core Components</CardTitle>
                <CardDescription>
                  Essential building blocks of the Kibo UI Editor
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="mb-1 font-semibold">EditorProvider</h3>
                  <p className="mb-2 text-muted-foreground text-sm">
                    The main wrapper component that provides the editor context
                    to all child components.
                  </p>
                  <pre className="overflow-x-auto rounded-md bg-muted p-2 text-xs">
                    {`<EditorProvider content={content} onUpdate={handleUpdate}>
  {/* Other editor components */}
</EditorProvider>`}
                  </pre>
                </div>
                <div>
                  <h3 className="mb-1 font-semibold">EditorContent</h3>
                  <p className="mb-2 text-muted-foreground text-sm">
                    Renders the actual editor content area.
                  </p>
                  <pre className="overflow-x-auto rounded-md bg-muted p-2 text-xs">
                    {`<EditorContent />`}
                  </pre>
                </div>
                <div>
                  <h3 className="mb-1 font-semibold">Menu Components</h3>
                  <p className="mb-2 text-muted-foreground text-sm">
                    Various toolbars and menus for formatting content.
                  </p>
                  <pre className="overflow-x-auto rounded-md bg-muted p-2 text-xs">
                    {`<EditorBubbleMenu>
  <EditorNodeHeading1 />
  <EditorFormatBold />
  {/* Additional buttons */}
</EditorBubbleMenu>

<EditorFloatingMenu />
<EditorTableMenu />`}
                  </pre>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="example" className="mt-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Live Example</CardTitle>
                <CardDescription>
                  Try out the Kibo UI Editor below
                </CardDescription>
              </CardHeader>
              <CardContent className="min-h-[400px]">
                <EditorProvider
                  content="<h2>Try me out!</h2><p>This is a live instance of the Kibo UI Editor. Try the following:</p><ul><li>Select text to see the bubble menu</li><li>Press / to see slash commands</li><li>Click between blocks to see the floating menu</li></ul>"
                  placeholder="Start typing..."
                >
                  <EditorBubbleMenu>
                    <EditorNodeHeading1 />
                    <EditorNodeHeading2 />
                    <EditorNodeHeading3 />
                    <EditorNodeBulletList />
                    <EditorNodeOrderedList />
                    <EditorNodeQuote />
                    <EditorNodeCodeBlock />
                    <EditorFormatBold />
                    <EditorFormatItalic />
                    <EditorFormatCode />
                  </EditorBubbleMenu>
                  <EditorFloatingMenu />
                  <EditorContent />
                  <EditorCharacterCount.Characters>
                    Characters:{' '}
                  </EditorCharacterCount.Characters>
                </EditorProvider>
              </CardContent>
              <CardFooter>
                <Button asChild>
                  <a href="/word-editor">Open Full Word Editor</a>
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
