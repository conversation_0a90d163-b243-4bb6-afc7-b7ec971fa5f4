'use client';

import { useCallback, useEffect, useRef, useState } from 'react';

/**
 * Enhanced hook for tracking text selection state in the editor
 * Most of the actual selection enhancement is now handled by the BetterTextSelection extension
 * This version includes debouncing and better performance
 *
 * @param editorRef Reference to the editor instance
 */
export const useEnhancedSelection = (editorRef: React.RefObject<any>) => {
  const [isTextSelected, setIsTextSelected] = useState(false);
  const selectionTimeout = useRef<NodeJS.Timeout | null>(null);

  // Track selection changes with debounce
  const handleSelectionChange = useCallback(() => {
    if (!editorRef.current) return;

    // Clear previous timeout to debounce rapid selection changes
    if (selectionTimeout.current) {
      clearTimeout(selectionTimeout.current);
    }

    // Debounce selection changes
    selectionTimeout.current = setTimeout(() => {
      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) {
        setIsTextSelected(false);
        return;
      }

      const range = selection.getRangeAt(0);
      if (range.collapsed) {
        setIsTextSelected(false);
        return;
      }

      // We have an actual text selection
      setIsTextSelected(true);
    }, 10); // Small timeout to avoid performance issues
  }, [editorRef]);

  // Set up listeners
  useEffect(() => {
    document.addEventListener('selectionchange', handleSelectionChange);
    document.addEventListener('mouseup', handleSelectionChange);
    document.addEventListener('keyup', handleSelectionChange);

    return () => {
      document.removeEventListener('selectionchange', handleSelectionChange);
      document.removeEventListener('mouseup', handleSelectionChange);
      document.removeEventListener('keyup', handleSelectionChange);

      if (selectionTimeout.current) {
        clearTimeout(selectionTimeout.current);
      }
    };
  }, [handleSelectionChange]);

  return {
    isTextSelected,
  };
};
