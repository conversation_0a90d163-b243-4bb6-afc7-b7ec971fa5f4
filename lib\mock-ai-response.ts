import { nanoid } from 'nanoid';

// This provides a mock AI flow for development when Gemini API is not available
export function getMockAIFlow(prompt: string) {
  // Create a deterministic flow based on the prompt that matches the expected node structure
  const nodes = [
    // First node - Text input (primitive source)
    {
      id: nanoid(6),
      type: 'text',
      data: {
        source: 'primitive',
        model: 'default',
        text: 'Input text here',
        content: {
          type: 'doc',
          content: [
            {
              type: 'paragraph',
              content: [
                {
                  type: 'text',
                  text: 'Input text here',
                },
              ],
            },
          ],
        },
      },
      position: { x: 100, y: 100 },
      origin: [0, 0.5],
      // The following are optional but might help ReactFlow
      width: 300,
      height: 200,
      draggable: true,
      selectable: true,
    },
    // Second node - AI text transformation
    {
      id: nanoid(6),
      type: 'text',
      data: {
        source: 'transform',
        instructions: `Transform the input based on: ${prompt.substring(0, 100)}`,
        model: 'default',
      },
      position: { x: 500, y: 100 },
      origin: [0, 0.5],
      width: 300,
      height: 200,
      draggable: true,
      selectable: true,
    },
    // Third node - Image generation
    {
      id: nanoid(6),
      type: 'image',
      data: {
        source: 'transform',
        instructions: `Generate an image related to: ${prompt.substring(0, 100)}`,
        model: 'default',
        size: 'medium',
      },
      position: { x: 900, y: 100 },
      origin: [0, 0.5],
      width: 400,
      height: 300,
      draggable: true,
      selectable: true,
    },
  ];
  // Store node IDs for edge creation
  const nodeIds = nodes.map((node) => node.id);

  // Create edges for a clear flow
  const edges = [
    {
      id: nanoid(6),
      source: nodeIds[0],
      target: nodeIds[1],
      type: 'animated',
    },
    {
      id: nanoid(6),
      source: nodeIds[1],
      target: nodeIds[2],
      type: 'animated',
    },
  ];

  return { nodes, edges };
}
