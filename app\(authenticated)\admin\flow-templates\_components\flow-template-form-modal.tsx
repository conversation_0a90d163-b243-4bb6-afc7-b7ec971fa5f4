'use client';

import { useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogClose
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { flow_templates, categories as categoriesSchema } from '@/schema';
import type { InferSelectModel } from 'drizzle-orm';

type FlowTemplate = InferSelectModel<typeof flow_templates>;
type Category = InferSelectModel<typeof categoriesSchema>;

interface FlowTemplateFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (formData: FormData) => Promise<void>;
  template?: FlowTemplate | null;
  categories: Category[];
}

export function FlowTemplateFormModal({
  isOpen,
  onClose,
  onSubmit,
  template,
  categories,
}: FlowTemplateFormModalProps) {
  const formRef = useRef<HTMLFormElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [currentFlowJson, setCurrentFlowJson] = useState<string>('');
  const [isPublic, setIsPublic] = useState(true);

  useEffect(() => {
    if (isOpen && formRef.current) {
      if (template) {
        (formRef.current.elements.namedItem('name') as HTMLInputElement).value = template.name;
        (formRef.current.elements.namedItem('description') as HTMLTextAreaElement).value = template.description || '';
        const flowJsonString = typeof template.flow_json === 'string' ? template.flow_json : JSON.stringify(template.flow_json, null, 2);
        setCurrentFlowJson(flowJsonString);
        (formRef.current.elements.namedItem('flow_json') as HTMLTextAreaElement).value = flowJsonString;
        // Category and is_public are handled by Select and Switch state
        setIsPublic(template.is_public === null ? true : template.is_public);
      } else {
        formRef.current.reset();
        setCurrentFlowJson('');
        setIsPublic(true);
      }
    }
  }, [isOpen, template]);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsLoading(true);
    const formData = new FormData(event.currentTarget);
    // Ensure is_public is set correctly from the Switch state
    formData.set('is_public', String(isPublic)); 
    await onSubmit(formData);
    setIsLoading(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-lg md:max-w-xl lg:max-w-2xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>{template ? 'Edit Flow Template' : 'Create New Flow Template'}</DialogTitle>
          <DialogDescription>
            {template ? 'Update the details of this flow template.' : 'Fill in the details to create a new flow template.'}
          </DialogDescription>
        </DialogHeader>
        <form ref={formRef} onSubmit={handleSubmit} className="grid gap-4 py-4 overflow-y-auto flex-grow">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              Name
            </Label>
            <Input id="name" name="name" required className="col-span-3" />
          </div>

          <div className="grid grid-cols-4 items-start gap-4">
            <Label htmlFor="description" className="text-right pt-2">
              Description
            </Label>
            <Textarea id="description" name="description" placeholder="Optional: A brief description of the template" className="col-span-3 min-h-[80px]" />
          </div>

          <div className="grid grid-cols-4 items-start gap-4">
            <Label htmlFor="flow_json" className="text-right pt-2">
              Flow JSON
            </Label>
            <Textarea
              id="flow_json"
              name="flow_json"
              required
              placeholder='Enter or paste the React Flow JSON content here. e.g., { "nodes": [], "edges": [] }'
              className="col-span-3 min-h-[150px] font-mono text-sm"
              value={currentFlowJson} // Controlled component for JSON
              onChange={(e) => setCurrentFlowJson(e.target.value)}
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="category_id" className="text-right">
              Category
            </Label>
            <Select name="category_id" defaultValue={template?.category_id || ''}>
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select a category (optional)" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value=""><em>No Category</em></SelectItem>
                {categories.map((cat) => (
                  <SelectItem key={cat.id} value={cat.id}>
                    {cat.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="is_public" className="text-right">
              Public
            </Label>
            <div className="col-span-3 flex items-center">
                <Switch 
                    id="is_public" 
                    name="is_public" // Name attribute might not be needed if handled via FormData.set
                    checked={isPublic} 
                    onCheckedChange={setIsPublic} 
                />
                <span className="ml-2 text-sm text-muted-foreground">
                    {isPublic ? 'Visible in public gallery' : 'Private (not listed publicly)'}
                </span>
            </div>
          </div>

          <DialogFooter className="mt-auto pt-4 border-t">
            <DialogClose asChild>
                <Button type="button" variant="outline" onClick={onClose} disabled={isLoading}>
                    Cancel
                </Button>
            </DialogClose>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (template ? 'Saving...' : 'Creating...') : (template ? 'Save Changes' : 'Create Template')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}