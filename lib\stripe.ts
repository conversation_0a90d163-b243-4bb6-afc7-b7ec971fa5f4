import Stripe from 'stripe';
import { currentUserProfile } from './auth';
import { env } from './env';

// Create a mock Stripe instance that doesn't actually make API calls
const mockStripe = {
  billing: {
    meterEvents: {
      create: async () => ({ id: 'mock-event-id' }),
    },
  },
  invoices: {
    createPreview: async () => ({
      lines: {
        data: [
          {
            pricing: {
              price_details: {
                product: env.STRIPE_USAGE_PRODUCT_ID,
                price: 'mock-price-id',
              },
            },
            quantity: 0,
          },
        ],
      },
    }),
  },
  prices: {
    retrieve: async () => ({
      tiers: [{ up_to: 100000 }],
    }),
  },
};

// Export the mock Stripe instance instead of the real one
export const stripe = mockStripe as unknown as Stripe;

const creditValue = 0.005;

export const trackCreditUsage = async ({
  action,
  cost,
}: {
  action: string;
  cost: number;
}) => {
  // Skip credit tracking completely
  return;
  
  // Original implementation:
  // const profile = await currentUserProfile();
  // const credits = Math.ceil(cost / creditValue);

  // if (!profile) {
  //   throw new Error('User profile not found');
  // }

  // if (!profile.customerId) {
  //   throw new Error('User customerId not found');
  // }

  // await stripe.billing.meterEvents.create({
  //   event_name: env.STRIPE_CREDITS_METER_NAME,
  //   payload: {
  //     action,
  //     value: credits.toString(),
  //     stripe_customer_id: profile.customerId,
  //   },
  // });
};
