'use client';

import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';

export function EditorSplashScreen() {
  const [show, setShow] = useState(true);
  const { theme } = useTheme();

  useEffect(() => {
    // Hide splash screen after animation completes
    const timer = setTimeout(() => {
      setShow(false);
    }, 2000); // Match this with the CSS animation duration + delay

    return () => clearTimeout(timer);
  }, []);

  if (!show) return null;

  return (
    <div className="editor-splash-screen">
      <div className="editor-splash-logo">
        <svg
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className={`h-full w-full ${theme === 'dark' ? 'text-primary' : 'text-primary'}`}
        >
          <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
          <polyline points="14 2 14 8 20 8" />
          <path d="M8 13h8" />
          <path d="M8 17h8" />
          <path d="M8 9h1" />
        </svg>
      </div>
      <h1 className="editor-splash-title">Tersa Word Editor</h1>
      <p className="editor-splash-subtitle">
        A modern, elegant document editor for your thoughts and ideas
      </p>
      <div className="editor-loading-indicator">
        <div className="editor-loading-dot"></div>
        <div className="editor-loading-dot"></div>
        <div className="editor-loading-dot"></div>
      </div>
    </div>
  );
}
