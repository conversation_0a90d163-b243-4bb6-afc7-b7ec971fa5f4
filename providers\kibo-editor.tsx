'use client';

import {
  type KiboEditorDocument,
  useKiboEditor,
} from '@/hooks/use-kibo-editor';
import { createContext, useContext, useEffect, useState } from 'react';

type KiboEditorContextType = ReturnType<typeof useKiboEditor> & {
  documents: KiboEditorDocument[];
  createDocument: () => void;
  saveDocument: (doc: KiboEditorDocument) => void;
  loadDocument: (id: string) => void;
  deleteDocument: (id: string) => void;
};

const KiboEditorContext = createContext<KiboEditorContextType | undefined>(
  undefined
);

export function KiboEditorProvider({
  children,
}: { children: React.ReactNode }) {
  const [documents, setDocuments] = useState<KiboEditorDocument[]>([]);
  const editorState = useKiboEditor();

  // Load saved documents from localStorage on mount
  useEffect(() => {
    try {
      const savedDocs = localStorage.getItem('kibo-editor-documents');
      if (savedDocs) {
        const parsedDocs = JSON.parse(savedDocs) as KiboEditorDocument[];
        setDocuments(parsedDocs);
      }
    } catch (error) {
      console.error('Failed to load documents from localStorage:', error);
    }
  }, []);

  // Save documents to localStorage whenever they change
  useEffect(() => {
    if (documents.length > 0) {
      localStorage.setItem('kibo-editor-documents', JSON.stringify(documents));
    }
  }, [documents]);

  const createDocument = () => {
    const newDoc: KiboEditorDocument = {
      id: Date.now().toString(),
      title: 'Untitled Document',
      content: '<h1>Untitled Document</h1><p>Start writing here...</p>',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    setDocuments((prev) => [...prev, newDoc]);
    editorState.setDocument(newDoc);
  };

  const saveDocument = (doc: KiboEditorDocument) => {
    const docToSave = {
      ...doc,
      updatedAt: new Date(),
      id: doc.id || Date.now().toString(),
    };

    setDocuments((prevDocs) => {
      const exists = prevDocs.find((d) => d.id === docToSave.id);
      if (exists) {
        return prevDocs.map((d) => (d.id === docToSave.id ? docToSave : d));
      } else {
        return [...prevDocs, docToSave];
      }
    });

    editorState.setDocument(docToSave);
  };

  const loadDocument = (id: string) => {
    const doc = documents.find((d) => d.id === id);
    if (doc) {
      editorState.setDocument(doc);
    }
  };

  const deleteDocument = (id: string) => {
    setDocuments((prev) => prev.filter((doc) => doc.id !== id));

    // If the current document is deleted, load another or create a new one
    if (editorState.document.id === id) {
      if (documents.length > 1) {
        const nextDoc = documents.find((doc) => doc.id !== id);
        if (nextDoc) {
          editorState.setDocument(nextDoc);
          return;
        }
      }

      // No documents left, create a new one
      createDocument();
    }
  };

  return (
    <KiboEditorContext.Provider
      value={{
        ...editorState,
        documents,
        createDocument,
        saveDocument,
        loadDocument,
        deleteDocument,
      }}
    >
      {children}
    </KiboEditorContext.Provider>
  );
}

export function useKiboEditorContext() {
  const context = useContext(KiboEditorContext);
  if (context === undefined) {
    throw new Error(
      'useKiboEditorContext must be used within a KiboEditorProvider'
    );
  }
  return context;
}
