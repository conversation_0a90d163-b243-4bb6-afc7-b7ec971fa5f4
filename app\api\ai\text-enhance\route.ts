// app/api/ai/text-enhance/route.ts
import { getSubscribedUser } from '@/lib/auth';
import { parseError } from '@/lib/error/parse';
import { textModels } from '@/lib/models/text';
import { createRateLimiter, slidingWindow } from '@/lib/rate-limit';
import { trackCreditUsage } from '@/lib/stripe';
import { streamText } from 'ai';

export const maxDuration = 30;

// Helper function to find model by ID
const getModelById = (id: string) => {
  return textModels
    .flatMap((group) => group.models)
    .find((model) => model.id === id);
};

const rateLimiter = createRateLimiter({
  limiter: slidingWindow(10, '1 m'),
  prefix: 'api-text-enhance',
});

export async function POST(req: Request) {
  let user;
  try {
    // Authenticate user
    user = await getSubscribedUser();
    if (!user) {
      return new Response('Unauthorized', { status: 401 });
    }
  } catch (error) {
    const parsedError = parseError(error);
    return new Response(parsedError.message, {
      status: parsedError.status === 401 ? 401 : 500,
    });
  }

  try {
    // Apply rate limiting using user.id
    const { success } = await rateLimiter.limit(user.id);
    if (!success) {
      return new Response('Rate limit exceeded', { status: 429 });
    }

    const body = await req.json();
    const { prompt, selectedText, action, customPrompt, modelId, context } =
      body;

    if (!modelId || !action) {
      return new Response('Missing modelId or action', { status: 400 });
    }

    const model = getModelById(modelId);
    if (!model) {
      return new Response('Model not found', { status: 400 });
    }

    let systemPrompt =
      'You are an AI assistant that helps manipulate and generate text.';
    if (context) {
      systemPrompt += `\n\nContext: "${context}"`;
    }

    let userMessage = '';

    // Validate inputs for each action
    switch (action) {
      case 'generate':
        if (!prompt)
          return new Response('Prompt is required for "generate" action', {
            status: 400,
          });
        userMessage = prompt;
        break;
      case 'summarize':
        if (!selectedText)
          return new Response(
            'Selected text is required for "summarize" action',
            { status: 400 }
          );
        userMessage = `Summarize the following text:\n\n"${selectedText}"`;
        break;
      case 'rephrase':
        if (!selectedText)
          return new Response(
            'Selected text is required for "rephrase" action',
            { status: 400 }
          );
        userMessage = `Rephrase the following text:\n\n"${selectedText}"`;
        break;
      case 'custom_prompt':
        if (!selectedText || !customPrompt)
          return new Response(
            'Selected text and custom prompt are required for "custom_prompt" action',
            { status: 400 }
          );
        userMessage = `${customPrompt}\n\nText to apply prompt to:\n\n"${selectedText}"`;
        break;
      case 'improve_writing':
        if (!selectedText)
          return new Response(
            'Selected text is required for "improve_writing" action',
            { status: 400 }
          );
        userMessage = `Improve the writing of the following text:\n\n"${selectedText}"`;
        break;
      case 'make_shorter':
        if (!selectedText)
          return new Response(
            'Selected text is required for "make_shorter" action',
            { status: 400 }
          );
        userMessage = `Make the following text shorter:\n\n"${selectedText}"`;
        break;
      case 'make_longer':
        if (!selectedText)
          return new Response(
            'Selected text is required for "make_longer" action',
            { status: 400 }
          );
        userMessage = `Make the following text longer:\n\n"${selectedText}"`;
        break;
      case 'change_tone_to_professional':
        if (!selectedText)
          return new Response(
            'Selected text is required for "change_tone_to_professional" action',
            { status: 400 }
          );
        userMessage = `Change the tone of the following text to professional:\n\n"${selectedText}"`;
        break;
      default:
        return new Response(`Invalid action: ${action}`, { status: 400 });
    }

    const result = streamText({
      model: model.model,
      system: systemPrompt,
      prompt: userMessage,
      onFinish: async ({ usage }) => {
        if (user?.id && model.getCost) {
          const cost = model.getCost({
            input: usage.promptTokens,
            output: usage.completionTokens || 0,
          });
          await trackCreditUsage({
            userId: user.id,
            action: action,
            modelId: model.id,
            cost,
            promptTokens: usage.promptTokens,
            completionTokens: usage.completionTokens || 0,
            totalTokens: usage.totalTokens,
          });
        }
      },
    });

    // Collect the stream into a single string for JSON response
    let generatedText = '';
    const reader = result.textStream.getReader();
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      generatedText += value;
    }

    return new Response(JSON.stringify({ generatedText }), {
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    const parsedError = parseError(error);
    // Ensure a default status if parsedError.status is not set
    return new Response(parsedError.message, {
      status: parsedError.status || 500,
    });
  }
}
