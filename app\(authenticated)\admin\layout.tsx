import { PropsWithChildren } from 'react';
import { NextPage } from 'next';
import { AdminSidebar } from './_components/admin-sidebar'; // Assuming a sidebar component
import { checkRole } from '@/lib/roles'; // You'll need to implement this
import { redirect } from 'next/navigation';

const AdminLayout: NextPage<PropsWithChildren> = async ({ children }) => {
  // Role-based access control
  // const isAdmin = await checkRole('admin');
  // if (!isAdmin) {
  //   redirect('/unauthorized'); // Or to a more appropriate page
  // }

  // For now, allow access if authenticated, actual role check is crucial for production
  // This is a placeholder until proper role management is implemented
  console.warn('TODO: Implement proper admin role check in AdminLayout');

  return (
    <div className="flex h-full">
      <AdminSidebar />
      <main className="flex-1 p-6 md:p-8 lg:p-10 overflow-y-auto">
        {children}
      </main>
    </div>
  );
};

export default AdminLayout;