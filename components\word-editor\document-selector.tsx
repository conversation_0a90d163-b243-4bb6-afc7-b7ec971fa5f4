'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  type StoredDocument,
  deleteDocumentFromStorage,
  getDocumentFromStorage,
  getRecentDocuments,
} from '@/lib/document-storage';
import {
  Calendar,
  Edit,
  FilePlus,
  FileText,
  MoreVertical,
  PanelLeft,
  Trash2,
} from 'lucide-react';
import { useEffect, useState } from 'react';

interface DocumentSelectorProps {
  currentDocId: string | undefined;
  onCreateNew: () => void;
  onLoadDocument: (document: StoredDocument) => void;
  onDeleteDocument?: (id: string) => void;
}

export function DocumentSelector({
  currentDocId,
  onCreateNew,
  onLoadDocument,
  onDeleteDocument,
}: DocumentSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [recentDocs, setRecentDocs] = useState<
    Array<{ id: string; title: string; updatedAt: string }>
  >([]);
  const [confirmDeleteId, setConfirmDeleteId] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      // Refresh document list when dialog opens
      setRecentDocs(getRecentDocuments());
    }
  }, [isOpen]);

  const handleLoadDocument = (id: string) => {
    const document = getDocumentFromStorage(id);
    if (document) {
      onLoadDocument(document);
      setIsOpen(false);
    }
  };

  const handleDeleteDocument = (id: string) => {
    deleteDocumentFromStorage(id);
    setRecentDocs((prev) => prev.filter((doc) => doc.id !== id));
    if (onDeleteDocument) {
      onDeleteDocument(id);
    }
    setConfirmDeleteId(null);
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return new Intl.DateTimeFormat('en-US', {
        day: 'numeric',
        month: 'short',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      }).format(date);
    } catch (e) {
      return 'Unknown date';
    }
  };

  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(true)}
        className="flex items-center gap-1"
      >
        <PanelLeft className="h-4 w-4" />
        <span>Documents</span>
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Your Documents
            </DialogTitle>
            <DialogDescription>
              Recent documents saved in your browser
            </DialogDescription>
          </DialogHeader>

          <Button
            variant="outline"
            className="mb-4 flex w-full items-center justify-center gap-2"
            onClick={() => {
              onCreateNew();
              setIsOpen(false);
            }}
          >
            <FilePlus className="h-4 w-4" />
            Create New Document
          </Button>

          {recentDocs.length === 0 ? (
            <div className="py-8 text-center text-muted-foreground">
              No saved documents found
            </div>
          ) : (
            <ScrollArea className="h-[300px]">
              <div className="space-y-2">
                {recentDocs.map((doc) => (
                  <div
                    key={doc.id}
                    className={`flex items-center justify-between rounded-md border p-3 ${
                      doc.id === currentDocId
                        ? 'border-primary bg-primary/5'
                        : 'border-border'
                    } transition-colors hover:bg-muted/50`}
                  >
                    <div
                      className="flex-1 cursor-pointer"
                      onClick={() => handleLoadDocument(doc.id)}
                    >
                      <div className="line-clamp-1 font-medium">
                        {doc.title || 'Untitled Document'}
                      </div>
                      <div className="mt-1 flex items-center gap-1 text-muted-foreground text-xs">
                        <Calendar className="h-3 w-3" />
                        <span>{formatDate(doc.updatedAt)}</span>
                      </div>
                    </div>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                        >
                          <MoreVertical className="h-4 w-4" />
                          <span className="sr-only">Actions</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => handleLoadDocument(doc.id)}
                          className="flex items-center gap-2"
                        >
                          <Edit className="h-4 w-4" />
                          Open
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => setConfirmDeleteId(doc.id)}
                          className="flex items-center gap-2 text-destructive focus:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Confirm Delete Dialog */}
      <Dialog
        open={!!confirmDeleteId}
        onOpenChange={() => setConfirmDeleteId(null)}
      >
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="text-destructive">
              Confirm Delete
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this document? This action cannot
              be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="gap-2 sm:gap-0">
            <Button variant="outline" onClick={() => setConfirmDeleteId(null)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() =>
                confirmDeleteId && handleDeleteDocument(confirmDeleteId)
              }
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
