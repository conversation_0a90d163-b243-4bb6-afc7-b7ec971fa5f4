'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  EditorBubbleMenu,
  EditorContent,
  EditorFloatingMenu,
  EditorFormatBold,
  EditorFormatCode,
  EditorFormatItalic,
  EditorFormatStrike,
  EditorFormatSubscript,
  EditorFormatSuperscript,
  EditorFormatUnderline,
  EditorLinkSelector,
  EditorNodeBulletList,
  EditorNodeCodeBlock,
  EditorNodeHeading1,
  EditorNodeHeading2,
  EditorNodeHeading3,
  EditorNodeOrderedList,
  EditorNodeQuote,
  EditorNodeTable,
  EditorProvider,
  EditorTableMenu,
} from '@/components/ui/kibo-ui/editor';
import { useKiboEditor } from '@/hooks/use-kibo-editor';
import { BetterTextSelection } from '@/lib/extensions/better-text-selection';
import { setGlobalKiboEditor } from '@/lib/kibo-editor-global';
import {
  Download,
  File,
  FileText,
  Maximize,
  Minimize,
  Save,
} from 'lucide-react';
import { useEffect, useRef } from 'react';
import './editor-fullscreen.css';
import './editor-enhancements.css';
import './editor-additional-styles.css';
import './editor-selection-styles.css';
import './editor-layout-fixes.css';
import './editor-headings.css';
import './editor-header-styles.css';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { WordEditorHeader as EditorHeader } from '@/components/word-editor/header';
import { SafeHTMLPreview } from '@/components/word-editor/safe-html-preview';
import { useEnhancedSelection } from '@/hooks/use-enhanced-selection-v2';

export default function WordEditorPage() {
  const {
    document: doc,
    handleEditorUpdate,
    exportToPdf,
    exportToDocx,
    isFullscreen,
    toggleFullscreen,
    wordCount,
    charCount,
    editor,
  } = useKiboEditor();

  // Using a more specific type for the editor reference
  const editorRef = useRef<{ commands?: Record<string, any> }>(null);

  // Use our enhanced selection hook
  const { isTextSelected } = useEnhancedSelection(editorRef);

  // Save the editor instance globally
  useEffect(() => {
    if (editorRef.current) {
      setGlobalKiboEditor(editorRef.current);

      return () => {
        setGlobalKiboEditor(null);
      };
    }
  }, []);
  const containerClass = isFullscreen
    ? 'editor-fullscreen'
    : 'container mx-auto py-4 px-4 mt-4';
  return (
    <div className={containerClass}>
      <EditorHeader />
      <Card className="editor-card animate-editor-in">
        <CardHeader className="editor-header">
          <CardTitle className="flex items-center justify-between">
            <div className="group relative cursor-pointer">
              <div className="editor-title">{doc.title}</div>
              <div className="-bottom-1 absolute left-0 h-0.5 w-0 bg-primary transition-all duration-300 group-hover:w-full" />
            </div>
            <div className="flex gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleFullscreen}
                className="editor-action-button"
              >
                {isFullscreen ? (
                  <Minimize className="h-4 w-4" />
                ) : (
                  <Maximize className="h-4 w-4" />
                )}
                {isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="editor-action-button"
                onClick={() => {
                  // Implementation for save button
                  const timestamp = new Date()
                    .toISOString()
                    .replace(/[:.]/g, '-');
                  const blob = new Blob([doc.content], { type: 'text/html' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `${doc.title}-${timestamp}.html`;
                  document.body.appendChild(a);
                  a.click();
                  document.body.removeChild(a);
                  URL.revokeObjectURL(url);
                }}
              >
                <Save className="mr-2 h-4 w-4" />
                Save
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="editor-action-button"
                  >
                    <Download className="mr-1 h-4 w-4" />
                    Export
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem
                    onClick={exportToDocx}
                    className="flex items-center gap-2"
                  >
                    <FileText className="h-4 w-4" />
                    Export as DOCX
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={exportToPdf}
                    className="flex items-center gap-2"
                  >
                    <File className="h-4 w-4" />
                    Export as PDF
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <Tabs defaultValue="editor" className="w-full">
            <TabsList className="editor-tabs w-full">
              <TabsTrigger value="editor" className="editor-tab">
                Document
              </TabsTrigger>
              <TabsTrigger value="preview" className="editor-tab">
                Preview
              </TabsTrigger>
            </TabsList>
            <TabsContent
              value="editor"
              className="prose prose-lg dark:prose-invert mx-auto max-w-5xl p-4"
            >
              <EditorProvider
                content={doc.content}
                onUpdate={handleEditorUpdate}
                onCreate={({ editor }) => {
                  editorRef.current = editor;
                }}
                placeholder="Start writing..."
                className="editor-paper min-h-[500px] rounded-lg p-0"
                extensions={[BetterTextSelection]}
                editorProps={{
                  attributes: {
                    class: 'outline-none selection-editor',
                  },
                }}
              >
                <EditorBubbleMenu className="bubble-menu">
                  <EditorNodeHeading1 />
                  <EditorNodeHeading2 />
                  <EditorNodeHeading3 />
                  <EditorNodeBulletList />
                  <EditorNodeOrderedList />
                  <EditorNodeQuote />
                  <EditorNodeCodeBlock />
                  <EditorNodeTable />
                  <EditorFormatBold />
                  <EditorFormatItalic />
                  <EditorFormatStrike />
                  <EditorFormatUnderline />
                  <EditorFormatCode />
                  <EditorLinkSelector />
                  <EditorFormatSubscript />
                  <EditorFormatSuperscript />
                </EditorBubbleMenu>
                <EditorFloatingMenu className="floating-menu" />
                <EditorTableMenu />
                <EditorContent contentClassName="editor-content" />
              </EditorProvider>
            </TabsContent>
            <TabsContent
              value="preview"
              className="prose prose-lg dark:prose-invert mx-auto max-w-5xl p-4"
            >
              <SafeHTMLPreview
                content={doc.content}
                className="editor-paper min-h-[500px] rounded-lg p-6"
              />
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="editor-footer">
          <div className="flex items-center gap-4">
            <div className="stats-counter">
              <span className="stats-label">Words:</span>
              <span className="stats-value">{wordCount}</span>
            </div>
            <div className="stats-counter">
              <span className="stats-label">Characters:</span>
              <span className="stats-value">{charCount}</span>
            </div>
          </div>
          <div className="text-muted-foreground/80 text-xs">
            Last saved: now
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
