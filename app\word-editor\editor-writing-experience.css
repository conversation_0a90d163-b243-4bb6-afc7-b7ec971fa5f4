/*
  Enhanced Writing Experience Styles for Tersa Word Editor
  Optimized for comfortable, distraction-free writing
*/

/* ===== CORE WRITING EXPERIENCE ===== */

/* Enhanced ProseMirror editor for optimal writing */
.prose-writer {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>,
    "Helvetica Neue", Arial, sans-serif;
  font-size: 1.125rem !important;
  line-height: 1.75 !important;
  color: hsl(var(--foreground)) !important; /* Full opacity for better visibility */
  padding: 2.5rem 3rem !important;
  min-height: 600px !important;
  max-width: none !important;

  /* Typography optimizations */
  font-feature-settings: "liga" 1, "kern" 1, "calt" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Improved focus and interaction */
  transition: all 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  cursor: text;

  /* Better spacing for readability */
  word-spacing: 0.05em;
  letter-spacing: -0.011em;
}

/* Enhanced focus state */
.prose-writer:focus {
  outline: none !important;
  background-color: hsl(var(--background)) !important;
  box-shadow: inset 0 0 0 1px hsl(var(--border) / 0.3) !important;
}

/* Ensure high contrast for text in all states */
.prose-writer,
.prose-writer p,
.prose-writer div,
.prose-writer span {
  color: hsl(var(--foreground)) !important;
  text-shadow: none !important;
}

/* ===== IMPROVED TYPOGRAPHY ===== */

/* Better paragraph spacing */
.prose-writer p {
  margin-top: 1.25em !important;
  margin-bottom: 1.25em !important;
  line-height: 1.75 !important;
  color: hsl(var(--foreground)) !important; /* Full opacity for better readability */
}

/* Enhanced headings for better hierarchy */
.prose-writer h1 {
  font-size: 2.25rem !important;
  font-weight: 700 !important;
  line-height: 1.2 !important;
  margin-top: 2.5rem !important;
  margin-bottom: 1.5rem !important;
  color: hsl(var(--foreground)) !important;
  letter-spacing: -0.025em !important;
}

.prose-writer h2 {
  font-size: 1.875rem !important;
  font-weight: 600 !important;
  line-height: 1.3 !important;
  margin-top: 2rem !important;
  margin-bottom: 1rem !important;
  color: hsl(var(--foreground)) !important;
  letter-spacing: -0.02em !important;
}

.prose-writer h3 {
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  line-height: 1.4 !important;
  margin-top: 1.75rem !important;
  margin-bottom: 0.75rem !important;
  color: hsl(var(--foreground)) !important;
  letter-spacing: -0.015em !important;
}

/* ===== ENHANCED SELECTION ===== */

/* Better text selection styling */
.prose-writer ::selection {
  background: linear-gradient(
    135deg,
    hsl(var(--primary) / 0.25),
    hsl(var(--primary) / 0.15)
  ) !important;
  color: hsl(var(--primary-foreground)) !important;
  border-radius: 2px;
}

.prose-writer:focus ::selection {
  background: linear-gradient(
    135deg,
    hsl(var(--primary) / 0.35),
    hsl(var(--primary) / 0.25)
  ) !important;
}

/* ===== IMPROVED CURSOR AND CARET ===== */

/* Enhanced cursor visibility */
.prose-writer .ProseMirror-cursor {
  border-left: 2px solid hsl(var(--primary)) !important;
  animation: blink 1.2s infinite;
}

@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

/* ===== BETTER LIST STYLING ===== */

.prose-writer ul {
  margin-top: 1.25em !important;
  margin-bottom: 1.25em !important;
  padding-left: 1.5rem !important;
}

.prose-writer ol {
  margin-top: 1.25em !important;
  margin-bottom: 1.25em !important;
  padding-left: 1.5rem !important;
}

.prose-writer li {
  margin-top: 0.5em !important;
  margin-bottom: 0.5em !important;
  line-height: 1.75 !important;
}

/* ===== ENHANCED BLOCKQUOTES ===== */

.prose-writer blockquote {
  border-left: 4px solid hsl(var(--primary) / 0.3) !important;
  margin: 1.5rem 0 !important;
  font-style: italic !important;
  color: hsl(var(--foreground) / 0.8) !important;
  background: hsl(var(--muted) / 0.3) !important;
  border-radius: 0 8px 8px 0 !important;
  padding: 1rem 1.5rem !important;
}

/* ===== IMPROVED CODE STYLING ===== */

.prose-writer code {
  background: hsl(var(--muted) / 0.6) !important;
  padding: 0.2em 0.4em !important;
  border-radius: 4px !important;
  font-size: 0.9em !important;
  font-family: "JetBrains Mono", "Fira Code", Consolas, monospace !important;
  color: hsl(var(--foreground) / 0.9) !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
}

.prose-writer pre {
  background: hsl(var(--muted) / 0.4) !important;
  padding: 1.5rem !important;
  border-radius: 8px !important;
  overflow-x: auto !important;
  margin: 1.5rem 0 !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
}

/* ===== PLACEHOLDER STYLING ===== */

.prose-writer .ProseMirror p.is-editor-empty:first-child::before {
  color: hsl(var(--muted-foreground) / 0.6) !important;
  content: attr(data-placeholder) !important;
  float: left !important;
  height: 0 !important;
  pointer-events: none !important;
  font-style: italic !important;
}

/* ===== RESPONSIVE IMPROVEMENTS ===== */

@media (max-width: 768px) {
  .prose-writer {
    font-size: 1rem !important;
    line-height: 1.6 !important;
    padding: 1.5rem 1rem !important;
  }

  .prose-writer h1 {
    font-size: 1.875rem !important;
    margin-top: 2rem !important;
    margin-bottom: 1rem !important;
  }

  .prose-writer h2 {
    font-size: 1.5rem !important;
    margin-top: 1.5rem !important;
    margin-bottom: 0.75rem !important;
  }

  .prose-writer h3 {
    font-size: 1.25rem !important;
    margin-top: 1.25rem !important;
    margin-bottom: 0.5rem !important;
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

/* Reduce repaints and improve scrolling */
.prose-writer {
  will-change: contents;
  contain: layout style;
}

/* Optimize for smooth scrolling */
.prose-writer * {
  scroll-behavior: smooth;
}

/* ===== DARK MODE ENHANCEMENTS ===== */

.dark .prose-writer {
  color: hsl(var(--foreground)) !important; /* Full opacity for dark mode too */
}

.dark .prose-writer p {
  color: hsl(var(--foreground)) !important; /* Full opacity for better readability */
}

.dark .prose-writer blockquote {
  background: hsl(var(--muted) / 0.2) !important;
  color: hsl(var(--foreground) / 0.75) !important;
}

.dark .prose-writer code {
  background: hsl(var(--muted) / 0.4) !important;
  color: hsl(var(--foreground) / 0.85) !important;
}

/* ===== WRITING FLOW IMPROVEMENTS ===== */

/* Keep menus at full opacity - no distractions */
.prose-writer .bubble-menu,
.prose-writer .floating-menu {
  opacity: 1;
}

/* Better line spacing for readability */
.prose-writer p + p {
  margin-top: 1.5em !important;
}

/* Improved focus ring for better accessibility */
.prose-writer:focus-visible {
  outline: 2px solid hsl(var(--primary) / 0.5) !important;
  outline-offset: 4px !important;
}

/* Smooth transitions for all interactive elements */
.prose-writer * {
  transition: color 0.2s ease, background-color 0.2s ease;
}

/* Better spacing for nested lists */
.prose-writer ul ul,
.prose-writer ol ol,
.prose-writer ul ol,
.prose-writer ol ul {
  margin-top: 0.5em !important;
  margin-bottom: 0.5em !important;
}

/* Enhanced table styling for better readability */
.prose-writer table {
  border-collapse: collapse !important;
  margin: 1.5rem 0 !important;
  width: 100% !important;
}

.prose-writer th,
.prose-writer td {
  border: 1px solid hsl(var(--border) / 0.3) !important;
  padding: 0.75rem !important;
  text-align: left !important;
}

.prose-writer th {
  background: hsl(var(--muted) / 0.3) !important;
  font-weight: 600 !important;
}

/* Better horizontal rule styling */
.prose-writer hr {
  border: none !important;
  height: 2px !important;
  background: linear-gradient(
    90deg,
    transparent,
    hsl(var(--border) / 0.5),
    transparent
  ) !important;
  margin: 2rem 0 !important;
}

/* Improved link styling */
.prose-writer a {
  color: hsl(var(--primary)) !important;
  text-decoration: underline !important;
  text-decoration-color: hsl(var(--primary) / 0.3) !important;
  text-underline-offset: 3px !important;
  transition: all 0.2s ease !important;
}

.prose-writer a:hover {
  text-decoration-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary) / 0.8) !important;
}
