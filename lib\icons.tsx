import type { SVGProps } from 'react';

export const OpenAiIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg strokeLinejoin="round" viewBox="0 0 16 16" {...props}>
    <title>OpenAI</title>
    <path
      d="M14.9449 6.54871C15.3128 5.45919 15.1861 4.26567 14.5978 3.27464C13.7131 1.75461 11.9345 0.972595 10.1974 1.3406C9.42464 0.481584 8.3144 -0.00692594 7.15045 7.42132e-05C5.37487 -0.00392587 3.79946 1.1241 3.2532 2.79113C2.11256 3.02164 1.12799 3.72615 0.551837 4.72468C-0.339497 6.24071 -0.1363 8.15175 1.05451 9.45178C0.686626 10.5413 0.813308 11.7348 1.40162 12.7258C2.28637 14.2459 4.06498 15.0279 5.80204 14.6599C6.5743 15.5189 7.68504 16.0074 8.849 15.9999C10.6256 16.0044 12.2015 14.8754 12.7478 13.2069C13.8884 12.9764 14.873 12.2718 15.4491 11.2733C16.3394 9.75728 16.1357 7.84774 14.9454 6.54771L14.9449 6.54871ZM8.85001 14.9544C8.13907 14.9554 7.45043 14.7099 6.90468 14.2604C6.92951 14.2474 6.97259 14.2239 7.00046 14.2069L10.2293 12.3668C10.3945 12.2743 10.4959 12.1008 10.4949 11.9133V7.42173L11.8595 8.19925C11.8742 8.20625 11.8838 8.22025 11.8858 8.23625V11.9558C11.8838 13.6099 10.5263 14.9509 8.85001 14.9544ZM2.32133 12.2028C1.9651 11.5958 1.8369 10.8843 1.95902 10.1938C1.98284 10.2078 2.02489 10.2333 2.05479 10.2503L5.28366 12.0903C5.44733 12.1848 5.65003 12.1848 5.81421 12.0903L9.75604 9.84429V11.3993C9.75705 11.4153 9.74945 11.4308 9.73678 11.4408L6.47295 13.3004C5.01915 14.1264 3.1625 13.6354 2.32184 12.2028H2.32133ZM1.47155 5.24819C1.82626 4.64017 2.38619 4.17516 3.05305 3.93366C3.05305 3.96116 3.05152 4.00966 3.05152 4.04366V7.72424C3.05051 7.91124 3.15186 8.08475 3.31654 8.17725L7.25838 10.4228L5.89376 11.2003C5.88008 11.2093 5.86285 11.2108 5.84765 11.2043L2.58331 9.34327C1.13255 8.51426 0.63494 6.68272 1.47104 5.24869L1.47155 5.24819ZM12.6834 7.82274L8.74157 5.57669L10.1062 4.79968C10.1199 4.79068 10.1371 4.78918 10.1523 4.79568L13.4166 6.65522C14.8699 7.48373 15.3681 9.31827 14.5284 10.7523C14.1732 11.3593 13.6138 11.8243 12.9474 12.0663V8.27575C12.9489 8.08875 12.8481 7.91574 12.6839 7.82274H12.6834ZM14.0414 5.8057C14.0176 5.7912 13.9756 5.7662 13.9457 5.7492L10.7168 3.90916C10.5531 3.81466 10.3504 3.81466 10.1863 3.90916L6.24442 6.15521V4.60017C6.2434 4.58417 6.251 4.56867 6.26367 4.55867L9.52751 2.70063C10.9813 1.87311 12.84 2.36563 13.6781 3.80066C14.0323 4.40667 14.1605 5.11618 14.0404 5.8057H14.0414ZM5.50257 8.57726L4.13744 7.79974C4.12275 7.79274 4.11312 7.77874 4.11109 7.76274V4.04316C4.11211 2.38713 5.47368 1.0451 7.15197 1.0461C7.86189 1.0461 8.54902 1.2921 9.09476 1.74011C9.06993 1.75311 9.02737 1.77661 8.99899 1.79361L5.77012 3.63365C5.60493 3.72615 5.50358 3.89916 5.50459 4.08666L5.50257 8.57626V8.57726ZM6.24391 7.00022L7.99972 5.9997L9.75553 6.99972V9.00027L7.99972 10.0003L6.24391 9.00027V7.00022Z"
      fill="currentColor"
    />
  </svg>
);

export const XaiIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000" {...props}>
    <title>x.ai</title>
    <g>
      <polygon
        fill="currentColor"
        points="226.83 411.15 501.31 803.15 623.31 803.15 348.82 411.15 226.83 411.15"
      />
      <polygon
        fill="currentColor"
        points="348.72 628.87 226.69 803.15 348.77 803.15 409.76 716.05 348.72 628.87"
      />
      <polygon
        fill="currentColor"
        points="651.23 196.85 440.28 498.12 501.32 585.29 773.31 196.85 651.23 196.85"
      />
      <polygon
        fill="currentColor"
        points="673.31 383.25 673.31 803.15 773.31 803.15 773.31 240.44 673.31 383.25"
      />
    </g>
  </svg>
);

export const AnthropicIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg strokeLinejoin="round" viewBox="0 0 16 16" {...props}>
    <title>Anthropic</title>
    <g transform="translate(0,2)">
      <path
        d="M11.375 0h-2.411L13.352 11.13h2.411L11.375 0ZM4.4 0 0 11.13h2.46l0.9-2.336h4.604l0.9 2.336h2.46L6.924 0H4.4Zm-0.244 6.723 1.506-3.909 1.506 3.909H4.156Z"
        fill="currentColor"
      />
    </g>
  </svg>
);

export const GroqIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg strokeLinejoin="round" viewBox="0 0 160 59" {...props}>
    <title>Groq</title>
    <mask
      id="mask0_4345_1846"
      maskUnits="userSpaceOnUse"
      x="0"
      y="0"
      width="160"
      height="59"
      style={{ maskType: 'luminance' }}
    >
      <path
        d="M0.273438 0.219727H159.216V58.1817H0.273438V0.219727Z"
        fill="white"
      />
    </mask>
    <g mask="url(#mask0_4345_1846)">
      <path
        d="M95.4635 0.314595C84.4822 0.314595 75.5599 9.19525 75.5599 20.1677C75.5599 31.1402 84.4632 40.0208 95.4635 40.0208C106.464 40.0208 115.367 31.1402 115.367 20.1677C115.348 9.21427 106.445 0.333611 95.4635 0.314595ZM95.4635 32.5664C88.6002 32.5664 83.0333 27.0136 83.0333 20.1677C83.0333 13.3218 88.6002 7.76902 95.4635 7.76902C102.327 7.76902 107.894 13.3218 107.894 20.1677C107.894 27.0136 102.327 32.5664 95.4635 32.5664ZM67.9912 0.39066C67.3049 0.314595 66.6376 0.276562 65.9513 0.276562C65.6081 0.276562 65.284 0.276562 64.9599 0.295578C64.6358 0.314595 64.2926 0.333611 63.9685 0.352628C62.634 0.44771 61.2995 0.675906 60.0031 1.03722C57.3531 1.74082 54.8556 2.99591 52.7013 4.68836C50.4898 6.43787 48.7358 8.68181 47.5347 11.23C46.9437 12.5041 46.5052 13.8543 46.2193 15.2234C46.0858 15.908 45.9905 16.5926 45.9142 17.2772C45.8952 17.6195 45.857 17.9618 45.857 18.3041L45.838 18.8175V19.293L45.8761 32.5854L45.9142 39.2221H53.3685L53.4067 32.5854L53.4448 19.293V18.5893C53.4448 18.3802 53.4829 18.171 53.4829 17.9618C53.5211 17.5434 53.5973 17.1441 53.6736 16.7257C53.8452 15.9271 54.093 15.1474 54.4362 14.4057C55.1225 12.9225 56.152 11.6293 57.4293 10.6025C58.7639 9.53755 60.3081 8.75787 61.9477 8.3205C62.7865 8.0923 63.6635 7.94017 64.5405 7.8641C64.7693 7.84509 64.979 7.82607 65.2078 7.82607C65.4365 7.82607 65.6653 7.80705 65.875 7.80705C66.2944 7.80705 66.7329 7.82607 67.1524 7.8641C68.8491 8.03525 70.4887 8.54869 71.9948 9.38541L75.7124 2.93886C73.3484 1.56968 70.7175 0.694923 67.9912 0.39066ZM20.3484 0.219513C9.36711 0.124431 0.36855 8.92902 0.273226 19.8825C0.177902 30.8359 9.00488 39.8116 19.9862 39.9067H26.8876V32.4713H20.3484C13.4851 32.5474 7.84193 27.0707 7.76567 20.2057C7.68941 13.3408 13.1801 7.73099 20.0624 7.65492H20.3484C27.2117 7.65492 32.7786 13.2077 32.8167 20.0536V38.3284C32.8167 45.1172 27.2689 50.651 20.4819 50.7271C17.2218 50.708 14.1142 49.3959 11.8265 47.0949L6.54553 52.3625C10.206 56.0326 15.1628 58.1244 20.3484 58.1625H20.6153C31.4632 58.0103 40.1757 49.2248 40.2329 38.4044V19.5592C39.966 8.81492 31.1391 0.238529 20.3484 0.219513ZM139.389 0.314595C128.407 0.314595 119.485 9.19525 119.504 20.1677C119.504 31.1212 128.407 40.0018 139.389 40.0018H146.195V32.5664H139.389C132.525 32.5664 126.958 27.0136 126.958 20.1677C126.958 13.3218 132.525 7.76902 139.389 7.76902C145.833 7.76902 151.209 12.6943 151.781 19.1028H151.762V57.2116H159.216V20.1677C159.216 9.21427 150.351 0.314595 139.389 0.314595Z"
        fill="currentColor"
      />
    </g>
  </svg>
);

export const GoogleIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" {...props}>
    <title>Google</title>
    <path
      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
      fill="#4285F4"
    />
    <path
      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
      fill="#34A853"
    />
    <path
      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
      fill="#FBBC05"
    />
    <path
      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
      fill="#EA4335"
    />
    <path d="M1 1h22v22H1z" fill="none" />
  </svg>
);

export const MistralIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    viewBox="0 0 176 162"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <title>Mistral</title>
    <rect
      x="15"
      y="1"
      width="32"
      height="32"
      fill="#FFCD00"
      stroke="#636363"
      strokeOpacity="0.2"
      strokeWidth="0.5"
    />
    <rect
      x="143"
      y="1"
      width="32"
      height="32"
      fill="#FFCD00"
      stroke="#636363"
      strokeOpacity="0.2"
      strokeWidth="0.5"
    />
    <rect
      x="15"
      y="33"
      width="32"
      height="32"
      fill="#FFA400"
      stroke="#636363"
      strokeOpacity="0.2"
      strokeWidth="0.5"
    />
    <rect
      x="47"
      y="33"
      width="32"
      height="32"
      fill="#FFA400"
      stroke="#636363"
      strokeOpacity="0.2"
      strokeWidth="0.5"
    />
    <rect
      x="111"
      y="33"
      width="32"
      height="32"
      fill="#FFA400"
      stroke="#636363"
      strokeOpacity="0.2"
      strokeWidth="0.5"
    />
    <rect
      x="143"
      y="33"
      width="32"
      height="32"
      fill="#FFA400"
      stroke="#636363"
      strokeOpacity="0.2"
      strokeWidth="0.5"
    />
    <rect
      x="15"
      y="65"
      width="32"
      height="32"
      fill="#FF7100"
      stroke="#636363"
      strokeOpacity="0.2"
      strokeWidth="0.5"
    />
    <rect
      x="47"
      y="65"
      width="32"
      height="32"
      fill="#FF7100"
      stroke="#636363"
      strokeOpacity="0.2"
      strokeWidth="0.5"
    />
    <rect
      x="79"
      y="65"
      width="32"
      height="32"
      fill="#FF7100"
      stroke="#636363"
      strokeOpacity="0.2"
      strokeWidth="0.5"
    />
    <rect
      x="111"
      y="65"
      width="32"
      height="32"
      fill="#FF7100"
      stroke="#636363"
      strokeOpacity="0.2"
      strokeWidth="0.5"
    />
    <rect
      x="143"
      y="65"
      width="32"
      height="32"
      fill="#FF7100"
      stroke="#636363"
      strokeOpacity="0.2"
      strokeWidth="0.5"
    />
    <rect
      x="15"
      y="97"
      width="32"
      height="32"
      fill="#FF4902"
      stroke="#636363"
      strokeOpacity="0.2"
      strokeWidth="0.5"
    />
    <rect
      x="79"
      y="97"
      width="32"
      height="32"
      fill="#FF4902"
      stroke="#636363"
      strokeOpacity="0.2"
      strokeWidth="0.5"
    />
    <rect
      x="143"
      y="97"
      width="32"
      height="32"
      fill="#FF4902"
      stroke="#636363"
      strokeOpacity="0.2"
      strokeWidth="0.5"
    />
    <rect
      x="15"
      y="129"
      width="32"
      height="32"
      fill="#FF0006"
      stroke="#636363"
      strokeOpacity="0.2"
      strokeWidth="0.5"
    />
    <rect
      x="143"
      y="129"
      width="32"
      height="32"
      fill="#FF0006"
      stroke="#636363"
      strokeOpacity="0.2"
      strokeWidth="0.5"
    />
    <rect y="1" width="16" height="160" fill="black" />
    <rect x="63" y="97" width="16" height="32" fill="black" />
    <rect x="95" y="33" width="16" height="32" fill="black" />
    <rect x="127" y="1" width="16" height="32" fill="black" />
    <rect x="127" y="97" width="16" height="64" fill="black" />
  </svg>
);

export const DeepSeekIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    viewBox="0 0 57 42"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <title>DeepSeek</title>
    <path
      d="M55.6128 3.47133C55.0175 3.17963 54.7611 3.73553 54.413 4.01793C54.2939 4.10903 54.1932 4.22743 54.0924 4.33673C53.2223 5.26593 52.2057 5.87653 50.8776 5.80353C48.9359 5.69413 47.2781 6.30473 45.8126 7.78983C45.5012 5.95853 44.4663 4.86503 42.8909 4.16363C42.0667 3.79913 41.2332 3.43463 40.6561 2.64193C40.2531 2.07723 40.1432 1.44853 39.9417 0.828932C39.8134 0.455432 39.6852 0.072632 39.2547 0.008832C38.7876 -0.063968 38.6044 0.327632 38.4212 0.655832C37.6885 1.99523 37.4046 3.47123 37.432 4.96543C37.4961 8.32753 38.9158 11.006 41.7367 12.9103C42.0573 13.129 42.1397 13.3475 42.039 13.6666C41.8466 14.3226 41.6176 14.9603 41.4162 15.6163C41.2879 16.0355 41.0955 16.1266 40.6468 15.9442C39.0989 15.2975 37.7616 14.3407 36.5801 13.1837C34.5743 11.243 32.7608 9.10193 30.4986 7.42543C29.9674 7.03363 29.4361 6.66933 28.8865 6.32293C26.5784 4.08173 29.1888 2.24113 29.7933 2.02263C30.4252 1.79483 30.0131 1.01113 27.9706 1.02043C25.9281 1.02943 24.0597 1.71283 21.6784 2.62393C21.3304 2.76063 20.9639 2.86073 20.5884 2.94273C18.4269 2.53283 16.1829 2.44153 13.8382 2.70593C9.4235 3.19783 5.8974 5.28433 3.3054 8.84683C0.191404 13.129 -0.541296 17.9942 0.356304 23.0692C1.2997 28.4173 4.029 32.8453 8.2239 36.3077C12.5745 39.8973 17.5845 41.6558 23.2997 41.3187C26.771 41.1183 30.6361 40.6537 34.9958 36.9637C36.0948 37.5104 37.2489 37.7289 39.1632 37.8929C40.6378 38.0296 42.0575 37.8201 43.1565 37.5924C44.8784 37.2279 44.7594 35.6334 44.1366 35.3419C39.09 32.9913 40.1981 33.9479 39.1907 33.1734C41.7552 30.1395 45.6204 26.9869 47.1316 16.7733C47.2506 15.9625 47.1499 15.4522 47.1316 14.7962C47.1224 14.3954 47.214 14.2406 47.672 14.1949C48.9359 14.0491 50.1632 13.703 51.2898 13.0834C54.5596 11.2977 55.8784 8.36393 56.1898 4.84703C56.2357 4.30943 56.1807 3.75353 55.6128 3.47133ZM27.119 35.1231C22.2281 31.2784 19.856 30.0118 18.8759 30.0665C17.96 30.1212 18.1249 31.169 18.3263 31.8524C18.537 32.5265 18.8118 32.9913 19.1964 33.5834C19.462 33.9752 19.6453 34.5582 18.9309 34.9957C17.3555 35.9706 14.6169 34.6676 14.4886 34.6039C11.3014 32.7269 8.6361 30.2486 6.7584 26.8595C4.9449 23.5975 3.8917 20.099 3.7176 16.3634C3.6718 15.4615 3.9374 15.1424 4.835 14.9786C6.0165 14.7599 7.2347 14.7142 8.4162 14.8873C13.408 15.6163 17.6577 17.8485 21.2205 21.3836C23.2538 23.3971 24.7925 25.8026 26.3771 28.1532C28.0623 30.6495 29.8758 33.0277 32.1839 34.9774C32.999 35.6607 33.6493 36.18 34.2721 36.5628C32.3946 36.7723 29.2622 36.818 27.119 35.1231ZM29.4637 20.0443C29.4637 19.6434 29.7843 19.3246 30.1874 19.3246C30.279 19.3246 30.3614 19.3426 30.4347 19.3699C30.5355 19.4065 30.6271 19.4612 30.7003 19.543C30.8286 19.6707 30.9018 19.8528 30.9018 20.0442C30.9018 20.4451 30.5813 20.7639 30.1784 20.7639C29.7755 20.7639 29.4637 20.4452 29.4637 20.0443ZM36.7452 23.7799C36.2781 23.9713 35.811 24.1351 35.3622 24.1534C34.6661 24.1898 33.9059 23.9073 33.4938 23.5611C32.8527 23.0235 32.3947 22.723 32.2024 21.7845C32.1199 21.3836 32.1657 20.764 32.2391 20.4088C32.4039 19.6434 32.2207 19.1515 31.6804 18.7049C31.2407 18.3404 30.682 18.2403 30.0683 18.2403C29.8393 18.2403 29.6288 18.14 29.473 18.058C29.2165 17.9305 29.006 17.6116 29.2074 17.2198C29.2715 17.0924 29.583 16.7825 29.6563 16.7279C30.4898 16.254 31.4515 16.409 32.3399 16.7643C33.1643 17.1014 33.7871 17.721 34.6846 18.5956C35.6005 19.6524 35.7653 19.9442 36.2874 20.7367C36.6997 21.3563 37.0752 21.994 37.3316 22.723C37.4873 23.1786 37.2856 23.5521 36.7452 23.7799Z"
      fill="#4D6BFE"
    />
  </svg>
);

export const CerebrasIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    viewBox="0 0 260 260"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <title>Cerebras</title>
    <path
      d="M130 260C201.797 260 260 201.797 260 130C260 58.203 201.797 0 130 0C58.203 0 0 58.203 0 130C0 201.797 58.203 260 130 260Z"
      fill="#F1592A"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M129.937 57.4319C110.824 57.4319 92.493 65.0247 78.9777 78.5399C65.4625 92.0551 57.8697 110.386 57.8697 129.499C57.8697 148.613 65.4625 166.943 78.9777 180.458C92.493 193.974 110.824 201.566 129.937 201.566V212.424C84.1345 212.424 47.012 175.294 47.012 129.491C47.012 83.6889 84.1267 46.5664 129.929 46.5664V57.4242L129.937 57.4319ZM166.765 85.9287C161.039 81.068 154.409 77.3872 147.255 75.0979C140.102 72.8087 132.567 71.9561 125.083 72.5892C117.599 73.2223 110.314 75.3286 103.646 78.787C96.9793 82.2454 91.0616 86.9877 86.2336 92.7411C81.4056 98.4946 77.7626 105.146 75.5141 112.312C73.2655 119.478 72.4558 127.018 73.1315 134.499C73.8072 141.979 75.9549 149.252 79.4512 155.899C82.9475 162.547 87.7234 168.437 93.5042 173.232L86.5215 181.556C79.6947 175.83 74.0629 168.814 69.9476 160.911C65.8322 153.008 63.314 144.372 62.5368 135.495C61.7595 126.619 62.7385 117.676 65.4177 109.178C68.0968 100.68 72.4238 92.7931 78.1515 85.9674C83.8785 79.1404 90.8942 73.5084 98.798 69.393C106.702 65.2776 115.339 62.7593 124.216 61.9821C133.093 61.2048 142.036 62.1838 150.535 64.8631C159.034 67.5424 166.921 71.8695 173.748 77.5974L166.765 85.9287ZM149.289 92.4697C139.472 87.3993 128.05 86.4111 117.508 89.7203C106.967 93.0294 98.1583 100.368 93.0005 110.139C87.8426 119.91 86.7526 131.323 89.9675 141.894C93.1824 152.465 100.442 161.338 110.167 166.583L105.083 176.193C92.936 169.532 83.895 158.363 79.9106 145.096C75.9263 131.828 77.318 117.526 83.7855 105.276C90.2531 93.0251 101.278 83.8086 114.481 79.6149C127.684 75.4212 142.006 76.5866 154.357 82.8597L149.289 92.4697ZM129.929 102.676C122.815 102.676 115.993 105.502 110.963 110.533C105.932 115.563 103.106 122.385 103.106 129.499C103.106 136.613 105.932 143.435 110.963 148.466C115.993 153.496 122.815 156.322 129.929 156.322V167.187C119.934 167.187 110.348 163.217 103.28 156.149C96.2117 149.081 92.241 139.495 92.241 129.499C92.241 119.504 96.2117 109.917 103.28 102.85C110.348 95.7816 119.934 91.8109 129.929 91.8109V102.676Z"
      fill="white"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M139.903 120.477C138.748 119.233 137.416 118.165 135.951 117.308C134.674 116.549 133.22 116.143 131.735 116.13C129.759 116.13 127.992 116.494 126.434 117.215C124.928 117.893 123.575 118.869 122.457 120.083C121.338 121.298 120.476 122.726 119.924 124.283C119.35 125.856 119.071 127.507 119.071 129.173C119.071 130.862 119.35 132.505 119.924 134.063C120.481 135.618 121.344 137.045 122.462 138.26C123.581 139.476 124.931 140.454 126.434 141.139C127.984 141.86 129.766 142.224 131.735 142.224C133.409 142.224 134.967 141.867 136.4 141.178C137.857 140.48 139.113 139.434 140.082 138.14L147.274 145.936C146.189 147.021 144.949 147.959 143.538 148.749C140.93 150.204 138.091 151.2 135.145 151.694C133.827 151.896 132.688 152.004 131.735 152.004C128.608 152.024 125.503 151.472 122.574 150.377C119.805 149.353 117.272 147.779 115.127 145.75C112.994 143.712 111.296 141.263 110.136 138.55C108.875 135.587 108.247 132.393 108.291 129.173C108.291 125.732 108.911 122.609 110.136 119.795C111.298 117.083 112.988 114.634 115.119 112.596C117.273 110.573 119.808 109 122.574 107.977C125.503 106.881 128.608 106.33 131.735 106.349C134.486 106.349 137.253 106.876 140.043 107.93C142.841 109 145.352 110.72 147.359 112.96L139.903 120.477Z"
      fill="white"
    />
  </svg>
);

export const AmazonIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    viewBox="0 0 181 108"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <title>Amazon</title>
    <g clipPath="url(#clip0_1480_1723)">
      <path
        d="M51.2203 39.3158C51.2203 41.5066 51.4572 43.2829 51.8717 44.5856C52.3453 45.8882 52.9374 47.3093 53.7664 48.8487C54.0624 49.3224 54.1809 49.7961 54.1809 50.2106C54.1809 50.8027 53.8256 51.3948 53.0559 51.9869L49.3256 54.4737C48.7927 54.829 48.2598 55.0066 47.7861 55.0066C47.194 55.0066 46.6019 54.7106 46.0098 54.1777C45.1809 53.2895 44.4703 52.3422 43.8782 51.3948C43.2861 50.3882 42.694 49.2632 42.0427 47.9014C37.4243 53.3487 31.6217 56.0724 24.6348 56.0724C19.6611 56.0724 15.694 54.6514 12.7927 51.8093C9.8914 48.9672 8.41113 45.1777 8.41113 40.4408C8.41113 35.4079 10.1874 31.3224 13.7993 28.2435C17.4111 25.1645 22.2072 23.6251 28.3059 23.6251C30.319 23.6251 32.3914 23.8027 34.5822 24.0987C36.773 24.3948 39.023 24.8685 41.3914 25.4014V21.079C41.3914 16.579 40.444 13.4408 38.6085 11.6053C36.7138 9.76979 33.5164 8.88163 28.9572 8.88163C26.8848 8.88163 24.7532 9.11847 22.5624 9.65137C20.3717 10.1843 18.2401 10.8356 16.1677 11.6645C15.2203 12.079 14.5098 12.3158 14.0953 12.4343C13.6809 12.5527 13.3848 12.6119 13.148 12.6119C12.319 12.6119 11.9046 12.0198 11.9046 10.7764V7.87505C11.9046 6.92768 12.023 6.21716 12.319 5.80268C12.6151 5.38821 13.148 4.97374 13.9769 4.55926C16.0493 3.49347 18.5361 2.60531 21.4374 1.89479C24.3388 1.12505 27.4177 0.769788 30.6743 0.769788C37.7203 0.769788 42.8717 2.36847 46.1875 5.56584C49.444 8.76321 51.1019 13.6185 51.1019 20.1316V39.3158H51.2203ZM27.1809 48.3158C29.1348 48.3158 31.148 47.9606 33.2796 47.2501C35.4111 46.5395 37.3059 45.2369 38.9046 43.4606C39.8519 42.3356 40.5625 41.0922 40.9177 39.6711C41.273 38.2501 41.5098 36.5329 41.5098 34.5198V32.0329C39.7927 31.6185 37.9572 31.2632 36.0624 31.0264C34.1677 30.7895 32.3322 30.6711 30.4967 30.6711C26.5296 30.6711 23.6282 31.4408 21.6743 33.0395C19.7203 34.6382 18.773 36.8882 18.773 39.8487C18.773 42.6316 19.4835 44.704 20.9638 46.1251C22.3848 47.6053 24.4572 48.3158 27.1809 48.3158ZM74.7269 54.7106C73.6611 54.7106 72.9506 54.5329 72.4769 54.1185C72.0032 53.7632 71.5888 52.9343 71.2335 51.8093L57.319 6.03953C56.9638 4.85531 56.7861 4.08558 56.7861 3.6711C56.7861 2.72374 57.2598 2.19084 58.2072 2.19084H64.0098C65.1348 2.19084 65.9046 2.36847 66.319 2.78295C66.7927 3.13821 67.148 3.96716 67.5032 5.09216L77.4506 44.2895L86.6874 5.09216C86.9835 3.90795 87.3388 3.13821 87.8124 2.78295C88.2861 2.42768 89.1151 2.19084 90.1809 2.19084H94.9177C96.0427 2.19084 96.8124 2.36847 97.2861 2.78295C97.7598 3.13821 98.1743 3.96716 98.4111 5.09216L107.766 44.7632L118.01 5.09216C118.365 3.90795 118.78 3.13821 119.194 2.78295C119.668 2.42768 120.437 2.19084 121.503 2.19084H127.01C127.957 2.19084 128.49 2.66453 128.49 3.6711C128.49 3.96716 128.431 4.26321 128.372 4.61847C128.312 4.97374 128.194 5.44742 127.957 6.09874L113.687 51.8685C113.332 53.0527 112.918 53.8224 112.444 54.1777C111.97 54.5329 111.201 54.7698 110.194 54.7698H105.102C103.977 54.7698 103.207 54.5922 102.733 54.1777C102.26 53.7632 101.845 52.9935 101.608 51.8093L92.4309 13.6185L83.3125 51.7501C83.0164 52.9343 82.6611 53.704 82.1874 54.1185C81.7138 54.5329 80.8848 54.7106 79.819 54.7106H74.7269ZM150.812 56.3093C147.734 56.3093 144.655 55.954 141.694 55.2435C138.733 54.5329 136.424 53.7632 134.885 52.8751C133.937 52.3422 133.286 51.7501 133.049 51.2172C132.812 50.6843 132.694 50.0922 132.694 49.5593V46.5395C132.694 45.2961 133.168 44.704 134.056 44.704C134.411 44.704 134.766 44.7632 135.122 44.8816C135.477 45.0001 136.01 45.2369 136.602 45.4737C138.615 46.3619 140.806 47.0724 143.115 47.5461C145.484 48.0198 147.793 48.2566 150.161 48.2566C153.891 48.2566 156.793 47.6053 158.806 46.3027C160.819 45 161.885 43.1053 161.885 40.6777C161.885 39.0198 161.352 37.6579 160.286 36.5329C159.22 35.4079 157.207 34.4014 154.306 33.454L145.72 30.7895C141.398 29.4277 138.201 27.4145 136.247 24.7501C134.293 22.1448 133.286 19.2435 133.286 16.1645C133.286 13.6777 133.819 11.4869 134.885 9.59216C135.951 7.69742 137.372 6.03953 139.148 4.73689C140.924 3.37505 142.937 2.36847 145.306 1.65795C147.674 0.94742 150.161 0.651367 152.766 0.651367C154.069 0.651367 155.431 0.710578 156.734 0.888209C158.095 1.06584 159.339 1.30268 160.582 1.53953C161.766 1.83558 162.891 2.13163 163.957 2.48689C165.023 2.84216 165.852 3.19742 166.444 3.55268C167.273 4.02637 167.865 4.50005 168.22 5.03295C168.576 5.50663 168.753 6.15795 168.753 6.98689V9.76979C168.753 11.0132 168.28 11.6645 167.391 11.6645C166.918 11.6645 166.148 11.4277 165.141 10.954C161.766 9.41453 157.977 8.64479 153.773 8.64479C150.398 8.64479 147.734 9.17768 145.898 10.3027C144.062 11.4277 143.115 13.1448 143.115 15.5724C143.115 17.2303 143.707 18.6514 144.891 19.7764C146.076 20.9014 148.266 22.0264 151.405 23.0329L159.812 25.6974C164.076 27.0593 167.155 28.954 168.99 31.3816C170.826 33.8093 171.714 36.5922 171.714 39.6711C171.714 42.2172 171.181 44.5264 170.174 46.5395C169.108 48.5527 167.687 50.329 165.852 51.7501C164.016 53.2303 161.826 54.2961 159.28 55.0658C156.615 55.8948 153.832 56.3093 150.812 56.3093Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M162.004 85.0856C142.524 99.4738 114.221 107.112 89.8855 107.112C55.7803 107.112 25.05 94.5001 1.83948 73.5396C0.00394917 71.8817 1.66184 69.6317 3.85263 70.9343C28.9579 85.5001 59.925 94.3225 91.9579 94.3225C113.57 94.3225 137.313 89.8225 159.162 80.5856C162.418 79.1054 165.201 82.7172 162.004 85.0856Z"
        fill="#FF9900"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M170.115 75.8487C167.628 72.6513 153.654 74.3092 147.319 75.079C145.424 75.3158 145.128 73.6579 146.845 72.4145C157.976 64.5987 176.272 66.8487 178.404 69.454C180.536 72.1184 177.812 90.4145 167.391 99.1776C165.792 100.539 164.253 99.8289 164.963 98.0526C167.332 92.1908 172.601 78.9868 170.115 75.8487Z"
        fill="#FF9900"
      />
    </g>
    <defs>
      <clipPath id="clip0_1480_1723">
        <rect
          width="180"
          height="107.763"
          fill="white"
          transform="translate(0.0625)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const FalIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    viewBox="0 0 170 171"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <title>Fal</title>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M109.571 0.690002C112.515 0.690002 114.874 3.08348 115.155 6.01352C117.665 32.149 138.466 52.948 164.603 55.458C167.534 55.7394 169.927 58.0985 169.927 61.042V110.255C169.927 113.198 167.534 115.557 164.603 115.839C138.466 118.349 117.665 139.148 115.155 165.283C114.874 168.213 112.515 170.607 109.571 170.607H60.3553C57.4116 170.607 55.0524 168.213 54.7709 165.283C52.2608 139.148 31.4601 118.349 5.32289 115.839C2.39266 115.557 -0.000976562 113.198 -0.000976562 110.255V61.042C-0.000976562 58.0985 2.39267 55.7394 5.3229 55.458C31.4601 52.948 52.2608 32.149 54.7709 6.01351C55.0524 3.08348 57.4116 0.690002 60.3553 0.690002H109.571ZM34.1182 85.5045C34.1182 113.776 57.0124 136.694 85.2539 136.694C113.495 136.694 136.39 113.776 136.39 85.5045C136.39 57.2332 113.495 34.3147 85.2539 34.3147C57.0124 34.3147 34.1182 57.2332 34.1182 85.5045Z"
      fill="currentColor"
    />
  </svg>
);

export const DeepinfraIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    viewBox="0 0 28 30"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <title>Deepinfra</title>
    <path
      d="M3.1577 9.92081C1.59566 9.92081 0.324219 8.64941 0.324219 7.08741C0.324219 5.52531 1.59566 4.25391 3.1577 4.25391C4.7197 4.25391 5.9911 5.52531 5.9911 7.08741C5.9911 8.64941 4.7197 9.92081 3.1577 9.92081ZM3.1577 5.36641C2.2089 5.36641 1.43673 6.13861 1.43673 7.08901C1.43673 8.03931 2.2089 8.81151 3.1577 8.81151C4.1064 8.81151 4.8802 8.03931 4.8802 7.08901C4.8802 6.13861 4.108 5.36641 3.1577 5.36641Z"
      fill="#2A3275"
    />
    <path
      d="M3.1577 17.913C1.59566 17.913 0.324219 16.6415 0.324219 15.0795C0.324219 13.5175 1.59566 12.2461 3.1577 12.2461C4.7197 12.2461 5.9911 13.5175 5.9911 15.0795C5.9911 16.6415 4.7197 17.913 3.1577 17.913ZM3.1577 13.3586C2.2089 13.3586 1.43673 14.1308 1.43673 15.0795C1.43673 16.0283 2.2089 16.8021 3.1577 16.8021C4.1064 16.8021 4.8802 16.0299 4.8802 15.0795C4.8802 14.1292 4.108 13.3586 3.1577 13.3586Z"
      fill="#2A3275"
    />
    <path
      d="M3.1577 25.9013C1.59566 25.9013 0.324219 24.6298 0.324219 23.0678C0.324219 21.5058 1.59566 20.2344 3.1577 20.2344C4.7197 20.2344 5.9911 21.5058 5.9911 23.0678C5.9911 24.6298 4.7197 25.9013 3.1577 25.9013ZM3.1577 21.3469C2.2089 21.3469 1.43673 22.1191 1.43673 23.0694C1.43673 24.0198 2.2089 24.792 3.1577 24.792C4.1064 24.792 4.8802 24.0198 4.8802 23.0694C4.8802 22.1191 4.108 21.3469 3.1577 21.3469Z"
      fill="#2A3275"
    />
    <path
      d="M24.6997 9.92081C23.1377 9.92081 21.8662 8.64941 21.8662 7.08741C21.8662 5.52531 23.1377 4.25391 24.6997 4.25391C26.2617 4.25391 27.5331 5.52531 27.5331 7.08741C27.5331 8.64941 26.2617 9.92081 24.6997 9.92081ZM24.6997 5.36641C23.7509 5.36641 22.9771 6.13861 22.9771 7.08901C22.9771 8.03931 23.7493 8.81151 24.6997 8.81151C25.65 8.81151 26.4222 8.03931 26.4222 7.08901C26.4222 6.13861 25.65 5.36641 24.6997 5.36641Z"
      fill="#2A3275"
    />
    <path
      d="M24.6997 17.913C23.1377 17.913 21.8662 16.6415 21.8662 15.0795C21.8662 13.5175 23.1377 12.2461 24.6997 12.2461C26.2617 12.2461 27.5331 13.5175 27.5331 15.0795C27.5331 16.6415 26.2617 17.913 24.6997 17.913ZM24.6997 13.3586C23.7509 13.3586 22.9771 14.1308 22.9771 15.0795C22.9771 16.0283 23.7493 16.8021 24.6997 16.8021C25.65 16.8021 26.4222 16.0299 26.4222 15.0795C26.4222 14.1292 25.65 13.3586 24.6997 13.3586Z"
      fill="#2A3275"
    />
    <path
      d="M24.6997 25.9013C23.1377 25.9013 21.8662 24.6298 21.8662 23.0678C21.8662 21.5058 23.1377 20.2344 24.6997 20.2344C26.2617 20.2344 27.5331 21.5058 27.5331 23.0678C27.5331 24.6298 26.2617 25.9013 24.6997 25.9013ZM24.6997 21.3469C23.7509 21.3469 22.9771 22.1191 22.9771 23.0694C22.9771 24.0198 23.7493 24.792 24.6997 24.792C25.65 24.792 26.4222 24.0198 26.4222 23.0694C26.4222 22.1191 25.65 21.3469 24.6997 21.3469Z"
      fill="#2A3275"
    />
    <path
      d="M13.9291 13.9169C12.3671 13.9169 11.0957 12.6455 11.0957 11.0834C11.0957 9.5214 12.3671 8.25 13.9291 8.25C15.4912 8.25 16.7626 9.5214 16.7626 11.0834C16.7626 12.6455 15.4912 13.9169 13.9291 13.9169ZM13.9291 9.3625C12.9804 9.3625 12.2066 10.1347 12.2066 11.0851C12.2066 12.0354 12.9788 12.8076 13.9291 12.8076C14.8795 12.8076 15.6517 12.0354 15.6517 11.0851C15.6517 10.1347 14.8795 9.3625 13.9291 9.3625Z"
      fill="#2A3275"
    />
    <path
      d="M13.9296 5.9286C12.3676 5.9286 11.0962 4.6572 11.0962 3.0952C11.0962 1.5332 12.3676 0.261719 13.9296 0.261719C15.4916 0.261719 16.7631 1.5332 16.7631 3.0952C16.7631 4.6572 15.4916 5.9286 13.9296 5.9286ZM13.9296 1.3742C12.9809 1.3742 12.2071 2.1464 12.2071 3.0968C12.2071 4.0471 12.9793 4.8193 13.9296 4.8193C14.88 4.8193 15.6522 4.0471 15.6522 3.0968C15.6522 2.1464 14.88 1.3742 13.9296 1.3742Z"
      fill="#2A3275"
    />
    <path
      d="M13.9296 21.9091C12.3676 21.9091 11.0962 20.6376 11.0962 19.0756C11.0962 17.5136 12.3676 16.2422 13.9296 16.2422C15.4916 16.2422 16.7631 17.5136 16.7631 19.0756C16.7631 20.6376 15.4916 21.9091 13.9296 21.9091ZM13.9296 17.3547C12.9809 17.3547 12.2071 18.1269 12.2071 19.0772C12.2071 20.0276 12.9793 20.7998 13.9296 20.7998C14.88 20.7998 15.6522 20.0276 15.6522 19.0772C15.6522 18.1269 14.88 17.3547 13.9296 17.3547Z"
      fill="#2A3275"
    />
    <path
      d="M13.9291 29.9013C12.3671 29.9013 11.0957 28.6298 11.0957 27.0678C11.0957 25.5058 12.3671 24.2344 13.9291 24.2344C15.4912 24.2344 16.7626 25.5058 16.7626 27.0678C16.7626 28.6298 15.4912 29.9013 13.9291 29.9013ZM13.9291 25.3469C12.9804 25.3469 12.2066 26.1191 12.2066 27.0694C12.2066 28.0198 12.9788 28.792 13.9291 28.792C14.8795 28.792 15.6517 28.0198 15.6517 27.0694C15.6517 26.1191 14.8795 25.3469 13.9291 25.3469Z"
      fill="#2A3275"
    />
    <path
      d="M9.417 10.4154C9.3031 10.4154 9.1875 10.3945 9.0751 10.3495L7.2305 9.61428C6.7554 9.42488 6.5242 8.88548 6.7136 8.41188C6.9031 7.93668 7.4408 7.70388 7.9176 7.89498L9.7622 8.63018C10.2374 8.81958 10.4685 9.35898 10.2791 9.83258C10.1346 10.1954 9.7863 10.4154 9.4186 10.4154H9.417Z"
      fill="#5699DB"
    />
    <path
      d="M9.417 18.2506C9.3031 18.2506 9.1875 18.2298 9.0751 18.1848L7.2305 17.4496C6.7554 17.2601 6.5242 16.7207 6.7136 16.2471C6.9031 15.772 7.4408 15.5408 7.9176 15.7302L9.7622 16.4655C10.2374 16.6549 10.4685 17.1943 10.2791 17.6679C10.1346 18.0307 9.7863 18.2506 9.4186 18.2506H9.417Z"
      fill="#5699DB"
    />
    <path
      d="M20.2373 14.3366C20.1234 14.3366 20.0078 14.3157 19.8954 14.2707L18.0509 13.5355C17.5757 13.3461 17.3445 12.8067 17.5339 12.3331C17.7234 11.8579 18.2612 11.6267 18.7363 11.8162L20.5809 12.5514C21.0561 12.7408 21.2872 13.2802 21.0978 13.7538C20.9533 14.1166 20.605 14.3366 20.2373 14.3366Z"
      fill="#5699DB"
    />
    <path
      d="M20.2373 6.49741C20.1234 6.49741 20.0078 6.47651 19.8954 6.43161L18.0509 5.69631C17.5757 5.50691 17.3445 4.96751 17.5339 4.49391C17.7234 4.01871 18.2612 3.78591 18.7363 3.97701L20.5809 4.71221C21.0561 4.90171 21.2872 5.44111 21.0978 5.91461C20.9533 6.27751 20.605 6.49741 20.2373 6.49741Z"
      fill="#5699DB"
    />
    <path
      d="M20.2369 22.1693C20.1229 22.1693 20.0073 22.1484 19.8949 22.1034L18.0504 21.3682C17.5752 21.1788 17.344 20.6394 17.5334 20.1658C17.7229 19.6906 18.2607 19.4578 18.7359 19.6489L20.5804 20.3841C21.0556 20.5735 21.2868 21.1129 21.0973 21.5865C20.9528 21.9493 20.6045 22.1693 20.2369 22.1693Z"
      fill="#5699DB"
    />
    <path
      d="M7.6224 22.4498C7.2548 22.4498 6.9064 22.2299 6.762 21.8671C6.5725 21.3919 6.8037 20.8541 7.2789 20.6647L9.1234 19.9294C9.5986 19.74 10.1364 19.9712 10.3274 20.4464C10.5169 20.9216 10.2857 21.4593 9.8105 21.6488L7.966 22.384C7.8536 22.429 7.738 22.4498 7.624 22.4498H7.6224Z"
      fill="#5699DB"
    />
    <path
      d="M7.6224 14.6608C7.2548 14.6608 6.9064 14.4408 6.762 14.078C6.5725 13.6029 6.8037 13.0651 7.2789 12.8756L9.1234 12.1404C9.5986 11.9509 10.1364 12.1821 10.3274 12.6573C10.5169 13.1325 10.2857 13.6703 9.8105 13.8597L7.966 14.595C7.8536 14.6399 7.738 14.6608 7.624 14.6608H7.6224Z"
      fill="#5699DB"
    />
    <path
      d="M7.6224 6.47718C7.2548 6.47718 6.9064 6.25728 6.762 5.89438C6.5725 5.41928 6.8037 4.88148 7.2789 4.69198L9.1234 3.95678C9.5986 3.76738 10.1364 3.99848 10.3274 4.47368C10.5169 4.94888 10.2857 5.48668 9.8105 5.67608L7.966 6.41138C7.8536 6.45628 7.738 6.47718 7.624 6.47718H7.6224Z"
      fill="#5699DB"
    />
    <path
      d="M9.417 26.6456C9.3031 26.6456 9.1875 26.6247 9.0751 26.5798L7.2305 25.8445C6.7554 25.6551 6.5242 25.1157 6.7136 24.6421C6.9031 24.1686 7.4408 23.9342 7.916 24.1252L9.7606 24.8605C10.2358 25.0499 10.4669 25.5893 10.2775 26.0629C10.133 26.4257 9.7847 26.6456 9.417 26.6456Z"
      fill="#5699DB"
    />
    <path
      d="M18.3925 10.4163C18.0248 10.4163 17.6765 10.1964 17.532 9.83358C17.3426 9.35838 17.5737 8.81898 18.0489 8.62948L19.8935 7.89428C20.3686 7.70488 20.908 7.93598 21.0975 8.41118C21.2869 8.88638 21.0557 9.42578 20.5805 9.61518L18.736 10.3505C18.6236 10.3954 18.508 10.4163 18.3941 10.4163H18.3925Z"
      fill="#5699DB"
    />
    <path
      d="M18.3925 18.2522C18.0248 18.2522 17.6765 18.0323 17.532 17.6695C17.3426 17.1943 17.5737 16.6549 18.0489 16.4655L19.8935 15.7302C20.3686 15.5408 20.908 15.772 21.0975 16.2471C21.2869 16.7223 21.0557 17.2617 20.5805 17.4512L18.736 18.1864C18.6236 18.2314 18.508 18.2522 18.3941 18.2522H18.3925Z"
      fill="#5699DB"
    />
    <path
      d="M18.3925 26.6452C18.0248 26.6452 17.6765 26.4236 17.532 26.0624C17.3426 25.5872 17.5737 25.0494 18.0489 24.86L19.8935 24.1248C20.3686 23.9353 20.9064 24.1665 21.0959 24.6417C21.2853 25.1169 21.0541 25.6547 20.5789 25.8441L18.7344 26.5793C18.622 26.6243 18.5064 26.6452 18.3925 26.6452Z"
      fill="#5699DB"
    />
  </svg>
);

export const TogetherIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" {...props}>
    <title>Together</title>
    <circle cx="12" cy="12" r="10" fill="#0f6fff" />
  </svg>
);

export const FireworksIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" {...props}>
    <title>Fireworks</title>
    <path
      clipRule="evenodd"
      d="M14.8 5l-2.801 6.795L9.195 5H7.397l3.072 7.428a1.64 1.64 0 003.038.002L16.598 5H14.8zm1.196 10.352l5.124-5.244-.699-1.669-5.596 5.739a1.664 1.664 0 00-.343 1.807 1.642 1.642 0 001.516 1.012L16 17l8-.02-.699-1.669-7.303.041h-.002zM2.88 10.104l.699-1.669 5.596 5.739c.468.479.603 1.189.343 1.807a1.643 1.643 0 01-1.516 1.012l-8-.018-.002.002.699-1.669 7.303.042-5.122-5.246z"
      fill="#5019C5"
      fillRule="evenodd"
    />
  </svg>
);

export const LumaIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    viewBox="0 0 121 141"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <title>Luma</title>
    <path
      d="M0 35.6017L60.5 0.607422V140.607L0 105.606V35.6017Z"
      fill="currentColor"
      fillOpacity="0.3"
    />
    <path
      d="M60.5 140.598L0 105.596L60.5 70.594L121 105.596L60.5 140.598Z"
      fill="currentColor"
      fillOpacity="0.3"
    />
    <path
      d="M60.5 140.598L0 105.596L60.5 70.594L121 105.596L60.5 140.598Z"
      fill="currentColor"
      fillOpacity="0.3"
    />
    <path
      d="M0 35.6017L60.5 0.607422V140.607L0 105.606V35.6017Z"
      fill="currentColor"
      fillOpacity="0.3"
    />
    <path
      d="M60.5 140.598L0 105.596L60.5 70.594L121 105.596L60.5 140.598Z"
      fill="currentColor"
      fillOpacity="0.3"
    />
    <path
      d="M0 35.6017L60.5 0.607422V140.607L0 105.606V35.6017Z"
      fill="currentColor"
      fillOpacity="0.3"
    />
  </svg>
);

export const ReplicateIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    viewBox="0 0 1000 1000"
    fill="currentColor"
    xmlSpace="preserve"
    {...props}
  >
    <title>Replicate</title>
    <g>
      <polygon points="1000,427.6 1000,540.6 603.4,540.6 603.4,1000 477,1000 477,427.6" />
      <polygon points="1000,213.8 1000,327 364.8,327 364.8,1000 238.4,1000 238.4,213.8" />
      <polygon points="1000,0 1000,113.2 126.4,113.2 126.4,1000 0,1000 0,0" />
    </g>
  </svg>
);

export const HumeIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    viewBox="0 0 32 31"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <title>Hume</title>
    <path
      d="M2.26452 16.1407C0.682903 16.5034 -0.301313 17.9975 0.083346 19.5988C0.446515 21.2002 1.98515 22.1404 3.54528 21.7775C5.12688 21.4148 6.08961 19.9207 5.72645 18.3194C5.36328 16.716 3.82464 15.7779 2.26452 16.1407Z"
      fill="currentColor"
    />
    <path
      d="M3.63026 10.0987C4.89168 11.1033 6.66454 10.9315 7.69174 9.65004C8.71677 8.36855 8.48255 6.59765 7.24262 5.59306C6.00268 4.58848 4.20616 4.76019 3.18113 6.04168C2.15609 7.32317 2.39033 9.09409 3.63026 10.0987Z"
      fill="currentColor"
    />
    <path
      d="M11.2837 25.2792C9.8095 24.5751 8.12045 25.1719 7.41559 26.6251C6.71074 28.0762 7.28666 29.7633 8.76297 30.4889C10.2371 31.193 11.9262 30.5961 12.6311 29.143C13.3144 27.6705 12.76 25.9833 11.2837 25.2792Z"
      fill="currentColor"
    />
    <path
      d="M20.175 25.2779C18.7008 25.9821 18.1228 27.6907 18.8275 29.1418C19.5324 30.5928 21.2 31.2132 22.6957 30.4876C24.1698 29.7835 24.7479 28.0749 24.043 26.6238C23.3382 25.1727 21.6513 24.5524 20.175 25.2779Z"
      fill="currentColor"
    />
    <path
      d="M29.1976 16.1405C27.616 15.7778 26.0988 16.718 25.7357 18.3192C25.3725 19.9206 26.3353 21.4361 27.9169 21.7774C29.4984 22.1402 31.0156 21.2 31.3788 19.5987C31.7419 17.9973 30.7793 16.5033 29.1976 16.1405Z"
      fill="currentColor"
    />
    <path
      d="M28.2351 10.1002C29.4966 9.09564 29.7093 7.32473 28.6843 6.04323C27.6593 4.76174 25.8843 4.59216 24.6228 5.59461C23.3615 6.59919 23.1486 8.37009 24.1737 9.65158C25.2009 10.9331 26.9738 11.1027 28.2351 10.1002Z"
      fill="currentColor"
    />
    <path
      d="M15.7295 0C14.062 0 12.822 1.28148 12.822 2.90428C12.822 4.52707 14.062 5.80857 15.7295 5.80857C17.3757 5.80857 18.637 4.52707 18.637 2.90428C18.6349 1.27934 17.3757 0 15.7295 0Z"
      fill="currentColor"
    />
  </svg>
);

export const LmntIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    viewBox="0 0 250.8 248.87"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    {...props}
  >
    <title>LMNT</title>
    <clipPath id="a">
      <path d="m123 246h3v2.871094h-3zm0 0" />
    </clipPath>
    <clipPath id="b">
      <path d="m245 123h5.800781v6h-5.800781zm0 0" />
    </clipPath>
    <path
      d="m248.613281 131.320313c0 .585937-.476562 1.058593-1.058593 1.058593-.585938 0-1.0625-.472656-1.0625-1.058593 0-.585938.476562-1.058594 1.0625-1.058594.582031 0 1.058593.472656 1.058593 1.058594"
      fill="#1267ac"
    />
    <path
      d="m249 140.492188c0 1.429687-1.160156 2.585937-2.589844 2.585937-1.429687 0-2.585937-1.15625-2.585937-2.585937 0-1.429688 1.15625-2.589844 2.585937-2.589844 1.429688 0 2.589844 1.160156 2.589844 2.589844"
      fill="#1267ac"
    />
    <path
      d="m247.929688 149.34375c0 1.402344-1.136719 2.539063-2.539063 2.539063s-2.539062-1.136719-2.539062-2.539063 1.136718-2.539062 2.539062-2.539062 2.539063 1.136718 2.539063 2.539062"
      fill="#1267ac"
    />
    <path
      d="m241.691406 168.058594c0 1.160156-.945312 2.101562-2.105468 2.101562-1.164063 0-2.105469-.941406-2.105469-2.101562 0-1.164063.941406-2.105469 2.105469-2.105469 1.160156 0 2.105468.941406 2.105468 2.105469"
      fill="#1267ac"
    />
    <path
      d="m244.609375 160.269531c0 .957032-.777344 1.734375-1.734375 1.734375s-1.734375-.777343-1.734375-1.734375c0-.957031.777344-1.734375 1.734375-1.734375s1.734375.777344 1.734375 1.734375"
      fill="#1267ac"
    />
    <path
      d="m242.277344 164.144531c0 .671875-.542969 1.214844-1.214844 1.214844s-1.21875-.542969-1.21875-1.214844.546875-1.21875 1.21875-1.21875 1.214844.546875 1.214844 1.21875"
      fill="#1267ac"
    />
    <path
      d="m238.511719 175.816406c0 .9375-.761719 1.695313-1.699219 1.695313s-1.699219-.757813-1.699219-1.695313c0-.941406.761719-1.699218 1.699219-1.699218s1.699219.757812 1.699219 1.699218"
      fill="#1267ac"
    />
    <path
      d="m237.140625 179.828125c0 1.128906-.914062 2.042969-2.042969 2.042969-1.128906 0-2.042968-.914063-2.042968-2.042969 0-1.125.914062-2.039062 2.042968-2.039062 1.128907 0 2.042969.914062 2.042969 2.039062"
      fill="#1267ac"
    />
    <path
      d="m225.3125 199.691406c0 1.570313-1.273437 2.839844-2.84375 2.839844-1.566406 0-2.839844-1.269531-2.839844-2.839844 0-1.570312 1.273438-2.839843 2.839844-2.839843 1.570313 0 2.84375 1.269531 2.84375 2.839843"
      fill="#1267ac"
    />
    <path
      d="m217.546875 207.195313c0 1.039062-.84375 1.882812-1.878906 1.882812-1.039063 0-1.878906-.84375-1.878906-1.882812 0-1.035157.839843-1.878907 1.878906-1.878907 1.035156 0 1.878906.84375 1.878906 1.878907"
      fill="#1267ac"
    />
    <path
      d="m212.867188 212.15625c0 1.191406-.96875 2.160156-2.160157 2.160156-1.191406 0-2.160156-.96875-2.160156-2.160156s.96875-2.160156 2.160156-2.160156c1.191407 0 2.160157.96875 2.160157 2.160156"
      fill="#1267ac"
    />
    <path
      d="m199.390625 223.316406c0 .765625-.617187 1.386719-1.382812 1.386719s-1.382813-.621094-1.382813-1.386719c0-.761718.617188-1.382812 1.382813-1.382812s1.382812.621094 1.382812 1.382812"
      fill="#1267ac"
    />
    <path
      d="m188.953125 230.851563c0 1.542968-1.25 2.792968-2.796875 2.792968-1.542969 0-2.792969-1.25-2.792969-2.792968 0-1.542969 1.25-2.796875 2.792969-2.796875 1.546875 0 2.796875 1.253906 2.796875 2.796875"
      fill="#1267ac"
    />
    <path
      d="m176.804688 236.617188c0 .847656-.6875 1.539062-1.539063 1.539062-.851562 0-1.539062-.691406-1.539062-1.539062 0-.851563.6875-1.542969 1.539062-1.542969.851563 0 1.539063.691406 1.539063 1.542969"
      fill="#1267ac"
    />
    <path
      d="m161.429688 242.152344c0 .910156-.738282 1.652344-1.652344 1.652344-.910156 0-1.648438-.742188-1.648438-1.652344 0-.914063.738282-1.652344 1.648438-1.652344.914062 0 1.652344.738281 1.652344 1.652344"
      fill="#1267ac"
    />
    <path
      d="m138.378906 246.300781c0 .890625-.722656 1.613282-1.613281 1.613282s-1.613281-.722657-1.613281-1.613282.722656-1.613281 1.613281-1.613281 1.613281.722656 1.613281 1.613281"
      fill="#1267ac"
    />
    <g clipPath="url(#a)">
      <path
        d="m125.789063 247.484375c0 .765625-.617188 1.386719-1.386719 1.386719-.765625 0-1.386719-.621094-1.386719-1.386719s.621094-1.386719 1.386719-1.386719c.769531 0 1.386719.621094 1.386719 1.386719"
        fill="#1267ac"
      />
    </g>
    <path
      d="m105.589844 245.132813c0 .878906-.714844 1.59375-1.59375 1.59375-.882813 0-1.59375-.714844-1.59375-1.59375 0-.882813.710937-1.59375 1.59375-1.59375.878906 0 1.59375.710937 1.59375 1.59375"
      fill="#1267ac"
    />
    <path
      d="m96.121094 243.050781c0 1.375-1.113281 2.492188-2.488281 2.492188-1.371094 0-2.488282-1.117188-2.488282-2.492188 0-1.371093 1.117188-2.488281 2.488282-2.488281 1.375 0 2.488281 1.117188 2.488281 2.488281"
      fill="#1267ac"
    />
    <path
      d="m86.9375 239.785156c0 1.351563-1.09375 2.445313-2.445312 2.445313-1.351563 0-2.449219-1.09375-2.449219-2.445313 0-1.351562 1.097656-2.449218 2.449219-2.449218 1.351562 0 2.445312 1.097656 2.445312 2.449218"
      fill="#1267ac"
    />
    <path
      d="m80 237.359375c0 1.265625-1.027344 2.292969-2.296875 2.292969-1.265625 0-2.292969-1.027344-2.292969-2.292969 0-1.269531 1.027344-2.296875 2.292969-2.296875 1.269531 0 2.296875 1.027344 2.296875 2.296875"
      fill="#1267ac"
    />
    <path
      d="m59.855469 226.308594c0 1.253906-1.015625 2.269531-2.265625 2.269531-1.253906 0-2.269531-1.015625-2.269531-2.269531 0-1.25 1.015625-2.269531 2.269531-2.269531 1.25 0 2.265625 1.019531 2.265625 2.269531"
      fill="#1267ac"
    />
    <path
      d="m51.746094 221.5c0 .949219-.769531 1.71875-1.71875 1.71875-.953125 0-1.722656-.769531-1.722656-1.71875s.769531-1.71875 1.722656-1.71875c.949219 0 1.71875.769531 1.71875 1.71875"
      fill="#1267ac"
    />
    <path
      d="m47.191406 216.636719c0 1.394531-1.132812 2.523437-2.527343 2.523437-1.394532 0-2.527344-1.128906-2.527344-2.523437s1.132812-2.527344 2.527344-2.527344c1.394531 0 2.527343 1.132813 2.527343 2.527344"
      fill="#1267ac"
    />
    <path
      d="m35.652344 205.570313c0 1.132812-.917969 2.054687-2.054688 2.054687-1.132812 0-2.054687-.921875-2.054687-2.054687 0-1.136719.921875-2.054688 2.054687-2.054688 1.136719 0 2.054688.917969 2.054688 2.054688"
      fill="#1267ac"
    />
    <path
      d="m26.015625 191.222656c0 1.644532-1.332031 2.980469-2.976562 2.980469-1.648438 0-2.980469-1.335937-2.980469-2.980469 0-1.644531 1.332031-2.980468 2.980469-2.980468 1.644531 0 2.976562 1.335937 2.976562 2.980468"
      fill="#1267ac"
    />
    <path
      d="m15.527344 172.152344c0 1.523437-1.234375 2.757812-2.757813 2.757812-1.523437 0-2.753906-1.234375-2.753906-2.757812 0-1.523438 1.230469-2.753906 2.753906-2.753906 1.523438 0 2.757813 1.230468 2.757813 2.753906"
      fill="#1267ac"
    />
    <path
      d="m8.382813 149.285156c0 1.597657-1.292969 2.890625-2.890625 2.890625-1.59375 0-2.890625-1.292968-2.890625-2.890625 0-1.597656 1.296875-2.890625 2.890625-2.890625 1.597656 0 2.890625 1.292969 2.890625 2.890625"
      fill="#1267ac"
    />
    <path
      d="m5.679688 126.25c0 1.570313-1.273438 2.839844-2.839844 2.839844-1.570313 0-2.839844-1.269531-2.839844-2.839844 0-1.570312 1.269531-2.839844 2.839844-2.839844 1.566406 0 2.839844 1.269532 2.839844 2.839844"
      fill="#1267ac"
    />
    <path
      d="m7.253906 108.515625c0 1.488281-1.207031 2.695313-2.699218 2.695313-1.484375 0-2.691407-1.207032-2.691407-2.695313s1.207032-2.695312 2.691407-2.695312c1.492187 0 2.699218 1.207031 2.699218 2.695312"
      fill="#1267ac"
    />
    <path
      d="m11.15625 85.414063c0 .671875-.539062 1.214843-1.210937 1.214843-.667969 0-1.207032-.542968-1.207032-1.214843 0-.667969.539063-1.210938 1.207032-1.210938.671875 0 1.210937.542969 1.210937 1.210938"
      fill="#1267ac"
    />
    <path
      d="m20.5625 64.03125c0 .597656-.480469 1.078125-1.078125 1.078125-.59375 0-1.074219-.480469-1.074219-1.078125 0-.59375.480469-1.078125 1.074219-1.078125.597656 0 1.078125.484375 1.078125 1.078125"
      fill="#1267ac"
    />
    <path
      d="m29.753906 49.757813c0 .660156-.53125 1.195312-1.191406 1.195312s-1.195312-.535156-1.195312-1.195312c0-.660157.535156-1.195313 1.195312-1.195313s1.191406.535156 1.191406 1.195313"
      fill="#1267ac"
    />
    <path
      d="m37.011719 42c0 .722656-.589844 1.3125-1.3125 1.3125-.726563 0-1.3125-.589844-1.3125-1.3125 0-.726562.585937-1.3125 1.3125-1.3125.722656 0 1.3125.585938 1.3125 1.3125"
      fill="#1267ac"
    />
    <path
      d="m50.886719 29.699219c0 1.597656-1.292969 2.890625-2.890625 2.890625-1.59375 0-2.890625-1.292969-2.890625-2.890625 0-1.59375 1.296875-2.890625 2.890625-2.890625 1.597656 0 2.890625 1.296875 2.890625 2.890625"
      fill="#1267ac"
    />
    <path
      d="m59.859375 22.925781c0 .875-.710937 1.585938-1.585937 1.585938-.878907 0-1.589844-.710938-1.589844-1.585938 0-.878906.710937-1.585937 1.589844-1.585937.875 0 1.585937.707031 1.585937 1.585937"
      fill="#1267ac"
    />
    <path
      d="m82.519531 11.667969c0 1.667969-1.351562 3.019531-3.015625 3.019531-1.667968 0-3.019531-1.351562-3.019531-3.019531s1.351563-3.019531 3.019531-3.019531c1.664063 0 3.015625 1.351562 3.015625 3.019531"
      fill="#1267ac"
    />
    <path
      d="m102.730469 4.9375c0 1.230469-.996094 2.226563-2.226563 2.226563-1.230468 0-2.226562-.996094-2.226562-2.226563s.996094-2.226562 2.226562-2.226562c1.230469 0 2.226563.996093 2.226563 2.226562"
      fill="#1267ac"
    />
    <path
      d="m124.972656 2.394531c0 1.324219-1.070312 2.394532-2.394531 2.394532-1.320312 0-2.394531-1.070313-2.394531-2.394532 0-1.320312 1.074219-2.394531 2.394531-2.394531 1.324219 0 2.394531 1.074219 2.394531 2.394531"
      fill="#1267ac"
    />
    <path
      d="m139.871094 3.480469c0 1.1875-.964844 2.152344-2.15625 2.152344s-2.15625-.964844-2.15625-2.152344c0-1.191406.964844-2.15625 2.15625-2.15625s2.15625.964844 2.15625 2.15625"
      fill="#1267ac"
    />
    <path
      d="m155.230469 5.289063c0 .996093-.808594 1.804687-1.804688 1.804687-.996093 0-1.804687-.808594-1.804687-1.804687 0-1 .808594-1.804688 1.804687-1.804688.996094 0 1.804688.804688 1.804688 1.804688"
      fill="#1267ac"
    />
    <path
      d="m162.074219 6.699219c0 1.28125-1.039063 2.316406-2.320313 2.316406s-2.320312-1.035156-2.320312-2.316406 1.039062-2.320313 2.320312-2.320313 2.320313 1.039063 2.320313 2.320313"
      fill="#1267ac"
    />
    <path
      d="m178.5 13.703125c0 .78125-.632812 1.414063-1.414062 1.414063-.785157 0-1.417969-.632813-1.417969-1.414063s.632812-1.417969 1.417969-1.417969c.78125 0 1.414062.636719 1.414062 1.417969"
      fill="#1267ac"
    />
    <path
      d="m193.421875 21.550781c0 1.636719-1.328125 2.964844-2.96875 2.964844-1.636719 0-2.96875-1.328125-2.96875-2.964844 0-1.640625 1.332031-2.96875 2.96875-2.96875 1.640625 0 2.96875 1.328125 2.96875 2.96875"
      fill="#1267ac"
    />
    <path
      d="m210.117188 34.160156c0 1.527344-1.234375 2.765625-2.761719 2.765625-1.523438 0-2.757813-1.238281-2.757813-2.765625 0-1.523437 1.234375-2.761718 2.757813-2.761718 1.527344 0 2.761719 1.238281 2.761719 2.761718"
      fill="#1267ac"
    />
    <path
      d="m215.28125 39.257813c0 1.210937-.984375 2.191406-2.195312 2.191406-1.210938 0-2.195313-.980469-2.195313-2.191406 0-1.210938.984375-2.195313 2.195313-2.195313 1.210937 0 2.195312.984375 2.195312 2.195313"
      fill="#1267ac"
    />
    <path
      d="m226.921875 52.136719c0 1.699219-1.378906 3.078125-3.078125 3.078125-1.703125 0-3.082031-1.378906-3.082031-3.078125s1.378906-3.078125 3.082031-3.078125c1.699219 0 3.078125 1.378906 3.078125 3.078125"
      fill="#1267ac"
    />
    <path
      d="m229.84375 57.640625c0 .925781-.75 1.675781-1.679687 1.675781-.925782 0-1.679688-.75-1.679688-1.675781s.753906-1.679687 1.679688-1.679687c.929687 0 1.679687.753906 1.679687 1.679687"
      fill="#1267ac"
    />
    <path
      d="m234.378906 65.539063c0 1.113281-.902343 2.015625-2.015625 2.015625-1.113281 0-2.015625-.902344-2.015625-2.015625 0-1.113282.902344-2.015625 2.015625-2.015625 1.113282 0 2.015625.902343 2.015625 2.015625"
      fill="#1267ac"
    />
    <path
      d="m238.480469 70.496094c0 1.636719-1.328125 2.96875-2.964844 2.96875-1.640625 0-2.96875-1.332031-2.96875-2.96875 0-1.640625 1.328125-2.96875 2.96875-2.96875 1.636719 0 2.964844 1.328125 2.964844 2.96875"
      fill="#1267ac"
    />
    <path
      d="m241.402344 80.914063c0 1.140625-.925781 2.066406-2.066406 2.066406-1.144532 0-2.066407-.925781-2.066407-2.066406 0-1.144532.921875-2.070313 2.066407-2.070313 1.140625 0 2.066406.925781 2.066406 2.070313"
      fill="#1267ac"
    />
    <path
      d="m248.234375 102.726563c0 1.417968-1.148437 2.566406-2.566406 2.566406s-2.566406-1.148438-2.566406-2.566406c0-1.417969 1.148437-2.566407 2.566406-2.566407s2.566406 1.148438 2.566406 2.566407"
      fill="#1267ac"
    />
    <g clipPath="url(#b)">
      <path
        d="m250.796875 125.628906c0 1.421875-1.15625 2.578125-2.582031 2.578125s-2.582031-1.15625-2.582031-2.578125c0-1.425781 1.15625-2.582031 2.582031-2.582031s2.582031 1.15625 2.582031 2.582031"
        fill="#1267ac"
      />
    </g>
    <g fill="#1588d5">
      <path d="m240.824219 125.972656c0 1.054688-.851563 1.90625-1.902344 1.90625-1.054687 0-1.910156-.851562-1.910156-1.90625 0-1.050781.855469-1.902343 1.910156-1.902343 1.050781 0 1.902344.851562 1.902344 1.902343" />
      <path d="m238.609375 145.835938c0 .902343-.734375 1.636718-1.636719 1.636718-.902343 0-1.632812-.734375-1.632812-1.636718 0-.902344.730469-1.636719 1.632812-1.636719.902344 0 1.636719.734375 1.636719 1.636719" />
      <path d="m238.21875 151.363281c0 1.433594-1.160156 2.59375-2.59375 2.59375s-2.59375-1.160156-2.59375-2.59375c0-1.429687 1.160156-2.589843 2.59375-2.589843s2.59375 1.160156 2.59375 2.589843" />
      <path d="m235.953125 157.007813c0 1.21875-.988281 2.207031-2.210937 2.207031-1.21875 0-2.210938-.988281-2.210938-2.207031 0-1.222657.992188-2.210938 2.210938-2.210938 1.222656 0 2.210937.988281 2.210937 2.210938" />
      <path d="m231.449219 169.523438c0 1.277343-1.035156 2.3125-2.3125 2.3125-1.28125 0-2.316406-1.035157-2.316406-2.3125 0-1.28125 1.035156-2.316407 2.316406-2.316407 1.277344 0 2.3125 1.035157 2.3125 2.316407" />
      <path d="m226.21875 179.171875c0 1.054688-.855469 1.910156-1.910156 1.910156-1.054688 0-1.914063-.855468-1.914063-1.910156 0-1.058594.859375-1.910156 1.914063-1.910156 1.054687 0 1.910156.851562 1.910156 1.910156" />
      <path d="m223.578125 184.640625c0 1.589844-1.28125 2.875-2.871094 2.875-1.585937 0-2.871093-1.285156-2.871093-2.875 0-1.585937 1.285156-2.871094 2.871093-2.871094 1.589844 0 2.871094 1.285157 2.871094 2.871094" />
      <path d="m207.152344 203.605469c0 .925781-.746094 1.671875-1.671875 1.671875s-1.675781-.746094-1.675781-1.671875.75-1.675781 1.675781-1.675781 1.671875.75 1.671875 1.675781" />
      <path d="m203.277344 207.703125c0 1.046875-.851563 1.898438-1.898438 1.898438-1.042968 0-1.894531-.851563-1.894531-1.898438s.851563-1.894531 1.894531-1.894531c1.046875 0 1.898438.847656 1.898438 1.894531" />
      <path d="m199.8125 210.960938c0 .902343-.730469 1.632812-1.632812 1.632812-.898438 0-1.628907-.730469-1.628907-1.632812 0-.902344.730469-1.632813 1.628907-1.632813.902343 0 1.632812.730469 1.632812 1.632813" />
      <path d="m196.125 214.320313c0 1.324218-1.074219 2.398437-2.398437 2.398437-1.324219 0-2.398438-1.074219-2.398438-2.398437 0-1.324219 1.074219-2.398438 2.398438-2.398438 1.324218 0 2.398437 1.074219 2.398437 2.398438" />
      <path d="m178.003906 225.261719c0 .820312-.664062 1.488281-1.488281 1.488281-.820312 0-1.484375-.667969-1.484375-1.488281 0-.820313.664063-1.488281 1.484375-1.488281.824219 0 1.488281.667968 1.488281 1.488281" />
      <path d="m162.753906 231.054688c0 .902343-.726562 1.632812-1.628906 1.632812-.898437 0-1.628906-.730469-1.628906-1.632812 0-.898438.730469-1.628907 1.628906-1.628907.902344 0 1.628906.730469 1.628906 1.628907" />
      <path d="m139.210938 236.035156c0 .605469-.492188 1.097657-1.101563 1.097657-.605469 0-1.097656-.492188-1.097656-1.097657 0-.609375.492187-1.101562 1.097656-1.101562.609375 0 1.101563.492187 1.101563 1.101562" />
      <path d="m123.558594 236.945313c0 1.589843-1.289063 2.875-2.878906 2.875-1.589844 0-2.878907-1.285157-2.878907-2.875 0-1.589844 1.289063-2.878907 2.878907-2.878907 1.589843 0 2.878906 1.289063 2.878906 2.878907" />
      <path d="m105.628906 235.023438c0 .890625-.722656 1.609375-1.609375 1.609375-.890625 0-1.609375-.71875-1.609375-1.609375 0-.886719.71875-1.609375 1.609375-1.609375.886719 0 1.609375.722656 1.609375 1.609375" />
      <path d="m85.78125 229.839844c0 .734375-.597656 1.332031-1.328125 1.332031-.738281 0-1.332031-.597656-1.332031-1.332031 0-.730469.59375-1.328125 1.332031-1.328125.730469 0 1.328125.597656 1.328125 1.328125" />
      <path d="m82.25 227.332031c0 1.601563-1.300781 2.902344-2.902344 2.902344-1.601562 0-2.902343-1.300781-2.902343-2.902344 0-1.601562 1.300781-2.902343 2.902343-2.902343 1.601563 0 2.902344 1.300781 2.902344 2.902343" />
      <path d="m69.238281 220.832031c0 1.011719-.820312 1.828125-1.828125 1.828125-1.011718 0-1.828125-.816406-1.828125-1.828125 0-1.007812.816407-1.828125 1.828125-1.828125 1.007813 0 1.828125.820313 1.828125 1.828125" />
      <path d="m57.25 212.773438c0 1.390625-1.128906 2.515625-2.515625 2.515625-1.390625 0-2.519531-1.125-2.519531-2.515625s1.128906-2.515625 2.519531-2.515625c1.386719 0 2.515625 1.125 2.515625 2.515625" />
      <path d="m49.882813 205.097656c0 1.644532-1.332032 2.976563-2.976563 2.976563s-2.980469-1.332031-2.980469-2.976563c0-1.644531 1.335938-2.976562 2.980469-2.976562s2.976563 1.332031 2.976563 2.976562" />
      <path d="m34.9375 187.613281c0 1.582032-1.28125 2.863282-2.863281 2.863282s-2.863281-1.28125-2.863281-2.863282c0-1.582031 1.28125-2.863281 2.863281-2.863281s2.863281 1.28125 2.863281 2.863281" />
      <path d="m28.523438 177.894531c0 1.453125-1.175782 2.628907-2.628907 2.628907-1.449218 0-2.625-1.175782-2.625-2.628907 0-1.449218 1.175782-2.625 2.625-2.625 1.453125 0 2.628907 1.175782 2.628907 2.625" />
      <path d="m23.699219 172.628906c0 .769532-.621094 1.390625-1.390625 1.390625-.765625 0-1.386719-.621093-1.386719-1.390625 0-.765625.621094-1.386718 1.386719-1.386718.769531 0 1.390625.621093 1.390625 1.386718" />
      <path d="m20.207031 157.3125c0 1.554688-1.257812 2.816406-2.8125 2.816406-1.558593 0-2.816406-1.261718-2.816406-2.816406 0-1.554687 1.257813-2.8125 2.816406-2.8125 1.554688 0 2.8125 1.257813 2.8125 2.8125" />
      <path d="m14.542969 136.023438c0 .777343-.632813 1.410156-1.414063 1.410156s-1.410156-.632813-1.410156-1.410156c0-.78125.628906-1.414063 1.410156-1.414063s1.414063.632813 1.414063 1.414063" />
      <path d="m14.609375 117.398438c0 1.191406-.964844 2.15625-2.15625 2.15625s-2.160156-.964844-2.160156-2.15625c0-1.191407.96875-2.160157 2.160156-2.160157s2.15625.96875 2.15625 2.160157" />
      <path d="m15.488281 102.082031c0 .703125-.570312 1.273438-1.273437 1.273438s-1.269531-.570313-1.269531-1.273438.566406-1.273437 1.269531-1.273437 1.273437.570312 1.273437 1.273437" />
      <path d="m21.429688 85.226563c0 1.246093-1.007813 2.253906-2.253907 2.253906-1.246093 0-2.253906-1.007813-2.253906-2.253906 0-1.246094 1.007813-2.253907 2.253906-2.253907 1.246094 0 2.253907 1.007813 2.253907 2.253907" />
      <path d="m30.378906 65.476563c0 1.015625-.824218 1.839843-1.839843 1.839843-1.019532 0-1.84375-.824218-1.84375-1.839843s.824218-1.839844 1.84375-1.839844c1.015625 0 1.839843.824219 1.839843 1.839844" />
      <path d="m43.167969 47.453125c0 .707031-.574219 1.28125-1.28125 1.28125-.710938 0-1.285156-.574219-1.285156-1.28125 0-.710937.574218-1.285156 1.285156-1.285156.707031 0 1.28125.574219 1.28125 1.285156" />
      <path d="m53.761719 37.261719c0 .929687-.757813 1.683594-1.6875 1.683594-.929688 0-1.683594-.753907-1.683594-1.683594 0-.929688.753906-1.683594 1.683594-1.683594.929687 0 1.6875.753906 1.6875 1.683594" />
      <path d="m63.058594 30.734375c0 1.101563-.894531 1.996094-1.996094 1.996094-1.105469 0-1.996094-.894531-1.996094-1.996094 0-1.105469.890625-2 1.996094-2 1.101563 0 1.996094.894531 1.996094 2" />
      <path d="m70.394531 25.671875c0 .761719-.613281 1.378906-1.375 1.378906-.761718 0-1.375-.617187-1.375-1.378906 0-.757812.613282-1.375 1.375-1.375.761719 0 1.375.617188 1.375 1.375" />
      <path d="m79.285156 20.980469c0 1.179687-.957031 2.136719-2.132812 2.136719-1.183594 0-2.140625-.957032-2.140625-2.136719 0-1.179688.957031-2.132813 2.140625-2.132813 1.175781 0 2.132812.953125 2.132812 2.132813" />
      <path d="m95.175781 15.015625c0 .734375-.59375 1.328125-1.328125 1.328125s-1.328125-.59375-1.328125-1.328125.59375-1.328125 1.328125-1.328125 1.328125.59375 1.328125 1.328125" />
      <path d="m103.671875 12.890625c0 1.648438-1.335937 2.984375-2.984375 2.984375-1.652344 0-2.988281-1.335937-2.988281-2.984375 0-1.652344 1.335937-2.988281 2.988281-2.988281 1.648438 0 2.984375 1.335937 2.984375 2.988281" />
      <path d="m109.957031 12.105469c0 1.355469-1.101562 2.453125-2.457031 2.453125s-2.453125-1.097656-2.453125-2.453125c0-1.359375 1.097656-2.457031 2.453125-2.457031s2.457031 1.097656 2.457031 2.457031" />
      <path d="m119.09375 10.992188c0 1.074218-.871094 1.945312-1.945312 1.945312-1.074219 0-1.945313-.871094-1.945313-1.945312 0-1.074219.871094-1.941407 1.945313-1.941407 1.074218 0 1.945312.867188 1.945312 1.941407" />
      <path d="m135.003906 10.976563c0 .769531-.625 1.394531-1.394531 1.394531s-1.390625-.625-1.390625-1.394531c0-.765625.621094-1.390625 1.390625-1.390625s1.394531.625 1.394531 1.390625" />
      <path d="m159.582031 14.851563c0 1.652343-1.34375 2.992187-2.996093 2.992187-1.652344 0-2.992188-1.339844-2.992188-2.992187 0-1.65625 1.339844-2.996094 2.992188-2.996094 1.652343 0 2.996093 1.339844 2.996093 2.996094" />
      <path d="m159.003906 15.304688c0 .632812-.511718 1.148437-1.144531 1.148437-.632812 0-1.148437-.515625-1.148437-1.148437 0-.632813.515625-1.148438 1.148437-1.148438.632813 0 1.144531.515625 1.144531 1.148438" />
      <path d="m168.421875 18.277344c0 1.242187-1.003906 2.246094-2.242187 2.246094-1.242188 0-2.246094-1.003907-2.246094-2.246094 0-1.238281 1.003906-2.242188 2.246094-2.242188 1.238281 0 2.242187 1.003907 2.242187 2.242188" />
      <path d="m189.167969 29.433594c0 .761719-.617188 1.378906-1.378906 1.378906-.761719 0-1.378907-.617187-1.378907-1.378906 0-.757813.617188-1.375 1.378907-1.375.761718 0 1.378906.617187 1.378906 1.375" />
      <path d="m203.132813 38.894531c0 1.480469-1.203125 2.683594-2.683594 2.683594-1.484375 0-2.683594-1.203125-2.683594-2.683594 0-1.484375 1.199219-2.683593 2.683594-2.683593 1.480469 0 2.683594 1.199218 2.683594 2.683593" />
      <path d="m208.320313 43.5625c0 1.460938-1.183594 2.648438-2.648438 2.648438-1.460937 0-2.648437-1.1875-2.648437-2.648438 0-1.464844 1.1875-2.648437 2.648437-2.648437 1.464844 0 2.648438 1.183593 2.648438 2.648437" />
      <path d="m217.585938 56.316406c0 .582032-.472657 1.054688-1.054688 1.054688s-1.054687-.472656-1.054687-1.054688c0-.582031.472656-1.050781 1.054687-1.050781s1.054688.46875 1.054688 1.050781" />
      <path d="m230.203125 76.390625c0 .910156-.734375 1.644531-1.644531 1.644531s-1.648438-.734375-1.648438-1.644531.738282-1.648437 1.648438-1.648437 1.644531.738281 1.644531 1.648437" />
      <path d="m237.363281 94.054688c0 1.375-1.117187 2.492187-2.492187 2.492187-1.378906 0-2.492188-1.117187-2.492188-2.492187 0-1.378907 1.113282-2.496094 2.492188-2.496094 1.375 0 2.492187 1.117187 2.492187 2.496094" />
      <path d="m239.992188 111.539063c0 1.230468-.996094 2.226562-2.226563 2.226562s-2.230469-.996094-2.230469-2.226562c0-1.230469 1-2.226563 2.230469-2.226563s2.226563.996094 2.226563 2.226563" />
      <path d="m240.609375 120.949219c0 .835937-.679687 1.515625-1.515625 1.515625-.835937 0-1.515625-.679688-1.515625-1.515625 0-.835938.679688-1.515625 1.515625-1.515625.835938 0 1.515625.679687 1.515625 1.515625" />
    </g>
    <path
      d="m231.933594 132.714844c0 1.53125-1.242188 2.769531-2.769531 2.769531-1.53125 0-2.769532-1.238281-2.769532-2.769531 0-1.527344 1.238282-2.765625 2.769532-2.765625 1.527343 0 2.769531 1.238281 2.769531 2.765625"
      fill="#1267ac"
    />
    <path
      d="m228.6875 146.175781c0 .597657-.480469 1.078125-1.074219 1.078125-.597656 0-1.078125-.480468-1.078125-1.078125 0-.59375.480469-1.074218 1.078125-1.074218.59375 0 1.074219.480468 1.074219 1.074218"
      fill="#1267ac"
    />
    <path
      d="m226.351563 156.363281c0 1.03125-.835938 1.863282-1.867188 1.863282s-1.863281-.832032-1.863281-1.863282.832031-1.863281 1.863281-1.863281 1.867188.832031 1.867188 1.863281"
      fill="#1267ac"
    />
    <path
      d="m216.492188 178.433594c0 1.1875-.960938 2.152344-2.148438 2.152344s-2.152344-.964844-2.152344-2.152344.964844-2.152344 2.152344-2.152344 2.148438.964844 2.148438 2.152344"
      fill="#1267ac"
    />
    <path
      d="m204.503906 193.496094c0 .601562-.492187 1.089844-1.09375 1.089844-.601562 0-1.09375-.488282-1.09375-1.089844 0-.605469.492188-1.09375 1.09375-1.09375.601563 0 1.09375.488281 1.09375 1.09375"
      fill="#1267ac"
    />
    <path
      d="m196.429688 201.765625c0 .710938-.578125 1.289063-1.289063 1.289063-.714844 0-1.289062-.578125-1.289062-1.289063 0-.710937.574218-1.289062 1.289062-1.289062.710938 0 1.289063.578125 1.289063 1.289062"
      fill="#1267ac"
    />
    <path
      d="m179.457031 214.207031c0 .8125-.660156 1.472657-1.472656 1.472657s-1.46875-.660157-1.46875-1.472657.65625-1.46875 1.46875-1.46875 1.472656.65625 1.472656 1.46875"
      fill="#1267ac"
    />
    <path
      d="m169.925781 219.324219c0 .660156-.53125 1.191406-1.191406 1.191406s-1.195312-.53125-1.195312-1.191406.535156-1.191406 1.195312-1.191406 1.191406.53125 1.191406 1.191406"
      fill="#1267ac"
    />
    <path
      d="m153.390625 225.894531c0 1.671875-1.359375 3.03125-3.035156 3.03125s-3.03125-1.359375-3.03125-3.03125c0-1.675781 1.355469-3.035156 3.03125-3.035156s3.035156 1.359375 3.035156 3.035156"
      fill="#1267ac"
    />
    <path
      d="m135.46875 227.953125c0 .597656-.484375 1.082031-1.082031 1.082031-.601563 0-1.085938-.484375-1.085938-1.082031s.484375-1.085937 1.085938-1.085937c.597656 0 1.082031.488281 1.082031 1.085937"
      fill="#1267ac"
    />
    <path
      d="m124.226563 228.535156c0 1.46875-1.1875 2.65625-2.65625 2.65625-1.464844 0-2.65625-1.1875-2.65625-2.65625s1.191406-2.65625 2.65625-2.65625c1.46875 0 2.65625 1.1875 2.65625 2.65625"
      fill="#1267ac"
    />
    <path
      d="m109.328125 226.910156c0 1.027344-.835937 1.863282-1.863281 1.863282s-1.859375-.835938-1.859375-1.863282c0-1.027343.832031-1.859375 1.859375-1.859375s1.863281.832032 1.863281 1.859375"
      fill="#1267ac"
    />
    <path
      d="m103.414063 225.882813c0 1.605468-1.300782 2.90625-2.90625 2.90625-1.601563 0-2.902344-1.300782-2.902344-2.90625 0-1.601563 1.300781-2.902344 2.902344-2.902344 1.605468 0 2.90625 1.300781 2.90625 2.902344"
      fill="#1267ac"
    />
    <path
      d="m86.097656 219.808594c0 1.425781-1.15625 2.585937-2.585937 2.585937-1.429688 0-2.585938-1.160156-2.585938-2.585937 0-1.429688 1.15625-2.585938 2.585938-2.585938 1.429687 0 2.585937 1.15625 2.585937 2.585938"
      fill="#1267ac"
    />
    <path
      d="m71.320313 211.332031c0 1.621094-1.316407 2.9375-2.9375 2.9375-1.621094 0-2.9375-1.316406-2.9375-2.9375 0-1.621093 1.316406-2.9375 2.9375-2.9375 1.621093 0 2.9375 1.316407 2.9375 2.9375"
      fill="#1267ac"
    />
    <path
      d="m53.914063 198.507813c0 .785156-.636719 1.417968-1.417969 1.417968-.785156 0-1.421875-.632812-1.421875-1.417968 0-.785157.636719-1.417969 1.421875-1.417969.78125 0 1.417969.632812 1.417969 1.417969"
      fill="#1267ac"
    />
    <path
      d="m48.886719 192.582031c0 1.28125-1.039063 2.316407-2.316406 2.316407-1.28125 0-2.320313-1.035157-2.320313-2.316407s1.039063-2.316406 2.320313-2.316406c1.277343 0 2.316406 1.035156 2.316406 2.316406"
      fill="#1267ac"
    />
    <path
      d="m35.980469 175.117188c0 .851562-.6875 1.542968-1.539063 1.542968-.847656 0-1.539062-.691406-1.539062-1.542968 0-.847657.691406-1.539063 1.539062-1.539063.851563 0 1.539063.691406 1.539063 1.539063"
      fill="#1267ac"
    />
    <path
      d="m28.414063 153.351563c0 1.703125-1.378907 3.082031-3.082032 3.082031-1.707031 0-3.089843-1.378906-3.089843-3.082031 0-1.707032 1.382812-3.085938 3.089843-3.085938 1.703125 0 3.082032 1.378906 3.082032 3.085938"
      fill="#1267ac"
    />
    <path
      d="m24.683594 147.4375c0 .695313-.566406 1.257813-1.257813 1.257813-.695312 0-1.257812-.5625-1.257812-1.257813 0-.695312.5625-1.257812 1.257812-1.257812.691407 0 1.257813.5625 1.257813 1.257812"
      fill="#1267ac"
    />
    <path
      d="m24.363281 144.433594c0 .710937-.574218 1.285156-1.285156 1.285156-.710937 0-1.285156-.574219-1.285156-1.285156 0-.710938.574219-1.285156 1.285156-1.285156.710938 0 1.285156.574218 1.285156 1.285156"
      fill="#1267ac"
    />
    <path
      d="m23.054688 121.773438c0 1.105468-.898438 2-2.003907 2-1.109375 0-2.003906-.894532-2.003906-2 0-1.109375.894531-2.003907 2.003906-2.003907 1.105469 0 2.003907.894532 2.003907 2.003907"
      fill="#1267ac"
    />
    <path
      d="m23.761719 115.402344c0 1.09375-.882813 1.980469-1.980469 1.980469-1.089844 0-1.976562-.886719-1.976562-1.980469s.886718-1.976563 1.976562-1.976563c1.097656 0 1.980469.882813 1.980469 1.976563"
      fill="#1267ac"
    />
    <path
      d="m27.4375 96.851563c0 1.484375-1.203125 2.683593-2.683594 2.683593-1.484375 0-2.683593-1.199218-2.683593-2.683593s1.199218-2.6875 2.683593-2.6875c1.480469 0 2.683594 1.203125 2.683594 2.6875"
      fill="#1267ac"
    />
    <path
      d="m31.507813 83.21875c0 1.039063-.84375 1.878906-1.878907 1.878906-1.039062 0-1.882812-.839843-1.882812-1.878906 0-1.039062.84375-1.882812 1.882812-1.882812 1.035157 0 1.878907.84375 1.878907 1.882812"
      fill="#1267ac"
    />
    <path
      d="m33.097656 77.894531c0 .808594-.65625 1.464844-1.46875 1.464844-.808593 0-1.464843-.65625-1.464843-1.464844 0-.808593.65625-1.46875 1.464843-1.46875.8125 0 1.46875.660157 1.46875 1.46875"
      fill="#1267ac"
    />
    <path
      d="m43.433594 63.960938c0 1.625-1.316406 2.945312-2.945313 2.945312-1.625 0-2.945312-1.320312-2.945312-2.945312 0-1.628907 1.320312-2.945313 2.945312-2.945313 1.628907 0 2.945313 1.316406 2.945313 2.945313"
      fill="#1267ac"
    />
    <path
      d="m55.710938 49.914063c0 1.445312-1.171875 2.613281-2.613282 2.613281-1.441406 0-2.613281-1.167969-2.613281-2.613281 0-1.441407 1.171875-2.609375 2.613281-2.609375 1.441407 0 2.613282 1.167968 2.613282 2.609375"
      fill="#1267ac"
    />
    <path
      d="m60.453125 44.683594c0 1.414062-1.148437 2.5625-2.5625 2.5625-1.414062 0-2.558594-1.148438-2.558594-2.5625 0-1.414063 1.144532-2.558594 2.558594-2.558594 1.414063 0 2.5625 1.144531 2.5625 2.558594"
      fill="#1267ac"
    />
    <path
      d="m69.941406 37.613281c0 1.171875-.949218 2.121094-2.121093 2.121094s-2.121094-.949219-2.121094-2.121094.949219-2.121093 2.121094-2.121093 2.121093.949218 2.121093 2.121093"
      fill="#1267ac"
    />
    <path
      d="m90.132813 27.234375c0 1.261719-1.023438 2.285156-2.289063 2.285156-1.261719 0-2.285156-1.023437-2.285156-2.285156 0-1.265625 1.023437-2.289062 2.285156-2.289062 1.265625 0 2.289063 1.023437 2.289063 2.289062"
      fill="#1267ac"
    />
    <path
      d="m96.984375 24.722656c0 1.164063-.945312 2.109375-2.113281 2.109375s-2.109375-.945312-2.109375-2.109375c0-1.167968.941406-2.113281 2.109375-2.113281s2.113281.945313 2.113281 2.113281"
      fill="#1267ac"
    />
    <path
      d="m120.445313 20.398438c0 1.035156-.839844 1.875-1.875 1.875-1.035157 0-1.871094-.839844-1.871094-1.875 0-1.03125.835937-1.871094 1.871094-1.871094 1.035156 0 1.875.839844 1.875 1.871094"
      fill="#1267ac"
    />
    <path
      d="m133.21875 20.210938c0 1.398437-1.136719 2.535156-2.535156 2.535156-1.398438 0-2.53125-1.136719-2.53125-2.535156 0-1.398438 1.132812-2.53125 2.53125-2.53125 1.398437 0 2.535156 1.132812 2.535156 2.53125"
      fill="#1267ac"
    />
    <path
      d="m146.917969 21.746094c0 1.601562-1.296875 2.898437-2.894531 2.898437-1.601563 0-2.894532-1.296875-2.894532-2.898437 0-1.597656 1.292969-2.894531 2.894532-2.894531 1.597656 0 2.894531 1.296875 2.894531 2.894531"
      fill="#1267ac"
    />
    <path
      d="m155.261719 23.898438c0 .851562-.691406 1.539062-1.539063 1.539062-.851562 0-1.539062-.6875-1.539062-1.539062 0-.847657.6875-1.539063 1.539062-1.539063.847657 0 1.539063.691406 1.539063 1.539063"
      fill="#1267ac"
    />
    <path
      d="m164.863281 27.347656c0 .828125-.667968 1.496094-1.496093 1.496094-.824219 0-1.496094-.667969-1.496094-1.496094s.671875-1.496093 1.496094-1.496093c.828125 0 1.496093.667968 1.496093 1.496093"
      fill="#1267ac"
    />
    <path
      d="m179.8125 33.886719c0 1.117187-.90625 2.023437-2.023437 2.023437-1.117188 0-2.023438-.90625-2.023438-2.023437 0-1.117188.90625-2.023438 2.023438-2.023438 1.117187 0 2.023437.90625 2.023437 2.023438"
      fill="#1267ac"
    />
    <path
      d="m184.84375 37.484375c0 1.089844-.886719 1.976563-1.980469 1.976563s-1.980468-.886719-1.980468-1.976563c0-1.09375.886718-1.980469 1.980468-1.980469s1.980469.886719 1.980469 1.980469"
      fill="#1267ac"
    />
    <path
      d="m197.535156 46.886719c0 1.289062-1.042968 2.332031-2.332031 2.332031-1.289062 0-2.332031-1.042969-2.332031-2.332031 0-1.285156 1.042969-2.332031 2.332031-2.332031 1.289063 0 2.332031 1.046875 2.332031 2.332031"
      fill="#1267ac"
    />
    <path
      d="m208.335938 58.046875c0 1.089844-.882813 1.972656-1.972657 1.972656-1.089843 0-1.972656-.882812-1.972656-1.972656s.882813-1.972656 1.972656-1.972656c1.089844 0 1.972657.882812 1.972657 1.972656"
      fill="#1267ac"
    />
    <path
      d="m215.6875 68.894531c0 1.183594-.960937 2.148438-2.148437 2.148438s-2.148438-.964844-2.148438-2.148438c0-1.1875.960938-2.148437 2.148438-2.148437s2.148437.960937 2.148437 2.148437"
      fill="#1267ac"
    />
    <path
      d="m219.183594 73.417969c0 1.53125-1.238281 2.769531-2.769531 2.769531-1.527344 0-2.765625-1.238281-2.765625-2.769531 0-1.527344 1.238281-2.765625 2.765625-2.765625 1.53125 0 2.769531 1.238281 2.769531 2.765625"
      fill="#1267ac"
    />
    <path
      d="m224.191406 86.769531c0 .914063-.742187 1.65625-1.65625 1.65625-.914062 0-1.65625-.742187-1.65625-1.65625 0-.914062.742188-1.65625 1.65625-1.65625.914063 0 1.65625.742188 1.65625 1.65625"
      fill="#1267ac"
    />
    <path
      d="m225.679688 92.238281c0 .660157-.53125 1.191407-1.191407 1.191407-.65625 0-1.191406-.53125-1.191406-1.191407 0-.65625.535156-1.191406 1.191406-1.191406.660157 0 1.191407.535156 1.191407 1.191406"
      fill="#1267ac"
    />
    <path
      d="m229.191406 106.855469c0 .589844-.476562 1.070312-1.070312 1.070312-.589844 0-1.066406-.480468-1.066406-1.070312s.476562-1.070313 1.066406-1.070313c.59375 0 1.070312.480469 1.070312 1.070313"
      fill="#1267ac"
    />
    <path
      d="m231.34375 124.296875c0 .667969-.542969 1.207031-1.210937 1.207031-.667969 0-1.210938-.539062-1.210938-1.207031s.542969-1.210937 1.210938-1.210937c.667968 0 1.210937.542968 1.210937 1.210937"
      fill="#1267ac"
    />
    <path
      d="m221.898438 131.003906c0 .890625-.71875 1.613282-1.609375 1.613282s-1.613282-.722657-1.613282-1.613282.722657-1.609375 1.613282-1.609375 1.609375.71875 1.609375 1.609375"
      fill="#1588d5"
    />
    <path
      d="m219.882813 144.941406c0 .664063-.539063 1.199219-1.199219 1.199219-.664063 0-1.199219-.535156-1.199219-1.199219 0-.660156.535156-1.195312 1.199219-1.195312.660156 0 1.199219.535156 1.199219 1.195312"
      fill="#1588d5"
    />
    <path
      d="m217.789063 155.609375c0 1.472656-1.195313 2.667969-2.671875 2.667969-1.476563 0-2.671875-1.195313-2.671875-2.667969 0-1.476562 1.195312-2.671875 2.671875-2.671875 1.476562 0 2.671875 1.195313 2.671875 2.671875"
      fill="#1588d5"
    />
    <path
      d="m205.660156 177.410156c0 .824219-.664062 1.488282-1.488281 1.488282-.820312 0-1.484375-.664063-1.484375-1.488282 0-.820312.664063-1.484375 1.484375-1.484375.824219 0 1.488281.664063 1.488281 1.484375"
      fill="#1588d5"
    />
    <path
      d="m202.253906 182.355469c0 .675781-.550781 1.222656-1.222656 1.222656-.675781 0-1.222656-.546875-1.222656-1.222656s.546875-1.222656 1.222656-1.222656c.671875 0 1.222656.546875 1.222656 1.222656"
      fill="#1588d5"
    />
    <path
      d="m193.6875 192.664063c0 1.335937-1.085937 2.417968-2.421875 2.417968-1.335937 0-2.417969-1.082031-2.417969-2.417968 0-1.335938 1.082032-2.417969 2.417969-2.417969 1.335938 0 2.421875 1.082031 2.421875 2.417969"
      fill="#1588d5"
    />
    <path
      d="m183.796875 201.558594c0 1.703125-1.382812 3.085937-3.085937 3.085937s-3.082032-1.382812-3.082032-3.085937 1.378907-3.085938 3.082032-3.085938 3.085937 1.382813 3.085937 3.085938"
      fill="#1588d5"
    />
    <path
      d="m177.148438 205.53125c0 1.484375-1.199219 2.6875-2.683594 2.6875s-2.6875-1.203125-2.6875-2.6875 1.203125-2.6875 2.6875-2.6875 2.683594 1.203125 2.683594 2.6875"
      fill="#1588d5"
    />
    <path
      d="m156.675781 214.726563c0 1.394531-1.128906 2.519531-2.523437 2.519531-1.390625 0-2.519531-1.125-2.519531-2.519531 0-1.394532 1.128906-2.519532 2.519531-2.519532 1.394531 0 2.523437 1.125 2.523437 2.519532"
      fill="#1588d5"
    />
    <path
      d="m140.707031 218.222656c0 1.46875-1.1875 2.660157-2.660156 2.660157-1.46875 0-2.65625-1.191407-2.65625-2.660157s1.1875-2.660156 2.65625-2.660156c1.472656 0 2.660156 1.191406 2.660156 2.660156"
      fill="#1588d5"
    />
    <path
      d="m130.695313 219.425781c0 1.707032-1.382813 3.089844-3.089844 3.089844s-3.089844-1.382812-3.089844-3.089844c0-1.707031 1.382813-3.089843 3.089844-3.089843s3.089844 1.382812 3.089844 3.089843"
      fill="#1588d5"
    />
    <path
      d="m113.949219 218.109375c0 1.152344-.929688 2.082031-2.082031 2.082031-1.148438 0-2.078125-.929687-2.078125-2.082031 0-1.148437.929687-2.078125 2.078125-2.078125 1.152343 0 2.082031.929688 2.082031 2.078125"
      fill="#1588d5"
    />
    <path
      d="m104.695313 217.152344c0 .632812-.507813 1.144531-1.140625 1.144531-.628907 0-1.140625-.511719-1.140625-1.144531 0-.628906.511718-1.140625 1.140625-1.140625.632812 0 1.140625.511719 1.140625 1.140625"
      fill="#1588d5"
    />
    <path
      d="m90.363281 211.988281c0 .707032-.574218 1.285157-1.285156 1.285157-.707031 0-1.28125-.578125-1.28125-1.285157 0-.707031.574219-1.28125 1.28125-1.28125.710938 0 1.285156.574219 1.285156 1.28125"
      fill="#1588d5"
    />
    <path
      d="m81.492188 207.035156c0 1.492188-1.207032 2.699219-2.699219 2.699219-1.488281 0-2.695313-1.207031-2.695313-2.699219 0-1.488281 1.207032-2.699218 2.695313-2.699218 1.492187 0 2.699219 1.210937 2.699219 2.699218"
      fill="#1588d5"
    />
    <path
      d="m75.300781 203.71875c0 .953125-.773437 1.722656-1.722656 1.722656s-1.722656-.769531-1.722656-1.722656c0-.949219.773437-1.71875 1.722656-1.71875s1.722656.769531 1.722656 1.71875"
      fill="#1588d5"
    />
    <path
      d="m62.824219 193.496094c0 1.308594-1.058594 2.367187-2.367188 2.367187-1.304687 0-2.367187-1.058593-2.367187-2.367187s1.0625-2.367188 2.367187-2.367188c1.308594 0 2.367188 1.058594 2.367188 2.367188"
      fill="#1588d5"
    />
    <path
      d="m57.253906 188.035156c0 1.246094-1.007812 2.257813-2.257812 2.257813-1.246094 0-2.257813-1.011719-2.257813-2.257813 0-1.246093 1.011719-2.257812 2.257813-2.257812 1.25 0 2.257812 1.011719 2.257812 2.257812"
      fill="#1588d5"
    />
    <path
      d="m49.78125 180.015625c0 .777344-.628906 1.40625-1.40625 1.40625s-1.40625-.628906-1.40625-1.40625.628906-1.40625 1.40625-1.40625 1.40625.628906 1.40625 1.40625"
      fill="#1588d5"
    />
    <path
      d="m39.582031 158.507813c0 1.53125-1.242187 2.769531-2.769531 2.769531-1.53125 0-2.769531-1.238281-2.769531-2.769531 0-1.527344 1.238281-2.765625 2.769531-2.765625 1.527344 0 2.769531 1.238281 2.769531 2.765625"
      fill="#1588d5"
    />
    <path
      d="m35.601563 143.175781c0 1.554688-1.257813 2.816407-2.8125 2.816407-1.554688 0-2.816407-1.261719-2.816407-2.816407 0-1.554687 1.261719-2.816406 2.816407-2.816406 1.554687 0 2.8125 1.261719 2.8125 2.816406"
      fill="#1588d5"
    />
    <path
      d="m33.980469 135.902344c0 1.554687-1.257813 2.8125-2.8125 2.8125-1.554688 0-2.8125-1.257813-2.8125-2.8125 0-1.554688 1.257812-2.8125 2.8125-2.8125 1.554687 0 2.8125 1.257812 2.8125 2.8125"
      fill="#1588d5"
    />
    <path
      d="m31.566406 116.71875c0 .640625-.519531 1.160156-1.160156 1.160156s-1.160156-.519531-1.160156-1.160156c0-.644531.519531-1.164062 1.160156-1.164062s1.160156.519531 1.160156 1.164062"
      fill="#1588d5"
    />
    <path
      d="m33.355469 121.503906c0 1.589844-1.289063 2.882813-2.882813 2.882813-1.589843 0-2.882812-1.292969-2.882812-2.882813 0-1.59375 1.292969-2.882812 2.882812-2.882812 1.59375 0 2.882813 1.289062 2.882813 2.882812"
      fill="#1588d5"
    />
    <path
      d="m34.484375 105.148438c0 1.363281-1.109375 2.472656-2.472656 2.472656-1.367188 0-2.472656-1.109375-2.472656-2.472656 0-1.367188 1.105468-2.472657 2.472656-2.472657 1.363281 0 2.472656 1.105469 2.472656 2.472657"
      fill="#1588d5"
    />
    <path
      d="m35.484375 99.589844c0 1.25-1.011719 2.261719-2.261719 2.261719s-2.261718-1.011719-2.261718-2.261719c0-1.246094 1.011718-2.261719 2.261718-2.261719s2.261719 1.015625 2.261719 2.261719"
      fill="#1588d5"
    />
    <path
      d="m37.816406 89.808594c0 .78125-.632812 1.414062-1.414062 1.414062s-1.410156-.632812-1.410156-1.414062.628906-1.410156 1.410156-1.410156 1.414062.628906 1.414062 1.410156"
      fill="#1588d5"
    />
    <path
      d="m37.152344 93.09375c0 .886719-.722656 1.605469-1.609375 1.605469s-1.605469-.71875-1.605469-1.605469.71875-1.605469 1.605469-1.605469 1.609375.71875 1.609375 1.605469"
      fill="#1588d5"
    />
    <path
      d="m41.027344 84.683594c0 1.367187-1.109375 2.472656-2.476563 2.472656-1.367187 0-2.472656-1.105469-2.472656-2.472656 0-1.367188 1.105469-2.476563 2.472656-2.476563 1.367188 0 2.476563 1.109375 2.476563 2.476563"
      fill="#1588d5"
    />
    <path
      d="m49.824219 67.894531c0 .746094-.605469 1.355469-1.355469 1.355469-.746094 0-1.355469-.609375-1.355469-1.355469 0-.75.609375-1.355468 1.355469-1.355468.75 0 1.355469.605468 1.355469 1.355468"
      fill="#1588d5"
    />
    <path
      d="m55.425781 61.542969c0 1.074219-.875 1.945312-1.949218 1.945312-1.074219 0-1.945313-.871093-1.945313-1.945312s.871094-1.945313 1.945313-1.945313c1.074218 0 1.949218.871094 1.949218 1.945313"
      fill="#1588d5"
    />
    <path
      d="m67.53125 49.386719c0 1.046875-.847656 1.898437-1.894531 1.898437s-1.894531-.851562-1.894531-1.898437c0-1.042969.847656-1.894531 1.894531-1.894531s1.894531.851562 1.894531 1.894531"
      fill="#1588d5"
    />
    <path
      d="m73.9375 45.417969c0 1.425781-1.15625 2.582031-2.582031 2.582031-1.429688 0-2.585938-1.15625-2.585938-2.582031 0-1.429688 1.15625-2.585938 2.585938-2.585938 1.425781 0 2.582031 1.15625 2.582031 2.585938"
      fill="#1588d5"
    />
    <path
      d="m80.917969 40.609375c0 .8125-.660156 1.46875-1.46875 1.46875-.8125 0-1.472656-.65625-1.472656-1.46875s.660156-1.472656 1.472656-1.472656c.808594 0 1.46875.660156 1.46875 1.472656"
      fill="#1588d5"
    />
    <path
      d="m94.628906 34.523438c0 .671875-.546875 1.21875-1.21875 1.21875-.675781 0-1.222656-.546875-1.222656-1.21875 0-.675782.546875-1.222657 1.222656-1.222657.671875 0 1.21875.546875 1.21875 1.222657"
      fill="#1588d5"
    />
    <path
      d="m100.886719 32.371094c0 .804687-.652344 1.460937-1.460938 1.460937-.804687 0-1.457031-.65625-1.457031-1.460937 0-.804688.652344-1.460938 1.457031-1.460938.808594 0 1.460938.65625 1.460938 1.460938"
      fill="#1588d5"
    />
    <path
      d="m116.484375 29.339844c0 1.511719-1.226562 2.738281-2.738281 2.738281s-2.738281-1.226562-2.738281-2.738281 1.226562-2.738281 2.738281-2.738281 2.738281 1.226562 2.738281 2.738281"
      fill="#1588d5"
    />
    <path
      d="m124.148438 28.863281c0 1.386719-1.125 2.511719-2.511719 2.511719s-2.511719-1.125-2.511719-2.511719c0-1.382812 1.125-2.507812 2.511719-2.507812s2.511719 1.125 2.511719 2.507812"
      fill="#1588d5"
    />
    <path
      d="m139.953125 29.773438c0 1.355468-1.101562 2.457031-2.460937 2.457031-1.355469 0-2.457032-1.101563-2.457032-2.457031 0-1.359375 1.101563-2.460938 2.457032-2.460938 1.359375 0 2.460937 1.101563 2.460937 2.460938"
      fill="#1588d5"
    />
    <path
      d="m157.070313 33.789063c0 .902343-.730469 1.632812-1.632813 1.632812-.898437 0-1.628906-.730469-1.628906-1.632812 0-.898438.730469-1.628907 1.628906-1.628907.902344 0 1.632813.730469 1.632813 1.628907"
      fill="#1588d5"
    />
    <path
      d="m168.660156 37.777344c0 1.140625-.925781 2.066406-2.066406 2.066406-1.136719 0-2.0625-.925781-2.0625-2.066406 0-1.136719.925781-2.0625 2.0625-2.0625 1.140625 0 2.066406.925781 2.066406 2.0625"
      fill="#1588d5"
    />
    <path
      d="m181.953125 45.417969c0 1.625-1.316406 2.941406-2.941406 2.941406s-2.941406-1.316406-2.941406-2.941406 1.316406-2.941406 2.941406-2.941406 2.941406 1.316406 2.941406 2.941406"
      fill="#1588d5"
    />
    <path
      d="m194.894531 55.933594c0 1.707031-1.382812 3.089844-3.089843 3.089844-1.707032 0-3.089844-1.382813-3.089844-3.089844s1.382812-3.089844 3.089844-3.089844c1.707031 0 3.089843 1.382813 3.089843 3.089844"
      fill="#1588d5"
    />
    <path
      d="m197.683594 60.742188c0 .59375-.480469 1.070312-1.070313 1.070312-.589843 0-1.070312-.476562-1.070312-1.070312s.480469-1.070313 1.070312-1.070313c.589844 0 1.070313.476563 1.070313 1.070313"
      fill="#1588d5"
    />
    <path
      d="m212.082031 78.90625c0 1.46875-1.1875 2.660156-2.65625 2.660156-1.472656 0-2.660156-1.191406-2.660156-2.660156s1.1875-2.660156 2.660156-2.660156c1.46875 0 2.65625 1.191406 2.65625 2.660156"
      fill="#1588d5"
    />
    <path
      d="m220.175781 99.105469c0 1.480469-1.199218 2.679687-2.679687 2.679687s-2.679688-1.199218-2.679688-2.679687c0-1.476563 1.199219-2.675781 2.679688-2.675781s2.679687 1.199218 2.679687 2.675781"
      fill="#1588d5"
    />
    <path
      d="m219.40625 100.445313c0 .792968-.640625 1.433593-1.433594 1.433593-.789062 0-1.429687-.640625-1.429687-1.433593 0-.789063.640625-1.429688 1.429687-1.429688.792969 0 1.433594.640625 1.433594 1.429688"
      fill="#1588d5"
    />
    <path
      d="m221.132813 114.382813c0 .640625-.515625 1.15625-1.152344 1.15625s-1.15625-.515625-1.15625-1.15625c0-.636719.519531-1.152344 1.15625-1.152344s1.152344.515625 1.152344 1.152344"
      fill="#1588d5"
    />
    <path
      d="m223.75 124.027344c0 1.472656-1.191406 2.660156-2.660156 2.660156s-2.65625-1.1875-2.65625-2.660156c0-1.46875 1.1875-2.660156 2.65625-2.660156s2.660156 1.191406 2.660156 2.660156"
      fill="#1588d5"
    />
  </svg>
);

export const MinimaxIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    fill="none"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <title>Minimax</title>
    <defs>
      <linearGradient
        id="lobe-icons-minimax-fill"
        x1="0%"
        x2="100.182%"
        y1="50.057%"
        y2="50.057%"
      >
        <stop offset="0%" stopColor="#E2167E" />
        <stop offset="100%" stopColor="#FE603C" />
      </linearGradient>
    </defs>
    <path
      d="M16.278 2c1.156 0 2.093.927 2.093 2.07v12.501a.74.74 0 00.744.709.74.74 0 00.743-.709V9.099a2.06 2.06 0 012.071-2.049A2.06 2.06 0 0124 9.1v6.561a.649.649 0 01-.652.645.649.649 0 01-.653-.645V9.1a.762.762 0 00-.766-.758.762.762 0 00-.766.758v7.472a2.037 2.037 0 01-2.048 2.026 2.037 2.037 0 01-2.048-2.026v-12.5a.785.785 0 00-.788-.753.785.785 0 00-.789.752l-.001 15.904A2.037 2.037 0 0113.441 22a2.037 2.037 0 01-2.048-2.026V18.04c0-.356.292-.645.652-.645.36 0 .652.289.652.645v1.934c0 .263.142.506.372.638.23.131.514.131.744 0a.734.734 0 00.372-.638V4.07c0-1.143.937-2.07 2.093-2.07zm-5.674 0c1.156 0 2.093.927 2.093 2.07v11.523a.648.648 0 01-.652.645.648.648 0 01-.652-.645V4.07a.785.785 0 00-.789-.78.785.785 0 00-.789.78v14.013a2.06 2.06 0 01-2.07 2.048 2.06 2.06 0 01-2.071-2.048V9.1a.762.762 0 00-.766-.758.762.762 0 00-.766.758v3.8a2.06 2.06 0 01-2.071 2.049A2.06 2.06 0 010 12.9v-1.378c0-.357.292-.646.652-.646.36 0 .653.29.653.646V12.9c0 .418.343.757.766.757s.766-.339.766-.757V9.099a2.06 2.06 0 012.07-2.048 2.06 2.06 0 012.071 2.048v8.984c0 .419.343.758.767.758.423 0 .766-.339.766-.758V4.07c0-1.143.937-2.07 2.093-2.07z"
      fill="url(#lobe-icons-minimax-fill)"
      fillRule="nonzero"
    />
  </svg>
);

export const RunwayIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    shapeRendering="geometricPrecision"
    textRendering="geometricPrecision"
    imageRendering="optimizeQuality"
    fillRule="evenodd"
    clipRule="evenodd"
    viewBox="0 0 512 512.004"
    {...props}
  >
    <title>Runway</title>
    <path d="M392.51 511.69c-62.032 5.67-113.901-67.019-153.657-103.887C218.749 552.665-.15 538.933 0 391.985c.072-61.724 0-212.549 0-272.331C0 98.16 5.899 76.515 16.965 58.16c21-35.599 61.58-58.584 102.906-58.14 62.254.079 212.177-.071 272.639 0 147.084 0 161.053 218.821 15.696 238.523l68.977 68.884c75.785 71.27 18.906 207.396-84.673 204.263zm-33.407-86.199c42.745 44.035 110.984-24.182 66.963-66.869L306.489 239.217h-66.891v66.862l103.365 103.222 16.14 16.19zM72.417 392.056c-.974 61.201 95.66 61.423 94.693 0V119.654c.817-30.525-31.464-54.778-60.613-45.375-1.268.373-2.465.746-3.59 1.197-18.306 6.787-31.013 25.522-30.49 45.074v271.506zM392.51 166.893c61.429.975 61.358-95.524 0-94.556H230.109c12.335 25.974 9.196 66.425 9.418 94.556H392.51z" />
  </svg>
);
