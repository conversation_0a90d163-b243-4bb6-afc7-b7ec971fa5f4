import { getCredits } from '@/app/actions/credits/get';
import { profile } from '@/schema';
import { eq } from 'drizzle-orm';
import { database } from './database';
import { env } from './env';
import { createClient } from './supabase/server';

export const currentUser = async () => {
  const client = await createClient();
  const {
    data: { user },
  } = await client.auth.getUser();
  return user;
};

export const currentUserProfile = async () => {
  const user = await currentUser();

  if (!user) {
    throw new Error('User not found');
  }

  const userProfiles = await database
    .select()
    .from(profile)
    .where(eq(profile.id, user.id));
  let userProfile = userProfiles.at(0);

  if (!userProfile && user.email) {
    const response = await database
      .insert(profile)
      .values({ id: user.id })
      .returning();

    if (!response.length) {
      throw new Error('Failed to create user profile');
    }

    userProfile = response[0];
  }

  return userProfile;
};

export const getSubscribedUser = async () => {
  const user = await currentUser();

  // Always return the user to allow access to all features for development
  if (process.env.NODE_ENV === 'development') {
    if (!user) {
      // Still ensure a user is logged in for development, but bypass credit/subscription checks
      throw new Error(
        'Create an account to use AI features (dev mode - no credit check).'
      );
    }
    return user;
  }

  // Original implementation for production:
  if (!user) {
    throw new Error('Create an account to use AI features.');
  }

  const userProfileData = await currentUserProfile(); // Renamed to avoid conflict

  if (!userProfileData) {
    throw new Error('User profile not found');
  }

  if (!userProfileData.subscriptionId) {
    throw new Error('Claim your free AI credits to use this feature.');
  }

  const credits = await getCredits();

  if ('error' in credits) {
    throw new Error(credits.error);
  }

  if (
    userProfileData.productId === env.STRIPE_HOBBY_PRODUCT_ID &&
    credits.credits <= 0
  ) {
    throw new Error(
      'Sorry, you have no credits remaining! Please upgrade for more credits.'
    );
  }

  return user;
};
