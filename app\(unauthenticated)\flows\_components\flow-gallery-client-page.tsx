'use client';

import React, { useEffect, useState, useTransition } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { getFlowTemplatesAction, FlowTemplateWithUserAndCategory } from '@/app/actions/flow-templates';
import { getCategoriesAction } from '@/app/actions/categories';
import { categories as categoriesSchema } from '@/schema'; // Import the table schema
import { InferSelectModel } from 'drizzle-orm'; // Import InferSelectModel
import { getFlowTemplatesAction, FlowTemplateWithUserAndCategory } from '@/app/actions/flow-templates';
import { getCategoriesAction } from '@/app/actions/categories';
import { categories } from '@/schema'; // Import the table schema
import { InferSelectModel } from 'drizzle-orm'; // Import InferSelectModel
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { PaginationControls } from '@/components/ui/pagination-controls';
import { toast } from 'sonner';
import { Search, XCircle } from 'lucide-react';
import { toggleUpvoteAction } from '@/app/actions/flow-templates/upvote';
import { Skeleton } from '@/components/ui/skeleton';
import { FlowCard } from './FlowCard';

const ITEMS_PER_PAGE = 9;

export function FlowGalleryClientPage(): React.ReactElement {
  const router = useRouter();
  const urlSearchParams = useSearchParams(); // Next.js hook for client-side search params

  const [templates, setTemplates] = useState<FlowTemplateWithUserAndCategory[]>([]);
  type Category = InferSelectModel<typeof categoriesSchema>; // Infer the type
  const [categoryList, setCategories] = useState<Category[]>([]);
  const [totalTemplates, setTotalTemplates] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isPending, startTransition] = useTransition();

  // Extract search parameters from the client-side URL searchParams
  const pageParam = urlSearchParams.get('page');
  const categoryParam = urlSearchParams.get('category');
  const searchParam = urlSearchParams.get('search');
  
  // Set initial state based on URL parameters
  const [searchTerm, setSearchTerm] = useState(searchParam || '');
  const [selectedCategory, setSelectedCategory] = useState(categoryParam || 'all');
  const currentPage = pageParam ? Number.parseInt(pageParam, 10) : 1;

  useEffect(() => {
    const fetchCategories = async () => {
      const result = await getCategoriesAction();
      if (result.success && result.data) {
        setCategories(result.data);
      } else {
        toast.error(result.error || 'Failed to load categories.');
      }
    };
    fetchCategories();
  }, []);

  useEffect(() => {
    setIsLoading(true);
    const categoryId = selectedCategory === 'all' ? undefined : selectedCategory;
    const page = Number.parseInt(urlSearchParams.get('page') || '1', 10);
    const query = urlSearchParams.get('search') || '';
    const cat = urlSearchParams.get('category') || 'all';

    getFlowTemplatesAction({
      page: page,
      limit: ITEMS_PER_PAGE,
      categoryId: cat === 'all' ? undefined : cat,
      searchTerm: query,
      orderBy: 'upvotes',
      orderDirection: 'desc',
      adminMode: false // Only fetch public templates
    })
      .then((result) => {
        if (result.success && result.data) {
          setTemplates(result.data.templates);
          setTotalTemplates(result.data.totalCount);
        } else {
          toast.error(result.error || 'Failed to load flow templates.');
          setTemplates([]);
          setTotalTemplates(0);
        }
      })
      .finally(() => setIsLoading(false));
  }, [urlSearchParams, selectedCategory]); // Re-fetch when URL parameters or selected category changes

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const handleCategoryChange = (value: string) => {
    setSelectedCategory(value);
    updateSearchParams(1, value, searchTerm);
  };

  const handlePageChange = (newPage: number) => {
    updateSearchParams(newPage, selectedCategory, searchTerm);
  };

  const executeSearch = () => {
    updateSearchParams(1, selectedCategory, searchTerm);
  };
  
  const clearSearch = () => {
    setSearchTerm('');
    updateSearchParams(1, selectedCategory, '');
  }

  const updateSearchParams = (page: number, category: string, search: string) => {
    const params = new URLSearchParams();
    params.set('page', page.toString());
    if (category && category !== 'all') {
      params.set('category', category);
    }
    if (search) {
      params.set('search', search);
    }
    startTransition(() => {
        router.push(`/flows?${params.toString()}`);
    });
  };

  const handleUpvote = async (templateId: string) => {
    startTransition(async () => {
      const result = await toggleUpvoteAction(templateId);
      
      if (result.success && result.data) {
        // Update the template in the local state
        setTemplates(prevTemplates => 
          prevTemplates.map(t => 
            t.id === templateId ? { 
              ...t, 
              upvotes: result.data?.upvotes || t.upvotes,
              upvotedByCurrentUser: result.data?.upvotedByCurrentUser
            } : t
          )
        );
        toast.success(result.data.upvotedByCurrentUser ? 'Upvoted!' : 'Upvote removed');
      } else {
        toast.error(result.error || 'Failed to update upvote.');
      }
    });
  };
  
  // Placeholder for import functionality
  const handleImport = (templateId: string) => {
    // This would typically redirect to the editor with the template JSON
    // or trigger a modal to confirm import into a new or existing project.
    toast.info(`Importing template ${templateId}... (Not Implemented Yet)`);
    // Example: router.push(`/editor?templateId=${templateId}`);
  };

  // Placeholder for view functionality
  const handleViewDetails = (templateId: string) => {
    toast.info(`Viewing details for template ${templateId}... (Not Implemented Yet)`);
    // Example: router.push(`/flows/${templateId}`); // If a detail page exists
  };

  const totalPages = Math.ceil(totalTemplates / ITEMS_PER_PAGE);

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row gap-4 p-4 border rounded-lg bg-card">
        <div className="flex-grow relative">
          <Input
            type="text"
            placeholder="Search templates by name or description..."
            value={searchTerm}
            onChange={handleSearchChange}
            onKeyPress={(e) => e.key === 'Enter' && executeSearch()}
            className="pr-10"
          />
          {searchTerm && (
             <Button variant="ghost" size="sm" className="absolute right-10 top-1/2 -translate-y-1/2 h-7 w-7 p-0" onClick={clearSearch} aria-label="Clear search">
                <XCircle className="h-4 w-4" />
            </Button>
          )}
          <Button variant="outline" size="sm" className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0" onClick={executeSearch} aria-label="Search">
            <Search className="h-4 w-4" />
          </Button>
        </div>
        <Select value={selectedCategory} onValueChange={handleCategoryChange}>
          <SelectTrigger className="w-full md:w-[200px]">
            <SelectValue placeholder="Filter by category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categoryList.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(ITEMS_PER_PAGE)].map((_, i) => (
            <div key={i} className="border rounded-lg p-4 space-y-3 bg-card flex flex-col h-full">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2" />
                <div className="flex-grow mt-2 space-y-1">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-5/6" />
                    <Skeleton className="h-4 w-full" />
                </div>
                <div className="flex justify-between items-center pt-4 mt-auto border-t">
                    <Skeleton className="h-8 w-20" />
                    <div className="flex space-x-2">
                        <Skeleton className="h-8 w-8" />
                        <Skeleton className="h-8 w-20" />
                    </div>
                </div>
            </div>
          ))}
        </div>
      ) : templates.length === 0 ? (
        <div className="text-center py-10 col-span-full">
          <p className="text-xl text-muted-foreground">No templates found matching your criteria.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {templates.map((template) => (
            <FlowCard
              key={template.id}
              template={template}
              onUpvote={handleUpvote}
              onImport={handleImport}
              onViewDetails={handleViewDetails}
            />
          ))}
        </div>
      )}

      {!isLoading && totalTemplates > 0 && totalPages > 1 && (
        <PaginationControls
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
        />
      )}
    </div>
  );
}