﻿'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>lose,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { useSaveProject } from '@/hooks/use-save-project';
import { useReactFlow } from '@xyflow/react';
import { Loader2, Sparkles } from 'lucide-react';
import { useParams } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

export function PromptFlowGeneratorInput() {
  const [prompt, setPrompt] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const reactFlow = useReactFlow();
  const { setNodes, setEdges } = reactFlow;
  const { projectId } = useParams();
  const { save } = useSaveProject(projectId as string);

  // Handle parsing the API response
  const parseApiResponse = async (response: Response) => {
    const responseText = await response.text();
    try {
      return JSON.parse(responseText);
    } catch (e) {
      toast.error('API returned invalid JSON response');
      throw new Error('Invalid response format from API');
    }
  };

  // Handle applying the generated flow to the canvas
  const applyFlowToCanvas = async (data: any) => {
    // Validate data structure
    if (
      !data.nodes ||
      !Array.isArray(data.nodes) ||
      !data.edges ||
      !Array.isArray(data.edges)
    ) {
      toast.error('Invalid flow structure returned from API');
      throw new Error('Invalid flow structure returned from API');
    }

    if (data.nodes.length === 0) {
      toast.error('No nodes were generated');
      throw new Error('No nodes were generated');
    }

    toast.info(
      `Processing flow with ${data.nodes.length} nodes and ${data.edges.length} edges`
    );
    toast.info(`Node types: ${data.nodes.map((n: any) => n.type).join(', ')}`);

    // Apply nodes and edges
    setNodes(data.nodes);
    setEdges(data.edges);

    // Save the project
    await save();

    toast.success('AI flow generated successfully');
    setIsOpen(false);
  };

  const handleGenerateFlow = async () => {
    if (!prompt.trim()) {
      toast.error('Please enter a prompt');
      return;
    }

    setIsLoading(true);
    toast.info('Generating AI flow...');

    try {
      const response = await fetch('/api/ai/generate-ai-flow', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ prompt }),
      });

      const data = await parseApiResponse(response);

      if (!response.ok) {
        toast.error(`API error: ${response.status}`);
        throw new Error(data.error || 'Failed to generate flow');
      }

      await applyFlowToCanvas(data);
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : 'Failed to generate flow'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="icon" className="rounded-full">
          <Sparkles size={16} />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Generate AI Flow</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <Textarea
            placeholder="Describe what you want to create, e.g., 'Create a system for AI to generate a complete story with short videos'"
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            rows={6}
            className="resize-none"
          />
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline" disabled={isLoading}>
              Cancel
            </Button>
          </DialogClose>
          <Button
            onClick={handleGenerateFlow}
            disabled={isLoading || !prompt.trim()}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Sparkles className="mr-2 h-4 w-4" />
                Generate Flow
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
