'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { LayoutDashboard, Palette, FileJson, Settings, Users } from 'lucide-react'; // Example icons

const adminNavItems = [
  { href: '/admin', label: 'Dashboard', icon: LayoutDashboard }, // Placeholder, will be /admin/dashboard or similar
  { href: '/admin/flow-templates', label: 'Flow Templates', icon: FileJson },
  { href: '/admin/categories', label: 'Categories', icon: Palette },
  // Add more admin links as needed, e.g., Users, Settings
  // { href: '/admin/users', label: 'Users', icon: Users },
  // { href: '/admin/settings', label: 'Settings', icon: Settings },
];

export function AdminSidebar() {
  const pathname = usePathname();

  return (
    <aside className="w-64 bg-background border-r p-4 flex flex-col space-y-2">
      <div className="mb-4">
        <Link href="/admin" className="text-2xl font-semibold text-primary">
          Admin Panel
        </Link>
      </div>
      <nav className="flex flex-col space-y-1">
        {adminNavItems.map((item) => {
          const Icon = item.icon;
          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                'flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium',
                pathname === item.href || (item.href !== '/admin' && pathname.startsWith(item.href))
                  ? 'bg-primary text-primary-foreground'
                  : 'text-muted-foreground hover:bg-muted hover:text-foreground'
              )}
            >
              <Icon className="h-5 w-5" />
              <span>{item.label}</span>
            </Link>
          );
        })}
      </nav>
      {/* You can add a footer or other elements here */}
    </aside>
  );
}