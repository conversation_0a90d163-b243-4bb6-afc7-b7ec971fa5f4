import type { Editor } from '@tiptap/core';
import CharacterCount from '@tiptap/extension-character-count';

import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import Placeholder from '@tiptap/extension-placeholder';
import TaskItem from '@tiptap/extension-task-item';
import TaskList from '@tiptap/extension-task-list';
import TextStyle from '@tiptap/extension-text-style';
import Underline from '@tiptap/extension-underline';
import StarterKit from '@tiptap/starter-kit';
// import { Document, Packer, Paragraph, TextRun } from 'docx'; // Commented out
// import { saveAs } from 'file-saver'; // Commented out
// import html2pdf from 'html2pdf.js'; // Commented out
import { useCallback, useMemo, useState } from 'react';

// Define a more specific type for Tiptap content
type TiptapNode = {
  type: string;
  attrs?: Record<string, unknown>; // Changed any to unknown
  content?: TiptapNode[];
  text?: string;
};

type TiptapDoc = {
  type: 'doc';
  content?: TiptapNode[];
};

export type KiboEditorDocument = {
  id?: string;
  title: string;
  content: string; // This will store HTML content
  createdAt?: Date;
  updatedAt?: Date;
};

// Added KiboEditorConfig type definition
export type KiboEditorConfig = {
  placeholderText?: string;
  characterLimit?: number | null;
  initialContent?: string; // HTML string
  onUpdate?: (content: string, title: string) => void; // HTML content
  onTitleUpdate?: (title: string) => void;
  onSave?: (document: KiboEditorDocument) => void;
  editable?: boolean;
};

const WORD_REGEX = /\\s+/;
const URL_PATTERN = /^(https?:\/\/|mailto:|tel:)/;

export const useKiboEditor = (
  initialDocument?: KiboEditorDocument,
  initialConfig?: Partial<KiboEditorConfig> // Added initialConfig parameter
) => {
  // Added defaultConfig and config object initialization
  const defaultConfig: KiboEditorConfig = {
    placeholderText: 'Start typing...',
    characterLimit: 10000,
    initialContent: '<h1>Untitled Document</h1><p>Start writing here...</p>',
    editable: true,
  };

  const config = { ...defaultConfig, ...initialConfig };

  const [document, setDocument] = useState<
    KiboEditorDocument & { editor?: Editor } // Use Editor type
  >(
    initialDocument || {
      title: 'Untitled Document',
      content:
        config.initialContent ||
        '<h1>Untitled Document</h1><p>Start writing here...</p>', // Use config.initialContent
      createdAt: new Date(),
      updatedAt: new Date(),
    }
  );

  const [isFullscreen, setIsFullscreen] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const [charCount, setCharCount] = useState(0);
  const extensions = useMemo(
    () => [
      StarterKit.configure({
        heading: { levels: [1, 2, 3] },
        bulletList: {
          keepMarks: true,
          keepAttributes: true,
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: true,
        },
        // Optimize for better typing performance
        history: {
          depth: 100,
          newGroupDelay: 500,
        },
        // Improve paragraph handling
        paragraph: {
          HTMLAttributes: {
            class: 'prose-paragraph',
          },
        },
      }),
      Placeholder.configure({
        placeholder: config.placeholderText || 'Start writing your thoughts...',
        showOnlyCurrent: true, // Only show placeholder in empty document
        showOnlyWhenEditable: true,
        includeChildren: true,
      }),
      CharacterCount.configure({
        limit: config.characterLimit || null,
      }),
      Image.configure({
        inline: false,
        allowBase64: true,
        HTMLAttributes: {
          class: 'rounded-md border border-border',
        },
      }),
      Underline,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-primary underline decoration-primary underline-offset-4',
          rel: 'noopener noreferrer',
          target: '_blank',
        },
        validate: (url: string) => {
          return URL_PATTERN.test(url);
        },
      }),
      TaskList.configure({
        HTMLAttributes: {
          class: 'not-prose pl-2',
        },
      }),
      TaskItem.configure({
        HTMLAttributes: {
          class: 'flex items-start gap-1.5 my-1',
        },
        nested: true,
      }),
      TextStyle,
    ],
    [] // Remove dependencies to make extensions stable
  );

  const handleEditorUpdate = useCallback(
    ({ editor }: { editor: Editor }) => {
      const html = editor.getHTML();
      const text = editor.getText();
      const jsonDoc = editor.getJSON() as TiptapDoc;

      setCharCount(editor.storage.characterCount.characters());
      setWordCount(text ? text.trim().split(WORD_REGEX).length : 0);

      const firstHeading = jsonDoc.content?.find(
        (node) => node.type === 'heading' && node.attrs?.level === 1
      );

      let title = document.title;
      if (firstHeading?.content) {
        const extractedTitle = firstHeading.content
          .map((c: TiptapNode) => c.text)
          .join('');
        if (extractedTitle) {
          title = extractedTitle;
        }
      }

      setDocument((prev) => ({
        ...prev,
        title,
        content: html,
        updatedAt: new Date(),
      }));

      // Call onUpdate and onTitleUpdate from config if they exist
      if (config.onUpdate) {
        config.onUpdate(html, title);
      }
      if (config.onTitleUpdate) {
        config.onTitleUpdate(title);
      }
    },
    // Only include dependencies that are actually used and change over time.
    // document.title is part of the 'document' state, which is already implicitly a dependency if setDocument is used.
    // config functions (onUpdate, onTitleUpdate) are generally stable unless the config object itself changes.
    [document.title, config.onUpdate, config.onTitleUpdate] // Simplified dependencies
  );

  const toggleFullscreen = useCallback(() => {
    setIsFullscreen((prev) => {
      const nextFullscreenState = !prev;
      if (typeof window !== 'undefined') {
        // Check for window object
        if (nextFullscreenState) {
          window.document.body.classList.add('overflow-hidden');
        } else {
          window.document.body.classList.remove('overflow-hidden');
        }
      }
      return nextFullscreenState;
    });
  }, []);

  // Placeholder export functions
  const exportToDocx = useCallback(() => {
    // Placeholder: Functionality to be implemented
    // alert(\'Export to DOCX functionality is currently unavailable.\');
  }, []);

  const exportToPdf = useCallback(() => {
    // Placeholder: Functionality to be implemented
    // alert(\'Export to PDF functionality is currently unavailable.\');
  }, []);

  return {
    document,
    setDocument, // Returning setDocument
    isFullscreen,
    toggleFullscreen,
    wordCount,
    charCount,
    extensions,
    handleEditorUpdate,
    exportToDocx, // Returning placeholder
    exportToPdf, // Returning placeholder
    config, // Returning config
  };
};
