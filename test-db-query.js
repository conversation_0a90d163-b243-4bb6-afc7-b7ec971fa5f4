import { desc } from 'drizzle-orm';
// test-db-query.js
import { database as db } from './lib/database.js';
import { categories } from './schema.js';

async function testQuery() {
  try {
    console.log('Testing database query...');
    const allCategories = await db
      .select()
      .from(categories)
      .orderBy(desc(categories.createdAt));
    console.log(
      'Categories query successful:',
      allCategories.length,
      'categories found'
    );
    process.exit(0);
  } catch (error) {
    console.error('Error testing database query:', error);
    process.exit(1);
  }
}

testQuery();
