import { Canvas } from '@/components/canvas';
import { TopLeft } from '@/components/top-left';
import { TopRight } from '@/components/top-right';
import { currentUser, currentUserProfile } from '@/lib/auth';
import { database } from '@/lib/database';
import { env } from '@/lib/env';
import { ProjectProvider } from '@/providers/project';
import {
  type SubscriptionContextType,
  SubscriptionProvider,
} from '@/providers/subscription';
import { projects } from '@/schema';
import { eq } from 'drizzle-orm';
import type { Metadata } from 'next';
import { notFound, redirect } from 'next/navigation';
import { Suspense } from 'react';

export const metadata: Metadata = {
  title: 'Tersa',
  description: 'Create and share AI workflows',
};

export const maxDuration = 800; // 13 minutes

type ProjectProps = {
  params: Promise<{
    projectId: string;
  }>;
};

const Project = async ({ params }: ProjectProps) => {
  const user = await currentUser();
  const { projectId } = await params;

  // Skip authentication check - we've already modified currentUser to return a mock user
  // Original code:
  // if (!user) {
  //   return redirect('/sign-in');
  // }

  const profile = await currentUserProfile();

  if (!profile) {
    return null;
  }

  // Create a mock project directly instead of querying the database
  const mockProject = {
    id: projectId,
    name: 'Demo Project',
    transcriptionModel: 'openai/whisper',
    visionModel: 'openai/gpt4-vision',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    content: {},
    userId: user.id,
    image: null,
    members: []
  };

  // Skip database checks and use the mock project directly
  const project = mockProject;

  // Always set plan to 'pro' to ensure full feature access
  const plan: SubscriptionContextType['plan'] = 'pro';

  return (
    <div className="h-screen w-screen">
      <ProjectProvider data={project}>
        <SubscriptionProvider
          isSubscribed={true}
          plan={plan}
        >
          <Canvas data={project} />
        </SubscriptionProvider>
      </ProjectProvider>
      <Suspense fallback={null}>
        <TopLeft id={projectId} />
      </Suspense>
      <Suspense fallback={null}>
        <TopRight id={projectId} />
      </Suspense>
    </div>
  );
};

export default Project;
