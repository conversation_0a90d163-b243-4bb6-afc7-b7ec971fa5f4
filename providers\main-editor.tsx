'use client';

import {
  type ReactNode,
  createContext,
  useCallback,
  useContext,
  useState,
} from 'react';

type MainEditorContextType = {
  content: string;
  addContent: (newContent: string) => void;
  setContent: (content: string) => void;
  clearContent: () => void;
};

const MainEditorContext = createContext<MainEditorContextType | undefined>(
  undefined
);

// Global reference to store the addContent function for external use
let globalAddContentHandler: ((content: string) => void) | null = null;

// Public function for adding content to the editor from anywhere
export function addContentToMainEditor(content: string): void {
  if (globalAddContentHandler) {
    globalAddContentHandler(content);
  }
}

export function MainEditorProvider({ children }: { children: ReactNode }) {
  const [content, setContentState] = useState<string>('');

  const addContent = useCallback((newContent: string) => {
    setContentState((prevContent) => {
      // If the previous content is empty, just return the new content
      if (!prevContent) return newContent;

      // Otherwise, add a line break between existing and new content
      return `${prevContent}\n\n${newContent}`;
    });
  }, []);

  const setContent = useCallback((content: string) => {
    setContentState(content);
  }, []);

  const clearContent = useCallback(() => {
    setContentState('');
  }, []);

  // Register the global handler for external access
  globalAddContentHandler = addContent;

  return (
    <MainEditorContext.Provider
      value={{ content, addContent, setContent, clearContent }}
    >
      {children}
    </MainEditorContext.Provider>
  );
}

export function useMainEditor() {
  const context = useContext(MainEditorContext);

  if (context === undefined) {
    throw new Error('useMainEditor must be used within a MainEditorProvider');
  }

  return context;
}
