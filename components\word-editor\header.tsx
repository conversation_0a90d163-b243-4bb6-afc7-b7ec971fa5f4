'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ThemeToggle } from '@/components/ui/theme-toggle'; // Corrected path for ThemeToggle
import type { Editor } from '@tiptap/core';
import {
  ArrowLeft,
  Bold,
  Home,
  Image as ImageIcon,
  Italic,
  List,
  Settings,
  Strikethrough,
  User,
} from 'lucide-react';
import { useRouter } from 'next/navigation';

export const EditorHeader = ({ editor }: { editor: Editor | null }) => {
  const router = useRouter();

  if (!editor) {
    return (
      <div className="editor-header-container flex items-center justify-between border-b p-2">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push('/')}
            title="Back to Home"
            className="rounded-full transition-colors hover:bg-primary/10"
            aria-label="Back to Home"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex items-center gap-2">
            <h1 className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text font-semibold text-lg text-transparent">
              Tersa Word Editor
            </h1>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {' '}
          {/* Placeholder for buttons if editor is null, or just show loading */}
          <div>Loading Editor Tools...</div>
        </div>
        <div className="flex items-center gap-2">
          <ThemeToggle />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" aria-label="Account options">
                <User className="h-5 w-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => router.push('/')}>
                <Home className="mr-2 h-4 w-4" />
                <span>Back to Dashboard</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => router.push('/settings')}>
                <Settings className="mr-2 h-4 w-4" />
                <span>Settings</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    );
  }

  return (
    <div className="editor-header-container flex items-center justify-between border-b p-2">
      <div className="flex items-center gap-3">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push('/')}
          title="Back to Home"
          className="rounded-full transition-colors hover:bg-primary/10"
          aria-label="Back to Home"
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <div className="flex items-center gap-2">
          <h1 className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text font-semibold text-lg text-transparent">
            Tersa Word Editor
          </h1>
        </div>
      </div>

      <div className="editor-formatting-buttons flex items-center gap-1 sm:gap-2">
        <Button
          variant={editor.isActive('bold') ? 'default' : 'ghost'}
          onClick={() => editor.chain().focus().toggleBold().run()}
          size="sm"
          aria-label="Toggle bold"
        >
          <Bold className="h-4 w-4" />
        </Button>
        <Button
          variant={editor.isActive('italic') ? 'default' : 'ghost'}
          onClick={() => editor.chain().focus().toggleItalic().run()}
          size="sm"
          aria-label="Toggle italic"
        >
          <Italic className="h-4 w-4" />
        </Button>
        <Button
          variant={editor.isActive('strike') ? 'default' : 'ghost'}
          onClick={() => editor.chain().focus().toggleStrike().run()}
          disabled={!editor.can().chain().focus().toggleStrike().run()}
          className={editor.isActive('strike') ? 'is-active' : ''}
          size="sm"
          aria-label="Toggle strikethrough"
        >
          <Strikethrough className="h-4 w-4" />
        </Button>
        <Button
          variant={editor.isActive('bulletList') ? 'default' : 'ghost'}
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          disabled={!editor.can().chain().focus().toggleBulletList().run()}
          className={editor.isActive('bulletList') ? 'is-active' : ''}
          size="sm"
          aria-label="Toggle bullet list"
        >
          <List className="h-4 w-4" />
        </Button>
        {/* Add Image Button Start */}
        <Button
          variant="ghost"
          size="icon"
          onClick={() => {
            const url = window.prompt('Enter image URL');
            if (url) {
              editor.chain().focus().setImage({ src: url }).run();
            }
          }}
          disabled={
            !editor.isEditable ||
            (editor.can().setImage && !editor.can().setImage({ src: '' }))
          } // Check if setImage can be run
          aria-label="Add image"
        >
          <ImageIcon className="h-4 w-4" />
        </Button>
        {/* Add Image Button End */}
      </div>

      <div className="flex items-center gap-2">
        <ThemeToggle />
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" aria-label="Account options">
              <User className="h-5 w-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>My Account</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => router.push('/')}>
              <Home className="mr-2 h-4 w-4" />
              <span>Back to Dashboard</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => router.push('/settings')}>
              <Settings className="mr-2 h-4 w-4" />
              <span>Settings</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};
