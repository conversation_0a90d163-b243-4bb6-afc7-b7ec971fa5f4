'use server';

import { db } from '@/lib/database';

export async function getCategoriesAction() {
  try {
    // Using our mock database with simplified API
    const allCategories = await db.query.categories.findMany();

    return {
      success: true,
      data: allCategories,
    };
  } catch (error) {
    // Log error to server console
    const errorMsg = error instanceof Error ? error.message : String(error);

    return {
      success: false,
      error: `Failed to fetch categories: ${errorMsg}`,
    };
  }
}
