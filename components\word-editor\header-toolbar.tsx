'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Bold, Italic, List, Strikethrough } from 'lucide-react';
import { useEffect, useState } from 'react';

export function EditorHeader() {
  const [editor, setEditor] = useState<any>(null);

  // Use an effect to access the global editor instance once available
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Check if the editor is already available
      const existingEditor =
        window.document.querySelector('.ProseMirror')?.['editor'];
      if (existingEditor) {
        setEditor(existingEditor);
      } else {
        // If not available immediately, wait for it to be initialized
        const checkInterval = setInterval(() => {
          const editorInstance =
            window.document.querySelector('.ProseMirror')?.['editor'];
          if (editorInstance) {
            setEditor(editorInstance);
            clearInterval(checkInterval);
          }
        }, 100);

        return () => clearInterval(checkInterval);
      }
    }
  }, []);

  if (!editor) {
    return (
      <div className="editor-header sticky top-0 z-10 flex items-center gap-2 border-gray-200 border-b bg-background/95 p-2 backdrop-blur supports-backdrop-blur:bg-background/80">
        <div className="mr-2 font-medium text-sm">Format</div>
        <div className="h-8 w-24 animate-pulse rounded bg-gray-200"></div>
      </div>
    );
  }

  return (
    <div className="editor-header sticky top-0 z-10 flex items-center gap-2 border-gray-200 border-b bg-background/95 p-2 backdrop-blur supports-backdrop-blur:bg-background/80">
      <div className="mr-2 font-medium text-sm">Format</div>
      <Button
        variant={editor.isActive('bold') ? 'default' : 'ghost'}
        onClick={() => editor.chain().focus().toggleBold().run()}
        className="h-8 px-2 py-1"
        size="sm"
        title="Bold"
      >
        <Bold className="h-4 w-4" />
      </Button>
      <Button
        variant={editor.isActive('italic') ? 'default' : 'ghost'}
        onClick={() => editor.chain().focus().toggleItalic().run()}
        className="h-8 px-2 py-1"
        size="sm"
        title="Italic"
      >
        <Italic className="h-4 w-4" />
      </Button>
      <Button
        variant={editor.isActive('strike') ? 'default' : 'ghost'}
        onClick={() => editor.chain().focus().toggleStrike().run()}
        className="h-8 px-2 py-1"
        size="sm"
        title="Strikethrough"
      >
        <Strikethrough className="h-4 w-4" />
      </Button>
      <Button
        variant={editor.isActive('bulletList') ? 'default' : 'ghost'}
        onClick={() => editor.chain().focus().toggleBulletList().run()}
        className="h-8 px-2 py-1"
        size="sm"
        title="Bullet List"
      >
        <List className="h-4 w-4" />
      </Button>
    </div>
  );
}
