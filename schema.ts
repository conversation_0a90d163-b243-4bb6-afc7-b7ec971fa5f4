import { sql } from 'drizzle-orm';
import { json, pgTable, text, timestamp, varchar, boolean, integer } from 'drizzle-orm/pg-core';

const uuid = sql`uuid_generate_v4()`;

export const projects = pgTable('project', {
  id: text('id').primaryKey().default(uuid).notNull(),
  name: varchar('name').notNull(),
  transcriptionModel: varchar('transcription_model').notNull(),
  visionModel: varchar('vision_model').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at'),
  content: json('content'),
  userId: varchar('user_id').notNull(),
  image: varchar('image'),
  members: text('members').array(),
});

export const profile = pgTable('profile', {
  id: text('id').primaryKey().notNull(),
  customerId: text('customer_id'),
  subscriptionId: text('subscription_id'),
  productId: text('product_id'),
});

export const categories = pgTable('categories', {
  id: text('id').primaryKey().default(uuid).notNull(),
  name: varchar('name').notNull().unique(),
  description: text('description'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at'),
});

export const flow_templates = pgTable('flow_templates', {
  id: text('id').primaryKey().default(uuid).notNull(),
  name: varchar('name').notNull(),
  description: text('description'),
  flow_json: json('flow_json').notNull(),
  category_id: text('category_id').references(() => categories.id, { onDelete: 'set null' }),
  user_id: varchar('user_id'), // Can be null for admin-uploaded templates
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at'),
  is_public: boolean('is_public').default(true).notNull(),
  upvotes: integer('upvotes').default(0).notNull(),
  // Consider adding: version, tags (text array), preview_image_url (varchar)
});
