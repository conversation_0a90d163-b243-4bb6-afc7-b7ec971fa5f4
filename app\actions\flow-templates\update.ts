'use server';

import { db } from '@/lib/db';
import { flow_templates } from '@/schema';
import { revalidatePath } from 'next/cache';
import { z } from 'zod';
import { auth } from '@clerk/nextjs/server';
import { eq } from 'drizzle-orm';

const UpdateFlowTemplateSchema = z.object({
  id: z.string().min(1, { message: 'Template ID is required' }),
  name: z.string().min(1, { message: 'Template name is required' }).optional(),
  description: z.string().optional(),
  flow_json: z.string().optional(), // Assuming flow_json is passed as a string
  category_id: z.string().nullable().optional(), // Allow unsetting category
  is_public: z.boolean().optional(),
});

export async function updateFlowTemplateAction(formData: FormData) {
  const { userId } = auth();
  if (!userId) {
    return {
      success: false,
      error: 'Unauthorized: You must be logged in to update a flow template.',
    };
  }

  let flowJsonParsed;
  const flowJsonString = formData.get('flow_json') as string | undefined;
  if (flowJsonString) {
    try {
      flowJsonParsed = JSON.parse(flowJsonString);
    } catch (e) {
      return {
        success: false,
        error: 'Invalid Flow JSON provided.',
      };
    }
  }

  const isPublicString = formData.get('is_public');
  let isPublicValue: boolean | undefined;
  if (isPublicString !== null && isPublicString !== undefined) {
    isPublicValue = String(isPublicString).toLowerCase() === 'true';
  }

  const validatedFields = UpdateFlowTemplateSchema.safeParse({
    id: formData.get('id'),
    name: formData.get('name'),
    description: formData.get('description'),
    flow_json: flowJsonString, // Validate as string, use parsed for DB
    category_id: formData.get('category_id'),
    is_public: isPublicValue,
  });

  if (!validatedFields.success) {
    return {
      success: false,
      error: 'Invalid fields for flow template update.',
      errors: validatedFields.error.flatten().fieldErrors,
    };
  }

  const { id, name, description, category_id, is_public } = validatedFields.data;

  // Ensure at least one field is being updated
  if (!name && description === undefined && !flowJsonParsed && category_id === undefined && is_public === undefined) {
    return {
      success: false,
      error: 'No changes provided to update the flow template.',
    };
  }

  try {
    const currentTemplate = await db.query.flow_templates.findFirst({
        where: eq(flow_templates.id, id),
    });

    if (!currentTemplate) {
        return { success: false, error: 'Flow template not found.' };
    }

    // TODO: Add role-based access control: Ensure user is owner or admin
    // if (currentTemplate.user_id !== userId && !userIsAdmin) {
    //   return { success: false, error: 'Forbidden: You do not have permission to update this template.' };
    // }

    const updateData: Partial<typeof flow_templates.$inferInsert> = {};
    if (name) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (flowJsonParsed) updateData.flow_json = flowJsonParsed;
    if (category_id !== undefined) updateData.category_id = category_id; // Handles null to unset
    if (is_public !== undefined) updateData.is_public = is_public;
    updateData.updated_at = new Date();

    const [updatedTemplate] = await db
      .update(flow_templates)
      .set(updateData)
      .where(eq(flow_templates.id, id))
      .returning();

    if (!updatedTemplate) {
      return {
        success: false,
        error: 'Flow template not found or no changes made.', // Should be caught by findFirst above
      };
    }

    revalidatePath('/admin/flow-templates');
    revalidatePath(`/admin/flow-templates/${id}`);
    revalidatePath('/flows');
    if (updatedTemplate.category_id) {
      revalidatePath(`/flows/category/${updatedTemplate.category_id}`);
    }
    if (currentTemplate.category_id && currentTemplate.category_id !== updatedTemplate.category_id) {
        revalidatePath(`/flows/category/${currentTemplate.category_id}`); // Revalidate old category page if changed
    }


    return {
      success: true,
      data: updatedTemplate,
      message: 'Flow template updated successfully.',
    };
  } catch (error) {
    console.error('Error updating flow template:', error);
    return {
      success: false,
      error: 'Failed to update flow template. Please try again.',
    };
  }
}