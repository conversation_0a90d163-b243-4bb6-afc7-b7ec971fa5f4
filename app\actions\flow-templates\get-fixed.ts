'use server';

import { db } from '@/lib/database';
import type { Category } from '@/schema';
import { z } from 'zod';

// Define the FlowTemplateWithUserAndCategory type
export type FlowTemplateWithUserAndCategory = {
  id: string;
  name: string;
  description: string | null;
  category_id: string | null;
  categoryName?: string | null;
  category?: Category | null;
  user_id: string;
  created_at: Date | null;
  updated_at: Date | null;
  is_public: boolean | null;
  upvotes: number | null;
  flow_json?: any;
};

const GetFlowTemplatesSchema = z.object({
  categoryId: z.string().optional(),
  userId: z.string().optional(),
  searchTerm: z.string().optional(),
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().default(10),
  orderBy: z.enum(['createdAt', 'upvotes', 'name']).default('createdAt'),
  orderDirection: z.enum(['asc', 'desc']).default('desc'),
  adminMode: z.boolean().default(false),
});

export async function getFlowTemplatesAction(
  params: z.infer<typeof GetFlowTemplatesSchema>
) {
  try {
    const validatedParams = GetFlowTemplatesSchema.safeParse(params);

    if (!validatedParams.success) {
      return {
        success: false,
        error: 'Invalid parameters for fetching flow templates.',
        errors: validatedParams.error.flatten().fieldErrors,
      };
    }

    const { page, limit } = validatedParams.data;

    // Use our mock database implementation
    const templates = await db.query.flow_templates.findMany();

    // Return the data with pagination info
    return {
      success: true,
      data: templates,
      pagination: {
        page,
        limit,
        totalCount: templates.length,
        totalPages: Math.ceil(templates.length / limit),
      },
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return {
      success: false,
      error: `Failed to fetch flow templates: ${errorMessage}`,
    };
  }
}

export async function getFlowTemplateByIdAction(id: string, adminMode = false) {
  if (!id) {
    return { success: false, error: 'Template ID is required.' };
  }

  try {
    // Use the mock database
    const template = await db.query.flow_templates.findFirst();

    if (!template) {
      return {
        success: false,
        error: 'Flow template not found or not accessible.',
      };
    }

    // Return the template with its category
    return {
      success: true,
      data: {
        ...template,
        category: {
          id: 'cat-1',
          name: 'Text Generation',
          description: 'Templates for generating various types of text content',
          createdAt: new Date(),
          updatedAt: null,
        },
      },
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return {
      success: false,
      error: `Failed to fetch flow template with ID ${id}: ${errorMessage}`,
    };
  }
}
