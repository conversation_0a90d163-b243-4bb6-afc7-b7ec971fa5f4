// Mock data for projects
const mockProjectData = {
  id: 'mock-project-1',
  name: 'Demo Project',
  transcriptionModel: 'openai/whisper',
  visionModel: 'openai/gpt4-vision',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  content: {},
  userId: 'mock-user-id',
  image: null,
  members: [],
};

// Mock data for categories
const mockCategoriesData = [
  {
    id: 'cat-1',
    name: 'Text Generation',
    description: 'Templates for generating various types of text content',
    createdAt: new Date().toISOString(),
    updatedAt: null,
  },
  {
    id: 'cat-2',
    name: 'Image Processing',
    description: 'Templates for image generation and manipulation',
    createdAt: new Date().toISOString(),
    updatedAt: null,
  },
  {
    id: 'cat-3',
    name: 'Data Analysis',
    description: 'Templates for analyzing and processing data',
    createdAt: new Date().toISOString(),
    updatedAt: null,
  },
];

// Mock data for flow templates
const mockFlowTemplatesData = [
  {
    id: 'flow-1',
    name: 'Basic Text Generator',
    description: 'A simple flow for generating text content',
    flow_json: { nodes: [], edges: [] },
    category_id: 'cat-1',
    user_id: 'system',
    created_at: new Date().toISOString(),
    updated_at: null,
    is_public: true,
    upvotes: 42,
  },
  {
    id: 'flow-2',
    name: 'Image Creator',
    description: 'Create images with AI',
    flow_json: { nodes: [], edges: [] },
    category_id: 'cat-2',
    user_id: 'system',
    created_at: new Date().toISOString(),
    updated_at: null,
    is_public: true,
    upvotes: 35,
  },
];

// Maps tables to their mock data
const mockData = {
  projects: [mockProjectData],
  categories: mockCategoriesData,
  flow_templates: mockFlowTemplatesData,
};

// Mock database implementation
export const mockDb = {
  // Basic query builder API
  select: () => ({
    from: (table) => ({
      where: (condition) => {
        const tableName = table._.name;
        return mockData[tableName] || [];
      },
      orderBy: () => mockData[table._.name] || [],
      limit: () => ({
        offset: () => mockData[table._.name] || [],
      }),
      leftJoin: () => ({
        where: () => ({
          limit: () => ({
            offset: () => {
              // For flow templates with category join
              if (table._.name === 'flow_templates') {
                return mockFlowTemplatesData.map((template) => {
                  const category = mockCategoriesData.find(
                    (c) => c.id === template.category_id
                  );
                  return {
                    ...template,
                    categoryName: category?.name,
                  };
                });
              }
              return [];
            },
          }),
        }),
      }),
    }),
  }),

  // New query API
  query: {
    categories: {
      findMany: ({ orderBy } = {}) => {
        return mockCategoriesData;
      },
      findFirst: ({ where } = {}) => {
        return mockCategoriesData[0];
      },
    },
    flow_templates: {
      findMany: ({ where, orderBy, limit, offset } = {}) => {
        return mockFlowTemplatesData.map((template) => {
          const category = mockCategoriesData.find(
            (c) => c.id === template.category_id
          );
          return {
            ...template,
            categoryName: category?.name,
          };
        });
      },
      findFirst: ({ where } = {}) => {
        return {
          ...mockFlowTemplatesData[0],
          categoryName: mockCategoriesData[0]?.name,
        };
      },
    },
    projects: {
      findMany: () => [mockProjectData],
      findFirst: () => mockProjectData,
    },
  },

  // Helper for the interface
  from: (table) => mockData[table._.name] || [],

  // For raw SQL execution
  execute: async (query) => {
    return [{ count: '0' }];
  },
};
