'use server';

import { database as db } from '@/lib/database';
import { flow_templates } from '@/schema'; // Assuming you might add a user_upvotes table later
import { revalidatePath } from 'next/cache';
import { z } from 'zod';
import { auth } from '@clerk/nextjs/server';
import { eq, sql } from 'drizzle-orm';

// For a more robust upvote system, you'd typically have a separate table like `user_template_upvotes`
// to track which user upvoted which template, preventing multiple upvotes from the same user.
// Then, the `upvotes` count on `flow_templates` would be a derived or periodically updated value.
// For simplicity in this step, we'll directly increment/decrement the count.
// This means a user could theoretically upvote multiple times if not handled on the client-side
// or by adding a more complex tracking mechanism later.

const UpvoteFlowTemplateSchema = z.object({
  templateId: z.string().min(1, { message: 'Template ID is required' }),
  // KIV: Add 'actionType: z.enum(['upvote', 'downvote'])' if you want to allow removing upvote
});

export async function toggleUpvoteAction(params: { templateId: string }) { // Changed to accept params directly for client-side call
  const { userId } = auth();
  if (!userId) {
    return {
      success: false,
      error: 'Unauthorized: You must be logged in to upvote.',
    };
  }

  const validatedFields = UpvoteFlowTemplateSchema.safeParse({
    templateId: params.templateId,
  });

  if (!validatedFields.success) {
    return {
      success: false,
      error: 'Invalid template ID for upvoting.',
      errors: validatedFields.error.flatten().fieldErrors,
    };
  }

  const { templateId } = validatedFields.data;

  try {
    // This is a simplified toggle. A real implementation would check if the user has already upvoted.
    // We'll just increment for now. To make it a toggle, you'd need to know the current state
    // or use a separate table as mentioned above.

    // TODO: Implement a proper user-specific upvote tracking system using a join table (e.g., user_template_upvotes).
    // For now, this action will just increment the total upvotes.
    // The `upvotedByCurrentUser` is a mock value to help the client-side optimistic update.

    // Simulate finding if the user has already upvoted (mocked)
    // In a real scenario, you would query the join table:
    // const existingUpvote = await db.query.user_template_upvotes.findFirst({
    //   where: and(eq(user_template_upvotes.userId, userId), eq(user_template_upvotes.templateId, templateId))
    // });

    let newUpvoteCountSql;
    let upvotedByCurrentUserMock;

    // This is a placeholder for toggling logic. 
    // For this example, we'll always increment, but return a toggled mock status.
    // A true toggle would look like:
    // if (existingUpvote) {
    //   await db.delete(user_template_upvotes).where(eq(user_template_upvotes.id, existingUpvote.id));
    //   newUpvoteCountSql = sql`${flow_templates.upvotes} - 1`;
    //   upvotedByCurrentUserMock = false;
    // } else {
    //   await db.insert(user_template_upvotes).values({ userId, templateId, createdAt: new Date() });
    //   newUpvoteCountSql = sql`${flow_templates.upvotes} + 1`;
    //   upvotedByCurrentUserMock = true;
    // }
    
    // Simplified: Always increment for now, but provide a mock toggle status for the client.
    newUpvoteCountSql = sql`${flow_templates.upvotes} + 1`;
    // This mock should ideally come from the client or be based on a real check.
    // Forcing it to 'true' on upvote action, 'false' would require a 'remove upvote' action.
    upvotedByCurrentUserMock = true; 

    const [updatedTemplate] = await db
      .update(flow_templates)
      .set({
        upvotes: newUpvoteCountSql,
        updated_at: new Date(),
      })
      .where(eq(flow_templates.id, templateId))
      .returning({ 
        id: flow_templates.id, 
        upvotes: flow_templates.upvotes, 
        category_id: flow_templates.category_id 
      });

    if (!updatedTemplate) {
      return {
        success: false,
        error: 'Flow template not found or failed to update.',
      };
    }

    revalidatePath(`/flows`);
    revalidatePath(`/admin/flow-templates`); // Revalidate admin if they can see upvotes
    // Consider revalidating specific template detail page if it exists
    // revalidatePath(`/flows/${templateId}`); 

    return {
      success: true,
      data: { 
        id: updatedTemplate.id,
        upvotes: updatedTemplate.upvotes,
        upvotedByCurrentUser: upvotedByCurrentUserMock // This is the crucial part for client UI
      },
      message: upvotedByCurrentUserMock ? 'Upvoted successfully!' : 'Upvote removed.',
    };
  } catch (error) {
    console.error('Error upvoting flow template:', error);
    return {
      success: false,
      error: 'Failed to register upvote. Please try again.',
    };
  }
}