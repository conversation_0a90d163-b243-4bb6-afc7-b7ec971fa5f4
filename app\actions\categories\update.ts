'use server';

import { db } from '@/lib/db';
import { categories } from '@/schema';
import { revalidatePath } from 'next/cache';
import { z } from 'zod';
import { auth } from '@clerk/nextjs/server';
import { eq } from 'drizzle-orm';

const UpdateCategorySchema = z.object({
  id: z.string().min(1, { message: 'Category ID is required' }),
  name: z.string().min(1, { message: 'Category name is required' }).optional(),
  description: z.string().optional(),
});

export async function updateCategoryAction(formData: FormData) {
  const { userId } = auth();
  if (!userId) {
    return {
      success: false,
      error: 'Unauthorized: You must be logged in to update a category.',
    };
  }

  // TODO: Add role-based access control to ensure only admins can update categories

  const validatedFields = UpdateCategorySchema.safeParse({
    id: formData.get('id'),
    name: formData.get('name'),
    description: formData.get('description'),
  });

  if (!validatedFields.success) {
    return {
      success: false,
      error: 'Invalid fields for category update.',
      errors: validatedFields.error.flatten().fieldErrors,
    };
  }

  const { id, name, description } = validatedFields.data;

  // Ensure at least one field is being updated
  if (!name && description === undefined) {
    return {
      success: false,
      error: 'No changes provided to update the category.',
    };
  }

  try {
    const updateData: Partial<{ name: string; description: string | null; updatedAt: Date }> = {};
    if (name) updateData.name = name;
    if (description !== undefined) updateData.description = description; // Allow setting description to empty string
    updateData.updatedAt = new Date();

    const [updatedCategory] = await db
      .update(categories)
      .set(updateData)
      .where(eq(categories.id, id))
      .returning();

    if (!updatedCategory) {
      return {
        success: false,
        error: 'Category not found or no changes made.',
      };
    }

    revalidatePath('/admin/flow-templates');
    revalidatePath('/flows');
    revalidatePath(`/admin/categories/${id}`); // If there's a specific admin page for a category

    return {
      success: true,
      data: updatedCategory,
      message: 'Category updated successfully.',
    };
  } catch (error) {
    console.error('Error updating category:', error);
     if (error instanceof Error && error.message.includes('duplicate key value violates unique constraint')) {
        return {
            success: false,
            error: 'A category with this name already exists.',
        };
    }
    return {
      success: false,
      error: 'Failed to update category. Please try again.',
    };
  }
}