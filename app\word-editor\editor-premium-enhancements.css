/* 
   Premium Enhancements for Tersa Word Editor
   Comprehensive styling improvements for a professional, modern editor experience
*/

/* ===== EDITOR CONTAINER & LAYOUT ===== */

/* Enhanced editor card with premium glass-morphism effect */
.editor-card {
  border-radius: 16px;
  overflow: hidden;
  background: linear-gradient(135deg, 
    hsl(var(--background) / 0.95), 
    hsl(var(--background) / 0.9));
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid hsl(var(--border) / 0.5);
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.1),
    0 8px 25px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  position: relative;
}

.dark .editor-card {
  background: linear-gradient(135deg, 
    hsl(var(--background) / 0.98), 
    hsl(var(--background) / 0.95));
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.3),
    0 8px 25px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

/* Enhanced editor header */
.editor-header {
  background: linear-gradient(135deg, 
    hsl(var(--muted) / 0.3), 
    hsl(var(--muted) / 0.1));
  border-bottom: 1px solid hsl(var(--border) / 0.6);
  backdrop-filter: blur(10px);
  padding: 1.5rem 2rem;
}

/* Premium editor title styling */
.editor-title {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, 
    hsl(var(--foreground)), 
    hsl(var(--primary)));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.02em;
}

/* Enhanced action buttons */
.editor-action-button {
  backdrop-filter: blur(8px);
  background-color: hsl(var(--background) / 0.8);
  border: 1px solid hsl(var(--border) / 0.6);
  transition: all 0.25s cubic-bezier(0.16, 1, 0.3, 1);
  font-weight: 500;
}

.editor-action-button:hover {
  background-color: hsl(var(--accent) / 0.15);
  border-color: hsl(var(--accent) / 0.4);
  transform: translateY(-1px);
  box-shadow: 
    0 4px 12px hsl(var(--accent) / 0.2),
    0 2px 4px rgba(0, 0, 0, 0.1);
}

/* ===== EDITOR TABS ===== */

.editor-tabs {
  background: linear-gradient(135deg, 
    hsl(var(--muted) / 0.2), 
    hsl(var(--muted) / 0.05));
  border-radius: 12px;
  padding: 0.25rem;
  margin: 0.5rem;
}

.editor-tab {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.25s cubic-bezier(0.16, 1, 0.3, 1);
  position: relative;
  overflow: hidden;
}

.editor-tab[data-state="active"] {
  background: linear-gradient(135deg, 
    hsl(var(--background)), 
    hsl(var(--background) / 0.95));
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* ===== EDITOR CONTENT AREA ===== */

/* Enhanced paper effect for editor content */
.editor-paper {
  background: linear-gradient(135deg, 
    hsl(var(--background)), 
    hsl(var(--background) / 0.98));
  position: relative;
  border-radius: 12px;
  overflow: hidden;
}

.editor-paper::before {
  content: '';
  position: absolute;
  inset: 0;
  background-image: 
    radial-gradient(circle at 2px 2px, hsl(var(--muted) / 0.1) 1px, transparent 0);
  background-size: 24px 24px;
  pointer-events: none;
  opacity: 0.4;
}

/* ===== SELECTION ENHANCEMENTS ===== */

/* Enhanced text selection styling */
.selection-editor ::selection {
  background: linear-gradient(135deg, 
    hsl(var(--primary) / 0.3), 
    hsl(var(--primary) / 0.2));
  color: hsl(var(--primary-foreground));
  border-radius: 2px;
}

.selection-editor:focus ::selection {
  background: linear-gradient(135deg, 
    hsl(var(--primary) / 0.4), 
    hsl(var(--primary) / 0.3));
}

/* ===== EDITOR FOOTER ===== */

.editor-footer {
  background: linear-gradient(135deg, 
    hsl(var(--muted) / 0.2), 
    hsl(var(--muted) / 0.05));
  border-top: 1px solid hsl(var(--border) / 0.6);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
}

/* Enhanced stats counters */
.stats-counter {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: hsl(var(--background) / 0.8);
  border: 1px solid hsl(var(--border) / 0.4);
  border-radius: 8px;
  backdrop-filter: blur(8px);
  transition: all 0.25s ease;
}

.stats-counter:hover {
  background: hsl(var(--accent) / 0.1);
  border-color: hsl(var(--accent) / 0.3);
  transform: translateY(-1px);
}

.stats-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: hsl(var(--muted-foreground));
}

.stats-value {
  font-size: 0.875rem;
  font-weight: 700;
  color: hsl(var(--foreground));
}

/* ===== ANIMATIONS ===== */

/* Enhanced editor entrance animation */
@keyframes editor-in {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.98);
    filter: blur(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

.animate-editor-in {
  animation: editor-in 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
  .editor-header {
    padding: 1rem 1.5rem;
  }
  
  .editor-title {
    font-size: 1.25rem;
  }
  
  .editor-footer {
    padding: 0.75rem 1.5rem;
  }
  
  .stats-counter {
    padding: 0.375rem 0.75rem;
  }
  
  .stats-label,
  .stats-value {
    font-size: 0.8rem;
  }
}

@media (max-width: 640px) {
  .editor-card {
    border-radius: 12px;
    margin: 0.5rem;
  }
  
  .editor-header {
    padding: 0.75rem 1rem;
  }
  
  .editor-footer {
    padding: 0.5rem 1rem;
  }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */

/* Enhanced focus indicators */
.editor-card *:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
  border-radius: 4px;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .editor-card,
  .editor-action-button,
  .editor-tab,
  .stats-counter {
    transition: none;
  }
  
  .animate-editor-in {
    animation: none;
  }
  
  .ProseMirror h1::after {
    animation: none;
  }
}
