'use client';

import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { useMainEditor } from '@/providers/main-editor';
import CharacterCount from '@tiptap/extension-character-count';
import Placeholder from '@tiptap/extension-placeholder';
import Typography from '@tiptap/extension-typography';
import { EditorContent, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import {
  BoldIcon,
  CodeIcon,
  Heading1Icon,
  Heading2Icon,
  Heading3Icon,
  ItalicIcon,
  ListIcon,
  ListOrderedIcon,
  TextQuoteIcon,
  TrashIcon,
} from 'lucide-react';
import { useEffect } from 'react';

export type MainEditorProps = {
  className?: string;
};

export const MainEditor = ({ className }: MainEditorProps) => {
  const { content, setContent, addContent, clearContent } = useMainEditor();

  const editor = useEditor({
    extensions: [
      StarterKit,
      Typography,
      Placeholder.configure({
        placeholder: 'Content from workflow nodes will appear here...',
      }),
      CharacterCount,
    ],
    content,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      setContent(html);
    },
  });

  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      editor.commands.setContent(content);
    }
  }, [content, editor]);

  return (
    <div
      className={cn(
        'flex h-full w-full flex-col rounded-md border bg-background',
        className
      )}
    >
      <div className="flex items-center border-b p-2">
        <h2 className="font-medium text-lg">Main Editor</h2>
        <Separator className="mx-2 h-6" orientation="vertical" />
        <div className="flex items-center space-x-1">
          <Button
            className={editor?.isActive('bold') ? 'bg-accent' : ''}
            onClick={() => editor?.chain().focus().toggleBold().run()}
            size="sm"
            variant="ghost"
          >
            <BoldIcon className="h-4 w-4" />
          </Button>
          <Button
            className={editor?.isActive('italic') ? 'bg-accent' : ''}
            onClick={() => editor?.chain().focus().toggleItalic().run()}
            size="sm"
            variant="ghost"
          >
            <ItalicIcon className="h-4 w-4" />
          </Button>
          <Button
            className={
              editor?.isActive('heading', { level: 1 }) ? 'bg-accent' : ''
            }
            onClick={() =>
              editor?.chain().focus().toggleHeading({ level: 1 }).run()
            }
            size="sm"
            variant="ghost"
          >
            <Heading1Icon className="h-4 w-4" />
          </Button>
          <Button
            className={
              editor?.isActive('heading', { level: 2 }) ? 'bg-accent' : ''
            }
            onClick={() =>
              editor?.chain().focus().toggleHeading({ level: 2 }).run()
            }
            size="sm"
            variant="ghost"
          >
            <Heading2Icon className="h-4 w-4" />
          </Button>
          <Button
            className={
              editor?.isActive('heading', { level: 3 }) ? 'bg-accent' : ''
            }
            onClick={() =>
              editor?.chain().focus().toggleHeading({ level: 3 }).run()
            }
            size="sm"
            variant="ghost"
          >
            <Heading3Icon className="h-4 w-4" />
          </Button>
          <Button
            className={editor?.isActive('bulletList') ? 'bg-accent' : ''}
            onClick={() => editor?.chain().focus().toggleBulletList().run()}
            size="sm"
            variant="ghost"
          >
            <ListIcon className="h-4 w-4" />
          </Button>
          <Button
            className={editor?.isActive('orderedList') ? 'bg-accent' : ''}
            onClick={() => editor?.chain().focus().toggleOrderedList().run()}
            size="sm"
            variant="ghost"
          >
            <ListOrderedIcon className="h-4 w-4" />
          </Button>
          <Button
            className={editor?.isActive('blockquote') ? 'bg-accent' : ''}
            onClick={() => editor?.chain().focus().toggleBlockquote().run()}
            size="sm"
            variant="ghost"
          >
            <TextQuoteIcon className="h-4 w-4" />
          </Button>
          <Button
            className={editor?.isActive('codeBlock') ? 'bg-accent' : ''}
            onClick={() => editor?.chain().focus().toggleCodeBlock().run()}
            size="sm"
            variant="ghost"
          >
            <CodeIcon className="h-4 w-4" />
          </Button>
          <Button
            onClick={() => {
              editor?.commands.clearContent();
              clearContent();
            }}
            size="sm"
            variant="ghost"
          >
            <TrashIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>
      <EditorContent
        className="dark:prose-invert prose prose-sm max-w-none flex-grow overflow-y-auto p-4 focus:outline-none"
        editor={editor}
      />
    </div>
  );
};
