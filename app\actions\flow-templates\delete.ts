'use server';

import { db } from '@/lib/db';
import { flow_templates } from '@/schema';
import { revalidatePath } from 'next/cache';
import { z } from 'zod';
import { auth } from '@clerk/nextjs/server';
import { eq } from 'drizzle-orm';

const DeleteFlowTemplateSchema = z.object({
  id: z.string().min(1, { message: 'Template ID is required' }),
});

export async function deleteFlowTemplateAction(formData: FormData) {
  const { userId } = auth();
  if (!userId) {
    return {
      success: false,
      error: 'Unauthorized: You must be logged in to delete a flow template.',
    };
  }

  const validatedFields = DeleteFlowTemplateSchema.safeParse({
    id: formData.get('id'),
  });

  if (!validatedFields.success) {
    return {
      success: false,
      error: 'Invalid template ID for deletion.',
      errors: validatedFields.error.flatten().fieldErrors,
    };
  }

  const { id } = validatedFields.data;

  try {
    const templateToDelete = await db.query.flow_templates.findFirst({
        where: eq(flow_templates.id, id),
        columns: { user_id: true, category_id: true },
    });

    if (!templateToDelete) {
        return { success: false, error: 'Flow template not found.' };
    }

    // TODO: Add role-based access control: Ensure user is owner or admin
    // if (templateToDelete.user_id !== userId && !userIsAdmin) {
    //   return { success: false, error: 'Forbidden: You do not have permission to delete this template.' };
    // }

    const [deletedTemplate] = await db
      .delete(flow_templates)
      .where(eq(flow_templates.id, id))
      .returning({ id: flow_templates.id, category_id: flow_templates.category_id });

    if (!deletedTemplate) {
      // This case should ideally be caught by the findFirst check above
      return {
        success: false,
        error: 'Flow template not found or already deleted.',
      };
    }

    revalidatePath('/admin/flow-templates');
    revalidatePath('/flows');
    if (deletedTemplate.category_id) {
      revalidatePath(`/flows/category/${deletedTemplate.category_id}`);
    }

    return {
      success: true,
      message: 'Flow template deleted successfully.',
    };
  } catch (error) {
    console.error('Error deleting flow template:', error);
    return {
      success: false,
      error: 'Failed to delete flow template. Please try again.',
    };
  }
}