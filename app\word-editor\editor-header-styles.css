/* Custom header styles for word editor */

.editor-header-container {
  height: var(--header-height, 60px);
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.98),
    rgba(255, 255, 255, 0.95)
  );
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  transition: all 0.2s ease;
}

.dark .editor-header-container {
  background: linear-gradient(
    to bottom,
    rgba(15, 23, 42, 0.98),
    rgba(15, 23, 42, 0.95)
  );
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2), 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Header title animation */
.editor-title {
  position: relative;
  display: inline-block;
  overflow: hidden;
}

.editor-title::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary);
  transition: width 0.3s ease;
}

.editor-title:hover::after {
  width: 100%;
}

/* Header action buttons */
.editor-action-button {
  border-radius: 6px;
  height: 36px;
  padding: 0 12px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.editor-action-button:hover {
  background-color: rgba(var(--primary-rgb), 0.08);
}

/* Ensure header has a minimum height */
.editor-header-container {
  min-height: var(--header-height, 60px);
}
