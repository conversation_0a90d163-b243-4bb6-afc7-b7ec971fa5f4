/* Enhanced selection styles for better visual feedback */

/* Base selection styling */
.selection-editor ::selection {
  background-color: hsla(var(--primary) / 0.15) !important;
  color: inherit !important;
  text-shadow: none !important;
  border-radius: 2px;
  box-shadow: 0 0 0 1px hsla(var(--primary) / 0.1);
  transition: all 0.15s ease;
}

.dark .selection-editor ::selection {
  background-color: hsla(var(--primary) / 0.25) !important;
  color: hsl(var(--primary-foreground)) !important;
  box-shadow: 0 0 0 1px hsla(var(--primary) / 0.2);
}

/* Selected headings styling */
.ProseMirror h1[data-selection-active="true"],
.ProseMirror h2[data-selection-active="true"],
.ProseMirror h3[data-selection-active="true"],
.ProseMirror h4[data-selection-active="true"],
.ProseMirror h5[data-selection-active="true"],
.ProseMirror h6[data-selection-active="true"] {
  position: relative;
  background-color: rgba(59, 130, 246, 0.12);
  border-radius: 6px;
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.15);
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
}

/* Add left marker for selected headings */
.ProseMirror h1[data-selection-active="true"]::before,
.ProseMirror h2[data-selection-active="true"]::before,
.ProseMirror h3[data-selection-active="true"]::before {
  content: "";
  position: absolute;
  left: -8px;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: rgba(59, 130, 246, 0.8);
  border-radius: 4px;
  animation: pulseMarker 1.5s ease-in-out infinite alternate;
}

@keyframes pulseMarker {
  0% {
    opacity: 0.7;
    box-shadow: 0 0 3px rgba(59, 130, 246, 0.4);
  }
  100% {
    opacity: 0.9;
    box-shadow: 0 0 6px rgba(59, 130, 246, 0.6);
  }
}

/* Specific heading selection styles */
.ProseMirror h1::selection,
.ProseMirror h1 *::selection {
  background-color: rgba(59, 130, 246, 0.28) !important;
}

.ProseMirror h2::selection,
.ProseMirror h2 *::selection {
  background-color: rgba(59, 130, 246, 0.26) !important;
}

.ProseMirror h3::selection,
.ProseMirror h3 *::selection {
  background-color: rgba(59, 130, 246, 0.24) !important;
}

/* Special styling for code and pre blocks */
.ProseMirror code::selection,
.ProseMirror code *::selection,
.ProseMirror pre::selection,
.ProseMirror pre *::selection {
  background-color: rgba(79, 70, 229, 0.25) !important;
}

.dark .ProseMirror code::selection,
.dark .ProseMirror code *::selection,
.dark .ProseMirror pre::selection,
.dark .ProseMirror pre *::selection {
  background-color: rgba(129, 140, 248, 0.3) !important;
}

/* Special styling for blockquotes */
.ProseMirror blockquote::selection,
.ProseMirror blockquote *::selection {
  background-color: rgba(107, 114, 128, 0.2) !important;
}

/* Selection in editor with active selection state */
.ProseMirror.has-text-selection ::selection {
  background-color: rgba(59, 130, 246, 0.32) !important;
}

.dark .ProseMirror.has-text-selection ::selection {
  background-color: rgba(96, 165, 250, 0.4) !important;
}

/* Improved z-index for bubble menus during selection */
.bubble-menu {
  z-index: 50;
  padding: 0.25rem !important; /* Reduced padding */
  border-radius: 0.375rem !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

/* More compact styling for the bubble menu buttons */
.bubble-menu button {
  padding: 0.25rem 0.5rem !important; /* Reduced padding */
  gap: 0.25rem !important; /* Reduced gap between icon and text */
  font-size: 0.875rem !important; /* Smaller font size */
  border-radius: 0.25rem !important;
}

/* Original selection menu classes for compatibility */
/* Styling for the text selection pop-up menu */
.selection-menu {
  background-color: #fff; /* White background */
  border: 1px solid #e2e8f0; /* Light gray border */
  border-radius: 0.375rem; /* Rounded corners */
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px
    rgba(0, 0, 0, 0.06); /* Subtle shadow */
  display: flex;
  gap: 0.25rem; /* Small gap between buttons */
  padding: 0.25rem; /* Original: 0.5rem or larger */
  opacity: 0; /* Initially hidden */
  transform: translateY(0.5rem) scale(0.95); /* Initial animation state */
  transition: opacity 0.15s ease-out, transform 0.15s ease-out;
  z-index: 50; /* Ensure it's above other content */
}

.selection-menu.is-active {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* Styling for individual buttons in the selection menu */
.selection-menu-button {
  background-color: transparent;
  border: none;
  border-radius: 0.25rem; /* Slightly rounded corners for buttons */
  color: #4a5568; /* Dark gray text */
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 0.25rem 0.5rem; /* Original: larger padding */
  gap: 0.25rem; /* Original: larger gap */
  font-size: 0.875rem; /* Original: larger font size */
  transition: background-color 0.15s ease;
}

.selection-menu-button:hover {
  background-color: #f7fafc; /* Very light gray on hover */
  color: #2d3748; /* Slightly darker text on hover */
}

.selection-menu-button.is-active {
  background-color: #edf2f7; /* Light gray for active button */
  color: #2d3748; /* Darker text for active button */
}

/* Icon styling within buttons */
.selection-menu-button svg {
  width: 1rem; /* 16px */
  height: 1rem; /* 16px */
}

/* Dark mode specific styles for the selection menu */
.dark .selection-menu {
  background-color: #2d3748; /* Dark background */
  border-color: #4a5568; /* Darker border */
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px
    rgba(0, 0, 0, 0.15); /* Darker shadow */
}

.dark .selection-menu-button {
  color: #e2e8f0; /* Light gray text in dark mode */
}

.dark .selection-menu-button:hover {
  background-color: #4a5568; /* Darker hover background */
  color: #f7fafc; /* Lighter text on hover */
}

.dark .selection-menu-button.is-active {
  background-color: #1a202c; /* Very dark active background */
  color: #f7fafc; /* Light text for active button */
}

/* Animation for selected headings */
@keyframes headingSelectedPulse {
  0% {
    background-color: rgba(59, 130, 246, 0.08);
  }
  50% {
    background-color: rgba(59, 130, 246, 0.16);
  }
  100% {
    background-color: rgba(59, 130, 246, 0.12);
  }
}

.heading-selected-animation {
  animation: headingSelectedPulse 0.5s ease-in-out;
}

/* Enhanced selection for paragraphs */
.ProseMirror p[data-selection-active="true"] {
  background-color: rgba(59, 130, 246, 0.08); /* Lighter blue highlight */
  border-radius: 4px;
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.1);
  padding: 2px 0; /* Minimal padding to avoid layout shift */
  margin: -2px 0; /* Counteract padding */
}

/* Enhanced selection for list items */
.ProseMirror li[data-selection-active="true"] > p,
.ProseMirror li[data-selection-active="true"] {
  background-color: rgba(59, 130, 246, 0.08);
  border-radius: 4px;
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.1);
  padding: 1px 0;
  margin: -1px 0;
}

/* Enhanced selection for code blocks */
.ProseMirror pre[data-selection-active="true"] {
  background-color: rgba(59, 130, 246, 0.1);
  border-radius: 4px;
  box-shadow: inset 2px 0 0 0 rgba(59, 130, 246, 0.7);
  padding-left: 6px;
  margin-left: -8px;
}

/* Enhanced selection for blockquotes */
.ProseMirror blockquote[data-selection-active="true"] {
  background-color: rgba(237, 137, 54, 0.08); /* Orange highlight for quotes */
  border-left-color: rgba(237, 137, 54, 0.7);
  box-shadow: 0 0 0 1px rgba(237, 137, 54, 0.1);
  padding: 2px 0;
  margin: -2px 0;
}

/* General active selection marker for any block */
.ProseMirror [data-selection-active="true"] {
  position: relative;
}

/* Ensure selection styles don't interfere with caret visibility */
.ProseMirror-focused [data-selection-active="true"] {
  caret-color: currentColor;
}

/* Style for the floating menu (slash commands, etc.) */
.floating-menu {
  background-color: #fff;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px
    rgba(0, 0, 0, 0.06);
  padding: 0.5rem;
  opacity: 0;
  transform: translateY(0.5rem) scale(0.95);
  transition: opacity 0.15s ease-out, transform 0.15s ease-out;
  z-index: 50;
}

.floating-menu.is-active {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.floating-menu-button {
  background-color: transparent;
  border: none;
  border-radius: 0.25rem;
  color: #4a5568;
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 0.5rem;
  gap: 0.5rem;
  font-size: 0.875rem;
  width: 100%;
  text-align: left;
  transition: background-color 0.15s ease;
}

.floating-menu-button:hover {
  background-color: #f7fafc;
  color: #2d3748;
}

.floating-menu-button.is-active {
  background-color: #edf2f7;
  color: #2d3748;
}

.dark .floating-menu {
  background-color: #2d3748;
  border-color: #4a5568;
}

.dark .floating-menu-button {
  color: #e2e8f0;
}

.dark .floating-menu-button:hover {
  background-color: #4a5568;
  color: #f7fafc;
}

.dark .floating-menu-button.is-active {
  background-color: #1a202c;
  color: #f7fafc;
}
