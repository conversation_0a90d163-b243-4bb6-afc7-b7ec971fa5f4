'use client';

import { FlowTemplateWithUserAndCategory } from '@/app/actions/flow-templates';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ThumbsUp, Download, Eye } from 'lucide-react';
import { useTransition } from 'react';
import { toast } from 'sonner';

interface FlowCardProps {
  template: FlowTemplateWithUserAndCategory;
  onUpvote: (templateId: string) => void;
  onImport: (templateId: string) => void;
  onViewDetails: (templateId: string) => void;
}

export function FlowCard({ template, onUpvote, onImport, onViewDetails }: FlowCardProps) {
  // const [isPending, startTransition] = useTransition();

  // const handleUpvoteClick = () => {
  //   startTransition(async () => {
  //     await onUpvote(template.id);
  //   });
  // };

  return (
    <Card key={template.id} className="flex flex-col h-full">
      {/* <CardHeader>
        <CardTitle className="text-xl line-clamp-2">{template.name}</CardTitle>
        <CardDescription>
          {template.category ? (
            <span className="text-xs px-2 py-0.5 bg-secondary text-secondary-foreground rounded-full inline-block">
              {template.category.name}
            </span>
          ) : (
            <span className="text-xs italic">Uncategorized</span>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-grow">
        <p className="text-sm text-muted-foreground line-clamp-3 mb-2 h-[60px]">
          {template.description || 'No description available.'}
        </p>
        <p className="text-xs text-muted-foreground">
          By: {template.user?.name || 'Unknown User'}
        </p>
      </CardContent>
      <CardFooter className="flex justify-between items-center mt-auto pt-4 border-t">
        <Button variant="outline" size="sm" onClick={handleUpvoteClick} disabled={isPending} aria-label={`Upvote ${template.name}">
          <ThumbsUp className={`h-4 w-4 mr-2 ${template.upvotedByCurrentUser ? 'fill-primary text-primary' : ''}`} />
          {template.upvotes}
        </Button>
        <div className="space-x-1 sm:space-x-2">
          <Button variant="ghost" size="icon" onClick={() => onViewDetails(template.id)} title="View Details (Coming Soon)" aria-label={`View details for ${template.name}" disabled>
            <Eye className="h-5 w-5" />
          </Button>
          <Button 
            variant="default" 
            size="sm" 
            onClick={() => onImport(template.id)} 
            title="Import Flow (Coming Soon)" 
            aria-label={`Import ${template.name}`}
            disabled // Disabled until import functionality is implemented
          >
            <Download className="h-4 w-4 md:mr-2" />
            <span className="hidden md:inline">Import</span>
          </Button>
        </div>
      </CardFooter> */}
      <div>Test Content</div>
    </Card>
  );
}