import { NextPage } from 'next';
import { getFlowTemplatesAction } from '@/app/actions/flow-templates';
import { getCategoriesAction } from '@/app/actions/categories';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { FlowTemplatesClientPage } from './_components/flow-templates-client-page';

// Re-exporting for potential use in other server components if needed, or direct import
export { getFlowTemplatesAction, getCategoriesAction };

const AdminFlowTemplatesPage: NextPage = async () => {
  // Fetch initial data on the server
  const templatesResult = await getFlowTemplatesAction({ adminMode: true, page: 1, limit: 10 }); // Fetch all for admin
  const categoriesResult = await getCategoriesAction();

  if (!templatesResult.success || !categoriesResult.success) {
    let errorMessages = [];
    if (!templatesResult.success) errorMessages.push(templatesResult.error || 'Could not fetch flow templates.');
    if (!categoriesResult.success) errorMessages.push(categoriesResult.error || 'Could not fetch categories.');
    
    return (
      <Card>
        <CardHeader>
          <CardTitle>Manage Flow Templates</CardTitle>
          <CardDescription>Error loading data.</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-destructive">{errorMessages.join(' ')} Please try again.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <FlowTemplatesClientPage
      initialTemplates={templatesResult.data || []}
      initialCategories={categoriesResult.data || []}
      initialPagination={templatesResult.pagination}
    />
  );
};

export default AdminFlowTemplatesPage;