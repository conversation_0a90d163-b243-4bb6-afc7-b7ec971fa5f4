'use client';

import { useState } from 'react';
import { categories } from '@/schema';
import type { InferSelectModel } from 'drizzle-orm';

type Category = InferSelectModel<typeof categories>;
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { PlusCircle, Edit, Trash2, MoreHorizontal } from 'lucide-react';
import { CategoryFormModal } from './category-form-modal'; // To be created
import { DeleteConfirmationDialog } from '@/components/ui/delete-confirmation-dialog'; // To be created or use existing
import { createCategoryAction, updateCategoryAction, deleteCategoryAction } from '@/app/actions/categories';
import { toast } from 'sonner';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

interface CategoryClientPageProps {
  initialCategories: Category[]; // This now uses the inferred type
}

export function CategoryClientPage({ initialCategories }: CategoryClientPageProps) {
  const [categories, setCategories] = useState<Category[]>(initialCategories); // This now uses the inferred type
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null); // This now uses the inferred type
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState<Category | null>(null); // This now uses the inferred type

  const handleOpenCreateModal = () => {
    setEditingCategory(null);
    setIsModalOpen(true);
  };

  const handleOpenEditModal = (category: Category) => { // This now uses the inferred type
    setEditingCategory(category);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingCategory(null);
  };

  const handleOpenDeleteDialog = (category: Category) => { // This now uses the inferred type
    setCategoryToDelete(category);
    setIsDeleteDialogOpen(true);
  };

  const handleCloseDeleteDialog = () => {
    setCategoryToDelete(null);
    setIsDeleteDialogOpen(false);
  };

  const handleSubmitCategory = async (formData: FormData) => {
    const action = editingCategory ? updateCategoryAction : createCategoryAction;
    if (editingCategory) {
      formData.append('id', editingCategory.id);
    }

    const result = await action(formData);
    if (result.success && result.data) {
      toast.success(editingCategory ? 'Category updated successfully!' : 'Category created successfully!');
      if (editingCategory) {
        setCategories(categories.map(cat => cat.id === result.data.id ? result.data as Category : cat));
      } else {
        setCategories([result.data as Category, ...categories]);
      }
      handleCloseModal();
    } else {
      toast.error(result.error || 'An unknown error occurred.');
    }
  };

  const handleDeleteCategory = async () => {
    if (!categoryToDelete) return;
    const formData = new FormData();
    formData.append('id', categoryToDelete.id);

    const result = await deleteCategoryAction(formData);
    if (result.success) {
      toast.success('Category deleted successfully!');
      setCategories(categories.filter(cat => cat.id !== categoryToDelete.id));
      handleCloseDeleteDialog();
    } else {
      toast.error(result.error || 'Failed to delete category.');
    }
  };

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Manage Categories</CardTitle>
            <CardDescription>View, create, edit, and delete flow template categories.</CardDescription>
          </div>
          <Button onClick={handleOpenCreateModal} size="sm">
            <PlusCircle className="mr-2 h-4 w-4" /> Add Category
          </Button>
        </CardHeader>
        <CardContent>
          {categories.length === 0 ? (
            <p className="text-muted-foreground">No categories found. Get started by adding one!</p>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Created At</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {categories.map((category) => (
                  <TableRow key={category.id}>
                    <TableCell className="font-medium">{category.name}</TableCell>
                    <TableCell className="text-muted-foreground truncate max-w-xs">{category.description || '-'}</TableCell>
                    <TableCell>{new Date(category.createdAt).toLocaleDateString()}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleOpenEditModal(category)}>
                            <Edit className="mr-2 h-4 w-4" /> Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleOpenDeleteDialog(category)} className="text-destructive">
                            <Trash2 className="mr-2 h-4 w-4" /> Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {isModalOpen && (
        <CategoryFormModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          onSubmit={handleSubmitCategory}
          category={editingCategory}
        />
      )}

      {categoryToDelete && (
        <DeleteConfirmationDialog
          isOpen={isDeleteDialogOpen}
          onClose={handleCloseDeleteDialog}
          onConfirm={handleDeleteCategory}
          title="Delete Category"
          description={`Are you sure you want to delete the category "${categoryToDelete.name}"? This action cannot be undone.`}
        />
      )}
    </>
  );
}