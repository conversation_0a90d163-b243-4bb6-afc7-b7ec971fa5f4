'use client';

export { MainEditor } from './editor';
export { MainEditorPanel } from './panel';
export { EnhancedMainEditor } from './enhanced-editor';
export { EnhancedMainEditorPanel } from './enhanced-panel';
export { addContentToMainEditor } from '@/providers/main-editor';

export type MainEditorProps = {
  className?: string;
};

export const MainEditor = ({ className }: MainEditorProps) => {
  const { content, setContent, clearContent } = useMainEditor();
  const addContentRef = useRef<((newContent: string) => void) | null>(null);

  const editor = useEditor({
    extensions: [
      StarterKit,
      Typography,
      Placeholder.configure({
        placeholder: 'Content from workflow nodes will appear here...',
      }),
      CharacterCount,
    ],
    content,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      setContent(html);
    },
  });

  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      editor.commands.setContent(content);
    }
  }, [content, editor]);

  // The function that will be called externally to add content
  const addContentToEditor = (newContent: string) => {
    if (!editor) return;

    // Insert content at cursor position or at the end
    editor.commands.focus('end');

    // Add a line break if there's already content
    if (editor.getHTML()) {
      editor.commands.insertContent('\n\n');
    }

    editor.commands.insertContent(newContent);
  };

  // Store the function reference for external access
  addContentRef.current = addContentToEditor;

  // Register the function on component mount
  useEffect(() => {
    globalAddContentToMainEditor = addContentToEditor;

    return () => {
      globalAddContentToMainEditor = null;
    };
  }, []);

  return (
    <div
      className={cn(
        'flex h-full w-full flex-col rounded-md border bg-background',
        className
      )}
    >
      <div className="flex items-center border-b p-2">
        <h2 className="font-medium text-lg">Main Editor</h2>
        <Separator orientation="vertical" className="mx-2 h-6" />
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor?.chain().focus().toggleBold().run()}
            className={editor?.isActive('bold') ? 'bg-accent' : ''}
          >
            <BoldIcon className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor?.chain().focus().toggleItalic().run()}
            className={editor?.isActive('italic') ? 'bg-accent' : ''}
          >
            <ItalicIcon className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() =>
              editor?.chain().focus().toggleHeading({ level: 1 }).run()
            }
            className={
              editor?.isActive('heading', { level: 1 }) ? 'bg-accent' : ''
            }
          >
            <Heading1Icon className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() =>
              editor?.chain().focus().toggleHeading({ level: 2 }).run()
            }
            className={
              editor?.isActive('heading', { level: 2 }) ? 'bg-accent' : ''
            }
          >
            <Heading2Icon className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() =>
              editor?.chain().focus().toggleHeading({ level: 3 }).run()
            }
            className={
              editor?.isActive('heading', { level: 3 }) ? 'bg-accent' : ''
            }
          >
            <Heading3Icon className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor?.chain().focus().toggleBulletList().run()}
            className={editor?.isActive('bulletList') ? 'bg-accent' : ''}
          >
            <ListIcon className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor?.chain().focus().toggleOrderedList().run()}
            className={editor?.isActive('orderedList') ? 'bg-accent' : ''}
          >
            <ListOrderedIcon className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor?.chain().focus().toggleBlockquote().run()}
            className={editor?.isActive('blockquote') ? 'bg-accent' : ''}
          >
            <TextQuoteIcon className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor?.chain().focus().toggleCodeBlock().run()}
            className={editor?.isActive('codeBlock') ? 'bg-accent' : ''}
          >
            <CodeIcon className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              editor?.commands.clearContent();
              clearContent();
            }}
          >
            <TrashIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>
      <EditorContent
        editor={editor}
        className="prose prose-sm dark:prose-invert max-w-none flex-grow overflow-y-auto p-4 focus:outline-none"
      />
    </div>
  );
};

// Export the method to add content from elsewhere in the app
export let globalAddContentToMainEditor: ((content: string) => void) | null =
  null;

export function registerMainEditorCallback(
  callback: (content: string) => void
) {
  globalAddContentToMainEditor = callback;
}

export function addContentToMainEditor(content: string) {
  if (globalAddContentToMainEditor) {
    globalAddContentToMainEditor(content);
  }
}
