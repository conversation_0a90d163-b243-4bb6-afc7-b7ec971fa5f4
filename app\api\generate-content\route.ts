import { getSubscribedUser } from '@/lib/auth';
import { env } from '@/lib/env';
import { parseError } from '@/lib/error/parse';
import {
  getMockImageResponse,
  getMockTextResponse,
  getMockCodeResponse,
  getMockSpeechResponse,
  getMockVideoResponse,
  shouldUseMockResponse,
} from '@/lib/mock-responses';
import { createRateLimiter, slidingWindow } from '@/lib/rate-limit';

export const maxDuration = 30;

const rateLimiter = createRateLimiter({
  limiter: slidingWindow(10, '1 m'),
  prefix: 'api-generate-content',
});

export const POST = async (req: Request) => {
  try {
    const user = await getSubscribedUser();

    if (!user) {
      return new Response('Authentication required', { status: 401 });
    }

    // Apply rate limiting
    const { success } = await rateLimiter.limit(user.id);

    if (!success) {
      return new Response('Too many requests', { status: 429 });
    }

    const { type, prompt, instructions, modelId, ...options } = await req.json();

    if (!type || !prompt) {
      return new Response('Type and prompt are required', { status: 400 });
    }

    // Check if we should use mock response due to invalid API keys
    const apiKey = env.OPENAI_API_KEY;
    if (shouldUseMockResponse(apiKey)) {
      let mockResponse;

      switch (type) {
        case 'text':
          mockResponse = getMockTextResponse(prompt);
          return new Response(mockResponse.text, {
            status: 200,
            headers: { 'Content-Type': 'text/plain' },
          });

        case 'image':
          mockResponse = getMockImageResponse();
          return new Response(JSON.stringify({
            success: true,
            data: {
              url: `data:${mockResponse.image.mimeType};base64,${mockResponse.image.base64}`,
              type: mockResponse.image.mimeType,
              description: 'Mock image generated (API key not available)',
            },
          }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' },
          });

        case 'video':
          mockResponse = getMockVideoResponse();
          return new Response(JSON.stringify({
            success: true,
            data: {
              url: mockResponse.videoUrl,
              type: 'video/mp4',
              description: 'Mock video generated (API key not available)',
            },
          }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' },
          });

        case 'speech':
          mockResponse = getMockSpeechResponse();
          return new Response(JSON.stringify({
            success: true,
            data: {
              url: 'data:audio/mp3;base64,' + Buffer.from(mockResponse.audio).toString('base64'),
              type: 'audio/mp3',
              description: 'Mock speech generated (API key not available)',
            },
          }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' },
          });

        case 'code':
          mockResponse = getMockCodeResponse(prompt, options.language || 'javascript');
          return new Response(JSON.stringify({
            text: mockResponse.text,
            language: mockResponse.language,
          }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' },
          });

        default:
          return new Response('Unsupported content type', { status: 400 });
      }
    }

    // If we have valid API keys, return an error indicating that real API integration is needed
    return new Response(JSON.stringify({
      error: 'Real API integration not implemented in this route. Use specific content generation actions.',
      suggestion: 'This route is designed for mock responses when API keys are invalid.',
    }), {
      status: 501,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    const message = parseError(error);
    return new Response(message, { status: 500 });
  }
};
