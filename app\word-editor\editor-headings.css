/* Enhanced heading styles for editor */

/* Base heading styles */
.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3 {
  position: relative;
  color: var(--heading-color, #1a1a1a);
  font-weight: 700;
  transition: background-color 0.15s ease;
  padding: 0.25rem 0.5rem;
  margin-left: -0.5rem;
  margin-right: -0.5rem;
  border-radius: 4px;
}

/* Dark mode heading colors */
.dark .ProseMirror h1 {
  --heading-color: #f8f9fa;
}

.dark .ProseMirror h2 {
  --heading-color: #f1f3f5;
}

.dark .ProseMirror h3 {
  --heading-color: #e9ecef;
}

/* Specific heading styles */
.ProseMirror h1 {
  font-size: 2.2rem;
  line-height: 1.3;
  margin: 1.5rem 0 1rem;
  letter-spacing: -0.03em;
  background-color: rgba(59, 130, 246, 0.03);
}

.ProseMirror h2 {
  font-size: 1.8rem;
  line-height: 1.4;
  font-weight: 600;
  margin: 1.4rem 0 0.9rem;
  letter-spacing: -0.02em;
  background-color: rgba(147, 51, 234, 0.02);
}

.ProseMirror h3 {
  font-size: 1.5rem;
  line-height: 1.45;
  font-weight: 600;
  margin: 1.3rem 0 0.8rem;
  letter-spacing: -0.015em;
  background-color: rgba(236, 72, 153, 0.02);
}

/* Heading hover states */
.ProseMirror h1:hover,
.ProseMirror h2:hover,
.ProseMirror h3:hover {
  background-color: rgba(59, 130, 246, 0.05);
}

/* Add left markers for headings */
.ProseMirror h1::before,
.ProseMirror h2::before,
.ProseMirror h3::before {
  content: "";
  position: absolute;
  left: -4px;
  top: 0;
  bottom: 0;
  width: 3px;
  border-radius: 3px;
  opacity: 0;
  transition: opacity 0.2s ease, transform 0.2s ease;
  transform: translateX(-4px);
}

.ProseMirror h1:hover::before,
.ProseMirror h2:hover::before,
.ProseMirror h3:hover::before {
  opacity: 1;
  transform: translateX(0);
}

/* Color variations for heading markers */
.ProseMirror h1::before {
  background-color: rgba(59, 130, 246, 0.8); /* blue */
}

.ProseMirror h2::before {
  background-color: rgba(147, 51, 234, 0.7); /* purple */
}

.ProseMirror h3::before {
  background-color: rgba(236, 72, 153, 0.7); /* pink */
}

/* First heading special styling */
.ProseMirror > h1:first-child {
  margin-top: 0.5rem;
  font-size: 2.4rem;
  letter-spacing: -0.04em;
  color: var(--heading-color, #111111);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding-bottom: 0.5rem;
  margin-bottom: 1.5rem;
}

.dark .ProseMirror > h1:first-child {
  color: #ffffff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
