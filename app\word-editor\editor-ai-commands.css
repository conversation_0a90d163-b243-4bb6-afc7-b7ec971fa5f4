/* AI Slash Commands and Prompts Styling with enhanced visual feedback */

.ai-slash-command {
  position: relative;
  display: inline-block;
  margin: 0 0.2em;
  padding: 0.1em 0.4em;
  border-radius: 0.25rem;
  background-color: hsl(var(--primary) / 0.1);
  color: hsl(var(--primary));
  font-family: var(--font-mono);
  font-size: 0.9em;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px hsl(var(--primary) / 0.1);
  animation: aiCommandPulse 2s ease-in-out infinite;
}

.ai-slash-command:hover {
  background-color: hsl(var(--primary) / 0.2);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px hsl(var(--primary) / 0.2);
}

@keyframes aiCommandPulse {
  0%,
  100% {
    background-color: hsl(var(--primary) / 0.1);
  }
  50% {
    background-color: hsl(var(--primary) / 0.15);
  }
}

/* Enhanced AI command button animation */
.editor-toolbar button[aria-label*="AI"] {
  position: relative;
  overflow: hidden;
  background: linear-gradient(
    90deg,
    hsl(var(--background)),
    hsl(var(--background)),
    hsl(var(--primary) / 0.2),
    hsl(var(--background))
  );
  background-size: 300% 100%;
  animation: aiButtonGlow 3s ease-in-out infinite;
}

@keyframes aiButtonGlow {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.ai-slash-command::before {
  content: "/";
  margin-right: 0.3em;
  opacity: 0.7;
}

.ai-prompt-container {
  position: relative;
  margin: 1.5rem 0;
  padding: 1rem;
  border-radius: 0.5rem;
  background-color: rgba(var(--primary-rgb), 0.05);
  border-left: 3px solid hsl(var(--primary));
}

.ai-response-container {
  position: relative;
  margin: 1.5rem 0;
  padding: 1.5rem;
  border-radius: 0.5rem;
  background-color: rgba(var(--primary-rgb), 0.08);
  border-left: 3px solid hsl(var(--primary));
}

.ai-response-container::before {
  content: "✨ AI Generated";
  position: absolute;
  top: -0.75rem;
  left: 1rem;
  padding: 0.2rem 0.5rem;
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 0.25rem;
}

.ai-loading {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  font-style: italic;
  color: hsl(var(--muted-foreground));
}

.ai-loading-dots {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.ai-loading-dots span {
  width: 0.5rem;
  height: 0.5rem;
  background-color: hsl(var(--primary));
  border-radius: 50%;
  animation: dot-flashing 1s infinite alternate;
}

.ai-loading-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.ai-loading-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes dot-flashing {
  0% {
    opacity: 0.2;
  }
  100% {
    opacity: 1;
  }
}

/* AI Command Suggestions */
.ai-command-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.ai-command-suggestion {
  padding: 0.3rem 0.7rem;
  border-radius: 1rem;
  background-color: hsl(var(--muted));
  color: hsl(var(--muted-foreground));
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.ai-command-suggestion:hover {
  background-color: hsl(var(--accent));
  border-color: hsl(var(--border));
}

.ai-command-suggestion .command-icon {
  display: inline-flex;
  margin-right: 0.3rem;
  opacity: 0.7;
}

/* Slash command menu enhancements */
.slash-command-group {
  padding: 0.25rem;
  margin-bottom: 0.25rem;
}

.slash-command-group-title {
  padding: 0.25rem 0.75rem;
  font-size: 0.7rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 600;
  color: hsl(var(--muted-foreground));
}

.slash-command-ai {
  background: linear-gradient(
    135deg,
    rgba(var(--primary-rgb), 0.1),
    rgba(var(--primary-rgb), 0.05)
  );
}

.slash-command-ai .command-item-icon {
  color: hsl(var(--primary));
}
