/* Enhanced Modern Styles for Tersa Word Editor - Premium Design */

/* Editor Content Area with sophisticated styling and improved typography */
.ProseMirror {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Inter", "Open Sans", "Helvetica Neue", sans-serif;
  font-size: 1.125rem;
  line-height: 1.75;
  padding: 3rem 3.5rem;
  min-height: 650px;
  color: hsl(var(--foreground) / 0.95);
  outline: none !important;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  background-color: hsl(var(--background));
  border-radius: inherit;
  position: relative;
  font-feature-settings: "liga" 1, "kern" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enhanced focus state with subtle glow */
.ProseMirror:focus {
  background-color: hsl(var(--background));
  box-shadow: inset 0 0 0 1px hsl(var(--primary) / 0.1);
}

/* Subtle paper texture effect */
.ProseMirror::before {
  content: "";
  position: absolute;
  inset: 0;
  background-image: radial-gradient(
    circle at 1px 1px,
    hsl(var(--muted) / 0.15) 1px,
    transparent 0
  );
  background-size: 20px 20px;
  pointer-events: none;
  opacity: 0.3;
  border-radius: inherit;
}

/* Enhanced typography with modern styling */
.ProseMirror h1 {
  font-size: 3rem;
  font-weight: 800;
  line-height: 1.2;
  margin-top: 2.5rem;
  margin-bottom: 1.75rem;
  color: hsl(var(--foreground));
  letter-spacing: -0.03em;
  position: relative;
  padding-bottom: 0.5rem;
  background: linear-gradient(
    135deg,
    hsl(var(--foreground)),
    hsl(var(--foreground) / 0.8)
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Enhanced underline effect for h1 */
.ProseMirror h1::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 4rem;
  height: 4px;
  background: linear-gradient(
    90deg,
    hsl(var(--primary)),
    hsl(var(--primary) / 0.6),
    transparent
  );
  border-radius: 2px;
  animation: underlineGlow 2s ease-in-out infinite alternate;
}

@keyframes underlineGlow {
  from {
    box-shadow: 0 0 5px hsl(var(--primary) / 0.3);
  }
  to {
    box-shadow: 0 0 15px hsl(var(--primary) / 0.6);
  }
}

.ProseMirror h2 {
  font-size: 2rem; /* Adjusted h2 size */
  font-weight: 600;
  line-height: 1.35;
  margin-top: 2.25rem; /* Consistent vertical rhythm */
  margin-bottom: 1.15rem;
  letter-spacing: -0.015em;
  color: hsl(var(--foreground) / 0.95);
}

.ProseMirror h3 {
  font-size: 1.5rem; /* Adjusted h3 size */
  font-weight: 600;
  line-height: 1.45;
  margin-top: 2rem;
  margin-bottom: 0.85rem;
  color: hsl(var(--foreground) / 0.9);
}

.ProseMirror p {
  margin-bottom: 1.35rem; /* Slightly more space after paragraphs */
  color: hsl(var(--foreground) / 0.85); /* Adjusted paragraph text color */
}

/* Enhanced paragraph spacing */
.ProseMirror p + p {
  margin-top: 0; /* Remove negative margin, rely on margin-bottom of previous p */
}

/* Lists with improved spacing and bullets */
.ProseMirror ul {
  list-style-type: none;
  padding-left: 2rem; /* Increased padding for visual separation */
  margin-bottom: 1.35rem;
  position: relative;
}

.ProseMirror ul li {
  margin-bottom: 0.6rem; /* Slightly more space between list items */
  position: relative;
  padding-left: 0.25rem; /* Space for the custom bullet */
}

.ProseMirror ul li::before {
  content: "";
  position: absolute;
  left: -1.5rem; /* Adjusted position based on ul padding-left */
  top: 0.65rem; /* Fine-tune vertical alignment */
  width: 7px; /* Slightly larger bullet */
  height: 7px;
  background-color: hsl(var(--primary) / 0.8); /* More visible bullet */
  border-radius: 50%;
}

.ProseMirror ol {
  list-style-type: decimal;
  padding-left: 2rem; /* Consistent with ul */
  margin-bottom: 1.35rem;
  counter-reset: item;
}

.ProseMirror ol li {
  margin-bottom: 0.6rem;
  position: relative;
}

/* Task lists with prettier checkboxes */
.ProseMirror ul[data-type="taskList"] {
  list-style: none;
  padding-left: 0.5rem; /* Adjusted padding for task lists */
}

.ProseMirror ul[data-type="taskList"] li {
  display: flex;
  align-items: center;
  margin-bottom: 0.85rem; /* More space for task items */
}

.ProseMirror ul[data-type="taskList"] li::before {
  display: none;
}

.ProseMirror ul[data-type="taskList"] input[type="checkbox"] {
  margin-right: 0.85rem;
  cursor: pointer;
  width: 1.35rem; /* Slightly larger checkbox */
  height: 1.35rem;
  border-radius: 5px; /* Softer corners */
  border: 2px solid hsl(var(--border) / 0.8); /* More visible border */
  position: relative;
  transition: all 0.2s ease;
  appearance: none;
  background-color: hsl(var(--background)); /* Ensure background for unchecked state */
}

.ProseMirror ul[data-type="taskList"] input[type="checkbox"]:hover {
  border-color: hsl(var(--primary) / 0.7);
}

.ProseMirror ul[data-type="taskList"] input[type="checkbox"]:checked {
  background-color: hsl(var(--primary));
  border-color: hsl(var(--primary));
}

.ProseMirror ul[data-type="taskList"] input[type="checkbox"]:checked::after {
  content: "";
  position: absolute;
  left: 6px; /* Adjusted checkmark position */
  top: 3px; /* Adjusted checkmark position */
  width: 5px;
  height: 10px;
  border: solid hsl(var(--primary-foreground)); /* Use foreground color for checkmark */
  border-width: 0 2.5px 2.5px 0; /* Slightly thicker checkmark */
  transform: rotate(45deg);
}

/* Stylish blockquotes */
.ProseMirror blockquote {
  padding: 1.25rem 1.75rem 1.25rem 1.5rem; /* Adjusted padding */
  border-left: 5px solid hsl(var(--primary) / 0.6); /* Thicker, more prominent border */
  margin: 1.75rem 0;
  background-color: hsl(var(--muted) / 0.25); /* Slightly more pronounced background */
  border-radius: 0 6px 6px 0; /* Softer radius */
  font-style: italic;
  color: hsl(var(--muted-foreground) / 0.9);
  position: relative;
}

.ProseMirror blockquote::before {
  content: "\201C"; /* Using unicode for opening quote for better rendering */
  position: absolute;
  top: 0.1em; /* Adjusted position */
  left: 0.6rem;
  font-size: 3rem; /* Larger quote character */
  font-family: Georgia, serif;
  color: hsl(var(--primary) / 0.35);
  line-height: 1;
  z-index: 0;
}

.ProseMirror blockquote p {
  margin-bottom: 0.6rem;
  position: relative; /* To ensure text is above the pseudo-element */
  z-index: 1;
}

/* Better code styling */
.ProseMirror code {
  background-color: hsl(var(--muted) / 0.4); /* Slightly more distinct background */
  border-radius: 5px;
  padding: 0.25rem 0.45rem;
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 0.92em; /* Slightly adjusted font size */
  color: hsl(var(--accent-foreground) / 0.9); /* Ensure good contrast */
  border: 1px solid hsl(var(--border) / 0.6);
}

.ProseMirror pre {
  background-color: hsl(var(--muted) / 0.6); /* Darker background for pre */
  border-radius: 8px; /* More rounded corners */
  padding: 1.25rem; /* More padding */
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 0.92em;
  overflow-x: auto;
  margin: 1.75rem 0;
  border: 1px solid hsl(var(--border) / 0.8);
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); /* Subtle shadow for depth */
}

.ProseMirror pre::before {
  content: attr(data-language);
  position: absolute;
  top: 0.6rem; /* Adjusted position */
  right: 0.85rem;
  font-size: 0.8rem; /* Slightly larger language indicator */
  color: hsl(var(--muted-foreground) / 0.8);
  text-transform: uppercase;
  font-weight: 500;
}

/* Tables with modern styling */
.ProseMirror table {
  width: 100%;
  border-collapse: separate; /* Use separate for border-spacing and radius on cells */
  border-spacing: 0;
  margin: 1.75rem 0;
  border-radius: 8px; /* More rounded table */
  overflow: hidden; /* Important for border-radius on table */
  border: 1px solid hsl(var(--border) / 0.9);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); /* Subtle shadow */
}

.ProseMirror table th {
  background-color: hsl(var(--muted) / 0.6);
  color: hsl(var(--foreground));
  font-weight: 600;
  text-align: left;
  padding: 0.85rem 1.1rem; /* Adjusted padding */
  border-bottom: 2px solid hsl(var(--border));
}

.ProseMirror table td {
  padding: 0.85rem 1.1rem;
  border-bottom: 1px solid hsl(var(--border) / 0.7);
  border-right: 1px solid hsl(var(--border) / 0.5); /* Add right border for cell separation */
}

.ProseMirror table th:last-child,
.ProseMirror table td:last-child {
  border-right: none; /* Remove right border for last cell in a row */
}

/* Horizontal rule */
.ProseMirror hr {
  margin: 2.5rem 0; /* More vertical space */
  height: 2.5px; /* Slightly thicker */
  background: linear-gradient(
    to right,
    hsl(var(--muted) / 0.2) /* Softer gradient start/end */,
    hsl(var(--muted) / 0.8) /* More prominent center */,
    hsl(var(--muted) / 0.2)
  );
  border: none;
  border-radius: 1px;
}

/* Links */
.ProseMirror a {
  color: hsl(var(--primary));
  text-decoration: none;
  border-bottom: 1.5px solid hsl(var(--primary) / 0.6); /* Slightly thicker, more solid underline */
  transition: all 0.2s ease;
  font-weight: 500; /* Slightly bolder links */
}

.ProseMirror a:hover {
  color: hsl(var(--primary) / 0.85);
  border-bottom-color: hsl(var(--primary) / 0.9);
  background-color: hsl(var(--primary) / 0.05); /* Subtle hover background */
  border-radius: 2px;
}

/* Images */
.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 8px; /* More rounded images */
  margin: 1.75rem 0;
  display: block;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08); /* Softer, more diffused shadow */
  border: 1px solid hsl(var(--border) / 0.5); /* Subtle border for images */
}

/* Editor card styling (ensure this complements .ProseMirror) */
.editor-card {
  border-radius: 12px; /* Ensure this matches or is larger than .ProseMirror if nested */
  overflow: hidden; /* Keep this if .ProseMirror is directly inside */
  box-shadow: 0 12px 45px rgba(0, 0, 0, 0.08); /* Enhanced shadow for the card */
  /* background-color: hsl(var(--card)); /* If editor-card is the direct parent and has the dots */
}

/* Placeholder styling */
.ProseMirror .is-empty::before {
  content: attr(data-placeholder);
  float: left;
  color: hsl(var(--muted-foreground) / 0.7);
  pointer-events: none;
  height: 0;
  font-style: italic;
}

/* Selection styling - ensure it's visible and pleasant */
.ProseMirror ::selection {
  background: hsl(var(--primary) / 0.3);
  color: hsl(var(--primary-foreground));
}
.ProseMirror.ProseMirror-focused ::selection {
  background: hsl(var(--primary) / 0.35);
}

/* Caret color */
.ProseMirror:focus {
  caret-color: hsl(var(--primary));
}

/* Additional style for the overall editor container if needed, e.g., .editor-paper */
/* .editor-paper { */
/* Styles for the dotted background or overall container can go here or in a more global CSS file */
/* For example: */
/* background-image: radial-gradient(hsl(var(--border) / 0.2) 1px, transparent 1px); */
/* background-size: 16px 16px; */
/* } */
