import { getSubscribedUser } from '@/lib/auth';
import { parseError } from '@/lib/error/parse';
import { textModels } from '@/lib/models/text';
import { createRateLimiter, slidingWindow } from '@/lib/rate-limit';
import { trackCreditUsage } from '@/lib/stripe';
import { streamText } from 'ai';

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

// Create a rate limiter for the chat API
const rateLimiter = createRateLimiter({
  limiter: slidingWindow(10, '1 m'),
  prefix: 'api-chat',
});

export const POST = async (req: Request) => {
  try {
    await getSubscribedUser();
  } catch (error) {
    const message = parseError(error);

    return new Response(message, { status: 401 });
  }

  // Apply rate limiting
  if (process.env.NODE_ENV === 'production') {
    const ip = req.headers.get('x-forwarded-for') || 'anonymous';
    const { success, limit, reset, remaining } = await rateLimiter.limit(ip);

    if (!success) {
      return new Response('Too many requests', {
        status: 429,
        headers: {
          'X-RateLimit-Limit': limit.toString(),
          'X-RateLimit-Remaining': remaining.toString(),
          'X-RateLimit-Reset': reset.toString(),
        },
      });
    }
  }

  const { messages, modelId } = await req.json();

  if (typeof modelId !== 'string') {
    return new Response('Model must be a string', { status: 400 });
  }

  const model = textModels
    .flatMap((m) => m.models)
    .find((m) => m.id === modelId);

  if (!model) {
    return new Response('Invalid model', { status: 400 });
  }

  console.log('DEBUG: Making real API call with model:', modelId);

  try {
    console.log('DEBUG: About to call streamText with model:', model.model);
    console.log('DEBUG: Messages:', JSON.stringify(messages, null, 2));

    const result = streamText({
      model: model.model,
      system: [
        "You are a helpful assistant that synthesizes content based on the user's prompts.",
        'The user will provide instructions; and may provide text, audio transcriptions, or images (and their descriptions) as context.',
        "You will then synthesize the content based on the user's instructions and the context provided.",
        'The output should be a concise summary of the content, no more than 100 words.',
      ].join('\n'),
      messages,
      onFinish: async ({ usage }) => {
        console.log('DEBUG: API call finished successfully, usage:', usage);
        await trackCreditUsage({
          action: 'chat',
          cost: model.getCost({
            input: usage.promptTokens,
            output: usage.completionTokens,
          }),
        });
      },
    });

    console.log('DEBUG: streamText call successful, returning response');
    return result.toDataStreamResponse();
  } catch (error) {
    console.log('DEBUG: Error in streamText:', error);
    console.log('DEBUG: Error details:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : 'An error occurred',
      stack: error instanceof Error ? error.stack : 'No stack trace',
    });

    return new Response(
      `Error: ${error instanceof Error ? error.message : 'An error occurred'}`,
      { status: 500 }
    );
  }
};
