import { getSubscribedUser } from '@/lib/auth';
import { parseError } from '@/lib/error/parse';
import { textModels } from '@/lib/models/text';
import { createRateLimiter, slidingWindow } from '@/lib/rate-limit';
import { trackCreditUsage } from '@/lib/stripe';
import { streamText } from 'ai';

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

// Create a rate limiter for the chat API
const rateLimiter = createRateLimiter({
  limiter: slidingWindow(10, '1 m'),
  prefix: 'api-chat',
});

export const POST = async (req: Request) => {
  console.log('DEBUG: Chat API called');
  try {
    const user = await getSubscribedUser();
    console.log('DEBUG: User authenticated:', user?.id);
  } catch (error) {
    console.log('DEBUG: Authentication failed:', error);
    const message = parseError(error);

    return new Response(message, { status: 401 });
  }

  // Apply rate limiting
  if (process.env.NODE_ENV === 'production') {
    const ip = req.headers.get('x-forwarded-for') || 'anonymous';
    const { success, limit, reset, remaining } = await rateLimiter.limit(ip);

    if (!success) {
      return new Response('Too many requests', {
        status: 429,
        headers: {
          'X-RateLimit-Limit': limit.toString(),
          'X-RateLimit-Remaining': remaining.toString(),
          'X-RateLimit-Reset': reset.toString(),
        },
      });
    }
  }

  const { messages, modelId } = await req.json();

  if (typeof modelId !== 'string') {
    return new Response('Model must be a string', { status: 400 });
  }

  const model = textModels
    .flatMap((m) => m.models)
    .find((m) => m.id === modelId);

  if (!model) {
    return new Response('Invalid model', { status: 400 });
  }

  // Check if we should use mock response due to invalid API keys
  const { env } = await import('@/lib/env');
  const { getMockTextResponse, shouldUseMockResponse } = await import(
    '@/lib/mock-responses'
  );

  const apiKey = env.OPENAI_API_KEY;
  console.log('DEBUG: API Key check:', apiKey?.substring(0, 10) + '...');
  console.log('DEBUG: Should use mock:', shouldUseMockResponse(apiKey));

  if (shouldUseMockResponse(apiKey)) {
    console.log('DEBUG: Using mock response');
    // Extract the user's prompt from messages
    const lastMessage = messages.at(-1);
    const prompt = lastMessage?.content || 'No prompt provided';
    const mockResponse = getMockTextResponse(prompt);
    console.log(
      'DEBUG: Mock response generated:',
      mockResponse.text.substring(0, 50) + '...'
    );

    // Return response in AI SDK's expected streaming format
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      start(controller) {
        // Send the text content
        const textChunk = `0:"${mockResponse.text.replace(/"/g, '\\"').replace(/\n/g, '\\n')}"\n`;
        controller.enqueue(encoder.encode(textChunk));

        // Send finish signal
        const finishChunk = `d:{"finishReason":"stop","usage":{"promptTokens":${mockResponse.usage.promptTokens},"completionTokens":${mockResponse.usage.completionTokens},"totalTokens":${mockResponse.usage.totalTokens}}}\n`;
        controller.enqueue(encoder.encode(finishChunk));

        controller.close();
      },
    });

    return new Response(stream, {
      status: 200,
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
      },
    });
  }

  try {
    const result = streamText({
      model: model.model,
      system: [
        "You are a helpful assistant that synthesizes content based on the user's prompts.",
        'The user will provide instructions; and may provide text, audio transcriptions, or images (and their descriptions) as context.',
        "You will then synthesize the content based on the user's instructions and the context provided.",
        'The output should be a concise summary of the content, no more than 100 words.',
      ].join('\n'),
      messages,
      onFinish: async ({ usage }) => {
        await trackCreditUsage({
          action: 'chat',
          cost: model.getCost({
            input: usage.promptTokens,
            output: usage.completionTokens,
          }),
        });
      },
    });

    return result.toDataStreamResponse();
  } catch (error) {
    return new Response(
      `Error: ${error instanceof Error ? error.message : 'An error occurred'}`,
      { status: 500 }
    );
  }
};
