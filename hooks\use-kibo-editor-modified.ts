import BulletList from '@tiptap/extension-bullet-list';
// This is a modification of the use-kibo-editor.ts file
// Add BulletList import at the top of the file
import { Document, Packer, Paragraph, TextRun } from 'docx';
import { saveAs } from 'file-saver';
import html2pdf from 'html2pdf.js';
import { useCallback, useState } from 'react';

export type KiboEditorDocument = {
  id?: string;
  title: string;
  content: string;
  createdAt?: Date;
  updatedAt?: Date;
};

export const useKiboEditor = (initialDocument?: KiboEditorDocument) => {
  const [document, setDocument] = useState<KiboEditorDocument>(
    initialDocument || {
      title: 'Untitled Document',
      content: '<h1>Untitled Document</h1><p>Start writing here...</p>',
      createdAt: new Date(),
      updatedAt: new Date(),
    }
  );

  const [isFullscreen, setIsFullscreen] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const [charCount, setCharCount] = useState(0);
  const [editor, setEditor] = useState(null);

  // Add initializing function to set editor instance
  const setEditorInstance = useCallback((editorInstance) => {
    setEditor(editorInstance);
  }, []);

  const handleEditorUpdate = useCallback(
    ({ editor }: { editor: any }) => {
      const html = editor.getHTML();
      const text = editor.getText();

      // Set the editor instance for later use
      setEditor(editor);

      // Update counts
      setCharCount(editor.storage.characterCount.characters());
      setWordCount(text ? text.trim().split(/\s+/).length : 0);

      // Extract title from first heading if it exists
      const firstHeading = editor
        .getJSON()
        .content?.find(
          (node) => node.type === 'heading' && node.attrs?.level === 1
        );

      let title = document.title;
      if (
        firstHeading &&
        firstHeading.content &&
        firstHeading.content.length > 0
      ) {
        const extractedTitle = firstHeading.content
          .map((c: any) => c.text)
          .join('');
        if (extractedTitle) title = extractedTitle;
      }

      setDocument((prev) => ({
        ...prev,
        title,
        content: html,
        updatedAt: new Date(),
      }));
    },
    [document.title]
  );

  const toggleFullscreen = useCallback(() => {
    setIsFullscreen((prev) => !prev);

    if (isFullscreen) {
      document.body.classList.remove('overflow-hidden');
    } else {
      document.body.classList.add('overflow-hidden');
    }
  }, [isFullscreen]);

  const exportToPdf = useCallback(async () => {
    const element = document.createElement('div');
    element.innerHTML = document.content;
    element.classList.add('kibo-pdf-export');
    document.body.appendChild(element);

    const opt = {
      margin: 1,
      filename: `${document.title.replace(/[^a-z0-9]/gi, '-').toLowerCase()}.pdf`,
      image: { type: 'jpeg', quality: 0.98 },
      html2canvas: { scale: 2 },
      jsPDF: { unit: 'in', format: 'letter', orientation: 'portrait' },
    };

    try {
      await html2pdf().set(opt).from(element).save();
    } finally {
      document.body.removeChild(element);
    }
  }, [document]);

  const exportToDocx = useCallback(async () => {
    // Create document with content - this is a simplified version
    // In a real implementation, you'd want to parse the HTML more carefully
    const doc = new Document({
      sections: [
        {
          properties: {},
          children: [
            new Paragraph({
              children: [new TextRun(document.title)],
            }),
            new Paragraph({
              children: [
                new TextRun(document.content.replace(/<[^>]+>/g, ' ')),
              ],
            }),
          ],
        },
      ],
    });

    // Generate and download
    const blob = await Packer.toBlob(doc);
    saveAs(
      blob,
      `${document.title.replace(/[^a-z0-9]/gi, '-').toLowerCase()}.docx`
    );
  }, [document]);

  // Extensions array to use for the editor
  const extensions = [
    // Default extensions
    // Add BulletList to the extensions list
    BulletList,
  ];

  return {
    document,
    setDocument,
    isFullscreen,
    toggleFullscreen,
    wordCount,
    charCount,
    handleEditorUpdate,
    exportToPdf,
    exportToDocx,
    editor,
    setEditorInstance,
    extensions,
  };
};
