'use client';

import { Extension } from '@tiptap/core';
import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state';

/**
 * BetterTextSelection extension for TipTap editor
 * Enhances text selection appearance and behavior, especially for headings
 */
export const BetterTextSelection = Extension.create({
  name: 'betterTextSelection',

  // Add custom attributes to mark selection state
  addGlobalAttributes() {
    return [
      {
        types: ['heading'],
        attributes: {
          'data-selection-active': {
            default: 'false',
            parseHTML: (element) =>
              element.getAttribute('data-selection-active') || 'false',
            renderHTML: (attributes) => {
              return attributes['data-selection-active'] !== 'false'
                ? {
                    'data-selection-active':
                      attributes['data-selection-active'],
                  }
                : {};
            },
          },
        },
      },
    ];
  },

  addKeyboardShortcuts() {
    return {
      'Shift-ArrowRight': ({ editor }) => {
        this.trackSelectionState(editor);
        return true; // Let default behavior work
      },
      'Shift-ArrowLeft': ({ editor }) => {
        this.trackSelectionState(editor);
        return true;
      },
      'Shift-ArrowUp': ({ editor }) => {
        this.trackSelectionState(editor);
        return true;
      },
      'Shift-ArrowDown': ({ editor }) => {
        this.trackSelectionState(editor);
        return true;
      },
      'Mod-a': ({ editor }) => {
        this.addSelectionClass(editor.view.dom);
        return true;
      },
    };
  },

  // Helper method to track selection state
  trackSelectionState(editor) {
    if (!editor || !editor.view || !editor.view.dom) return;

    const editorElement = editor.view.dom;
    const selection = window.getSelection();

    if (!selection || selection.rangeCount === 0) {
      // No selection, remove selection classes
      this.removeSelectionClasses(editorElement);
      return;
    }

    const range = selection.getRangeAt(0);

    if (range.collapsed) {
      // Cursor position only, not a selection
      this.removeSelectionClasses(editorElement);
      return;
    }

    // Add selection class to the editor to enable special styling
    this.addSelectionClass(editorElement);

    // Find and mark selected headings
    this.markSelectedHeadings(selection, editor);
  },

  addSelectionClass(editorElement) {
    if (!editorElement) return;
    editorElement.classList.add('has-text-selection');
  },

  removeSelectionClasses(editorElement) {
    if (!editorElement) return;
    editorElement.classList.remove('has-text-selection');

    // Remove data-selection-active attributes
    const headings = editorElement.querySelectorAll('h1, h2, h3, h4, h5, h6');
    headings.forEach((heading) => {
      heading.setAttribute('data-selection-active', 'false');
    });
  },
  markSelectedHeadings(selection, editor) {
    if (!selection || selection.rangeCount === 0 || !editor) return;

    const range = selection.getRangeAt(0);

    // Check if selection includes any heading
    const headings = editor.view.dom.querySelectorAll('h1, h2, h3, h4, h5, h6');

    headings.forEach((heading) => {
      try {
        // Check if this heading is selected
        const headingRange = document.createRange();
        headingRange.selectNode(heading);

        // Mark the heading as selected if it intersects with the selection
        if (this.rangesIntersect(range, headingRange)) {
          heading.setAttribute('data-selection-active', 'true');
          // Add a subtle animation effect
          if (!heading.classList.contains('heading-selected-animation')) {
            heading.classList.add('heading-selected-animation');
            // Remove the class after animation completes
            setTimeout(() => {
              heading.classList.remove('heading-selected-animation');
            }, 500);
          }
        } else {
          heading.setAttribute('data-selection-active', 'false');
        }
      } catch (e) {
        // Safely handle any DOM issues without logging
      }
    });
  },
  rangesIntersect(range1: Range, range2: Range) {
    return !(
      range1.compareBoundaryPoints(Range.END_TO_START, range2) > 0 ||
      range1.compareBoundaryPoints(Range.START_TO_END, range2) < 0
    );
  },

  addProseMirrorPlugins() {
    const extensionThis = this;

    return [
      new Plugin({
        key: new PluginKey('betterTextSelection'),
        props: {
          handleDOMEvents: {
            mouseup(view) {
              extensionThis.trackSelectionState(view.editor);
              return false;
            },
            keyup(view, event) {
              // Track selection on key navigation
              if (
                ['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(
                  event.key
                )
              ) {
                extensionThis.trackSelectionState(view.editor);
              }
              return false;
            },
            selectionchange(view) {
              extensionThis.trackSelectionState(view.editor);
              return false;
            },
          },
        },
      }),
    ];
  },
});
