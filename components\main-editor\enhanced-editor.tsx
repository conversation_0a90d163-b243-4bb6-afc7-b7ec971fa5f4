'use client';

import CharacterCount from '@tiptap/extension-character-count';
import { Color } from '@tiptap/extension-color';
import Placeholder from '@tiptap/extension-placeholder';
import TextStyle from '@tiptap/extension-text-style';
import Typography from '@tiptap/extension-typography';
import { EditorContent, Extension, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';

// Custom font family extension as a workaround
const FontFamily = Extension.create({
  name: 'fontFamily',
  addGlobalAttributes() {
    return [
      {
        types: ['textStyle'],
        attributes: {
          fontFamily: {
            default: null,
            parseHTML: (element) =>
              element.style.fontFamily?.replace(/['"]/g, ''),
            renderHTML: (attributes) => {
              if (!attributes.fontFamily) return {};
              return {
                style: `font-family: ${attributes.fontFamily}`,
              };
            },
          },
        },
      },
    ];
  },
  addCommands() {
    return {
      setFontFamily:
        (fontFamily) =>
        ({ chain }) => {
          return chain().setMark('textStyle', { fontFamily }).run();
        },
      unsetFontFamily:
        () =>
        ({ chain }) => {
          return chain().setMark('textStyle', { fontFamily: null }).run();
        },
    };
  },
});
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@/components/ui/resizable';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { useMainEditor } from '@/providers/main-editor';
import {
  BoldIcon,
  CodeIcon,
  DownloadIcon,
  FileIcon,
  FileTextIcon,
  Heading1Icon,
  Heading2Icon,
  Heading3Icon,
  ItalicIcon,
  ListIcon,
  ListOrderedIcon,
  MaximizeIcon,
  MinimizeIcon,
  PaintBucketIcon,
  TextQuoteIcon,
  TrashIcon,
} from 'lucide-react';
import { useEffect, useState } from 'react';

export type MainEditorProps = {
  className?: string;
};

// Font options
const fontOptions = [
  { value: 'Inter', label: 'Inter' },
  { value: 'Arial', label: 'Arial' },
  { value: 'Comic Sans MS', label: 'Comic Sans' },
  { value: 'Georgia', label: 'Georgia' },
  { value: 'monospace', label: 'Monospace' },
  { value: 'Times New Roman', label: 'Times New Roman' },
  { value: 'serif', label: 'Serif' },
  { value: 'sans-serif', label: 'Sans Serif' },
];

// Color options
const colorOptions = [
  { value: '#000000', label: 'Black' },
  { value: '#1a73e8', label: 'Blue' },
  { value: '#ff0000', label: 'Red' },
  { value: '#00ff00', label: 'Green' },
  { value: '#ff9900', label: 'Orange' },
  { value: '#9900ff', label: 'Purple' },
];

export const EnhancedMainEditor = ({ className }: MainEditorProps) => {
  const { content, setContent, clearContent } = useMainEditor();
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [wordCount, setWordCount] = useState(0);

  const editor = useEditor({
    extensions: [
      StarterKit,
      Typography,
      TextStyle,
      FontFamily,
      Color,
      Placeholder.configure({
        placeholder: 'Content from workflow nodes will appear here...',
      }),
      CharacterCount.configure({
        limit: Number.POSITIVE_INFINITY,
      }),
    ],
    content,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      setContent(html);
      // Count words
      const text = editor.getText();
      setWordCount(text ? text.trim().split(/\s+/).length : 0);
    },
  });

  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      editor.commands.setContent(content);
    }
  }, [content, editor]);

  // Function to export content as DOCX
  const exportToDocx = async () => {
    if (!editor) return;

    try {
      // Dynamically import the necessary libraries
      const { Document, Packer, Paragraph, TextRun } = await import('docx');

      // Create document with content
      const doc = new Document({
        sections: [
          {
            properties: {},
            children: [
              new Paragraph({
                children: [new TextRun(editor.getText())],
              }),
            ],
          },
        ],
      });

      // Generate and download
      const blob = await Packer.toBlob(doc);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'editor-content.docx';
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting to DOCX:', error);
    }
  };

  // Function to export content as PDF
  const exportToPdf = async () => {
    if (!editor) return;

    try {
      // Dynamically import html2pdf
      const html2pdf = (await import('html2pdf.js')).default;

      // Create a clean div with the editor content
      const contentDiv = document.createElement('div');
      contentDiv.innerHTML = editor.getHTML();
      contentDiv.style.padding = '20px';
      contentDiv.style.fontFamily = 'Arial, sans-serif';

      // Generate PDF
      const opt = {
        margin: 1,
        filename: 'editor-content.pdf',
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { scale: 2 },
        jsPDF: { unit: 'in', format: 'letter', orientation: 'portrait' },
      };

      html2pdf().from(contentDiv).set(opt).save();
    } catch (error) {
      console.error('Error exporting to PDF:', error);
    }
  };

  // Toggle fullscreen mode
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);

    if (isFullscreen) {
      document.body.classList.remove('overflow-hidden');
    } else {
      document.body.classList.add('overflow-hidden');
    }
  };

  return (
    <div
      className={cn(
        'flex h-full w-full flex-col rounded-md border bg-background',
        isFullscreen ? 'fixed inset-0 z-50' : '',
        className
      )}
    >
      <div className="flex flex-wrap items-center border-b p-3">
        <h2 className="mr-3 font-medium text-xl">Main Editor</h2>
        <Separator className="mx-2 h-6" orientation="vertical" />

        {/* Font family selector */}
        <Select
          onValueChange={(value) =>
            editor?.chain().focus().setFontFamily(value).run()
          }
        >
          <SelectTrigger className="mr-2 w-[150px]">
            <SelectValue placeholder="Font family" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              {fontOptions.map((font) => (
                <SelectItem key={font.value} value={font.value}>
                  <span style={{ fontFamily: font.value }}>{font.label}</span>
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>

        {/* Text color dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="mr-2">
              <PaintBucketIcon className="mr-1 h-5 w-5" />
              Color
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {colorOptions.map((color) => (
              <DropdownMenuItem
                key={color.value}
                onClick={() =>
                  editor?.chain().focus().setColor(color.value).run()
                }
                className="flex items-center"
              >
                <div
                  className="mr-2 h-4 w-4 rounded-full border"
                  style={{ backgroundColor: color.value }}
                />
                {color.label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        <div className="ml-1 flex flex-wrap items-center gap-1">
          <Button
            className={cn('p-2.5', editor?.isActive('bold') ? 'bg-accent' : '')}
            onClick={() => editor?.chain().focus().toggleBold().run()}
            size="sm"
            variant="ghost"
            title="Bold"
          >
            <BoldIcon className="h-5 w-5" />
          </Button>
          <Button
            className={cn(
              'p-2.5',
              editor?.isActive('italic') ? 'bg-accent' : ''
            )}
            onClick={() => editor?.chain().focus().toggleItalic().run()}
            size="sm"
            variant="ghost"
            title="Italic"
          >
            <ItalicIcon className="h-5 w-5" />
          </Button>
          <Button
            className={cn(
              'p-2.5',
              editor?.isActive('heading', { level: 1 }) ? 'bg-accent' : ''
            )}
            onClick={() =>
              editor?.chain().focus().toggleHeading({ level: 1 }).run()
            }
            size="sm"
            variant="ghost"
            title="Heading 1"
          >
            <Heading1Icon className="h-5 w-5" />
          </Button>
          <Button
            className={cn(
              'p-2.5',
              editor?.isActive('heading', { level: 2 }) ? 'bg-accent' : ''
            )}
            onClick={() =>
              editor?.chain().focus().toggleHeading({ level: 2 }).run()
            }
            size="sm"
            variant="ghost"
            title="Heading 2"
          >
            <Heading2Icon className="h-5 w-5" />
          </Button>
          <Button
            className={cn(
              'p-2.5',
              editor?.isActive('heading', { level: 3 }) ? 'bg-accent' : ''
            )}
            onClick={() =>
              editor?.chain().focus().toggleHeading({ level: 3 }).run()
            }
            size="sm"
            variant="ghost"
            title="Heading 3"
          >
            <Heading3Icon className="h-5 w-5" />
          </Button>
          <Button
            className={cn(
              'p-2.5',
              editor?.isActive('bulletList') ? 'bg-accent' : ''
            )}
            onClick={() => editor?.chain().focus().toggleBulletList().run()}
            size="sm"
            variant="ghost"
            title="Bullet List"
          >
            <ListIcon className="h-5 w-5" />
          </Button>
          <Button
            className={cn(
              'p-2.5',
              editor?.isActive('orderedList') ? 'bg-accent' : ''
            )}
            onClick={() => editor?.chain().focus().toggleOrderedList().run()}
            size="sm"
            variant="ghost"
            title="Numbered List"
          >
            <ListOrderedIcon className="h-5 w-5" />
          </Button>
          <Button
            className={cn(
              'p-2.5',
              editor?.isActive('blockquote') ? 'bg-accent' : ''
            )}
            onClick={() => editor?.chain().focus().toggleBlockquote().run()}
            size="sm"
            variant="ghost"
            title="Quote"
          >
            <TextQuoteIcon className="h-5 w-5" />
          </Button>
          <Button
            className={cn(
              'p-2.5',
              editor?.isActive('codeBlock') ? 'bg-accent' : ''
            )}
            onClick={() => editor?.chain().focus().toggleCodeBlock().run()}
            size="sm"
            variant="ghost"
            title="Code Block"
          >
            <CodeIcon className="h-5 w-5" />
          </Button>
        </div>

        <div className="ml-auto flex items-center gap-1">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="gap-1">
                <DownloadIcon className="h-4 w-4" />
                Export
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem
                onClick={exportToDocx}
                className="flex items-center gap-2"
              >
                <FileTextIcon className="h-4 w-4" />
                Export as DOCX
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={exportToPdf}
                className="flex items-center gap-2"
              >
                <FileIcon className="h-4 w-4" />
                Export as PDF
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button
            onClick={toggleFullscreen}
            size="sm"
            variant="ghost"
            title={isFullscreen ? 'Exit fullscreen' : 'Fullscreen'}
          >
            {isFullscreen ? (
              <MinimizeIcon className="h-5 w-5" />
            ) : (
              <MaximizeIcon className="h-5 w-5" />
            )}
          </Button>
          <Button
            onClick={() => {
              editor?.commands.clearContent();
              clearContent();
            }}
            size="sm"
            variant="ghost"
            title="Clear editor"
          >
            <TrashIcon className="h-5 w-5" />
          </Button>
        </div>
      </div>

      <ResizablePanelGroup direction="vertical" className="flex-grow">
        <ResizablePanel defaultSize={85} minSize={30}>
          <EditorContent
            className="prose prose-sm dark:prose-invert h-full max-w-none overflow-y-auto p-6 focus:outline-none"
            style={{
              minHeight: '200px',
              fontSize: '1.1rem',
              lineHeight: '1.6',
            }}
            editor={editor}
          />
        </ResizablePanel>
        <ResizableHandle />
        <ResizablePanel defaultSize={15} minSize={10}>
          <div className="bg-muted/30 p-3 text-sm">
            <div className="flex items-center justify-between">
              <div>Word count: {wordCount}</div>
              <div>
                Characters: {editor?.storage.characterCount.characters() || 0}
              </div>
            </div>
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
};
