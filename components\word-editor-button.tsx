'use client';

import { Button } from '@/components/ui/button';
import { FileText } from 'lucide-react';
import { useRouter } from 'next/navigation';

export const WordEditorButton = () => {
  const router = useRouter();

  return (
    <Button
      variant="outline"
      size="sm"
      className="flex items-center gap-2"
      onClick={() => router.push('/word-editor')}
    >
      <FileText className="h-4 w-4" />
      <span>Word Editor</span>
    </Button>
  );
};
