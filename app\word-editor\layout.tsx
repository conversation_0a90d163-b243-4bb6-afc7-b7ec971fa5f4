'use client';

import { EditorHeader } from '@/components/word-editor/header';
import { cn } from '@/lib/utils';
import { KiboEditorProvider } from '@/providers/kibo-editor';
import { ThemeProvider } from '@/providers/theme';
import type React from 'react';

export default function WordEditorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <KiboEditorProvider>
        {/* CSS Variable for header height control */}
        <style jsx global>{`
          :root {
            --header-height: 60px;
          }
        `}</style>
        <div className={cn('relative flex min-h-screen flex-col')}>
          {/* Fixed header with navigation */}
          <EditorHeader editor={null} />

          {/* Main content with editor-main-container class to prevent overlap */}
          <main className="editor-main-container flex-1">{children}</main>
        </div>
      </KiboEditorProvider>
    </ThemeProvider>
  );
}
