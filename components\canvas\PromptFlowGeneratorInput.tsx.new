'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';
import { Loader2, <PERSON>rk<PERSON> } from 'lucide-react';
import { toast } from 'sonner';
import { useReactFlow } from '@xyflow/react';
import { useParams } from 'next/navigation';
import { useSaveProject } from '@/hooks/use-save-project';

export function PromptFlowGeneratorInput() {
  const [prompt, setPrompt] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const { setNodes, setEdges } = useReactFlow();
  const { projectId } = useParams();
  const { save } = useSaveProject(projectId as string);

  const handleGenerateFlow = async () => {
    if (!prompt.trim()) {
      toast.error('Please enter a prompt');
      return;
    }

    setIsLoading(true);
    toast.info('Generating flow from your prompt...');

    try {
      const response = await fetch('/api/ai/generate-ai-flow', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ prompt }),
      });

      // Get the response text for better error handling
      const responseText = await response.text();
      
      // Try to parse the response as JSON
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (e) {
        toast.error('API returned invalid JSON');
        throw new Error(`Invalid JSON response: ${responseText.substring(0, 100)}...`);
      }
      
      if (!response.ok) {
        toast.error(`API error: ${response.status}`);
        throw new Error(data.error || `API error: ${response.status}`);
      }
      
      // Validate response data
      if (!data) {
        toast.error('Empty response from API');
        throw new Error('Empty response from API');
      }
      
      // Check if nodes and edges arrays exist
      if (!data.nodes || !Array.isArray(data.nodes)) {
        toast.error('No nodes array in response');
        throw new Error(`Invalid response structure: missing or invalid nodes array`);
      }
      
      if (!data.edges || !Array.isArray(data.edges)) {
        toast.error('No edges array in response');
        throw new Error(`Invalid response structure: missing or invalid edges array`);
      }
      
      // Check if we got any nodes
      if (data.nodes.length === 0) {
        toast.error('No nodes were generated');
        throw new Error('No nodes were generated');
      }
      
      toast.info(`Applying flow with ${data.nodes.length} nodes and ${data.edges.length} edges...`);
      
      // Update the ReactFlow canvas with the generated nodes and edges
      setNodes(data.nodes);
      setEdges(data.edges);

      // Save the project with the new content
      await save();

      toast.success('AI flow generated and saved successfully');
      setIsOpen(false);
    } catch (error) {
      // Use toast to show error
      toast.error(error instanceof Error ? error.message : 'Failed to generate flow');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="icon" className="rounded-full">
          <Sparkles size={16} />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Generate AI Flow</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <Textarea
            placeholder="Describe what you want to create, e.g., 'Create a system for AI to generate a complete story with short videos'"
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            rows={6}
            className="resize-none"
          />
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline" disabled={isLoading}>Cancel</Button>
          </DialogClose>
          <Button onClick={handleGenerateFlow} disabled={isLoading || !prompt.trim()}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Sparkles className="mr-2 h-4 w-4" />
                Generate Flow
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
