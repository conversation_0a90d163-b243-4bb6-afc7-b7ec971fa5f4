/* 
   This CSS file specifically targets the bubble menu that appears when text is selected 
   It applies more compact styling to make the menu less bulky
*/

/* Base bubble menu styling */
.bubble-menu {
  padding: 0.25rem !important; /* More compact padding */
  border-radius: 0.375rem !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  z-index: 100 !important;
  /* Ensure it's above other elements */
}

/* Compact styling for bubble menu buttons */
.bubble-menu button {
  padding: 0.25rem 0.5rem !important; /* Reduced padding for buttons */
  gap: 0.25rem !important; /* Smaller gap between icon and text */
  font-size: 0.875rem !important; /* Smaller font size */
  border-radius: 0.25rem !important;
  min-height: unset !important;
  height: 28px !important; /* Fixed smaller height */
}

/* Ensure icons are properly sized */
.bubble-menu button svg {
  width: 14px !important;
  height: 14px !important;
}

/* For compatibility with existing code that might use these classes */
.selection-menu {
  padding: 0.25rem; /* Reduced padding */
  border-radius: 0.375rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.selection-menu-button {
  padding: 0.25rem 0.5rem; /* Reduced padding */
  gap: 0.25rem; /* Reduced gap */
  font-size: 0.875rem; /* Smaller font size */
  height: 28px; /* Fixed smaller height */
}

/* Dark mode adjustments */
.dark .bubble-menu {
  background-color: rgba(30, 41, 59, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
