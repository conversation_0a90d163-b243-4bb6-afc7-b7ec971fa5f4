'use client';

// This file is deprecated and will be removed.
// Please use the Kibo UI Editor from components/ui/kibo-ui/editor/index.tsx instead.

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function Editor() {
  const router = useRouter();

  // Redirect to the main editor page
  useEffect(() => {
    router.push('/');
  }, [router]);

  return null;
}
