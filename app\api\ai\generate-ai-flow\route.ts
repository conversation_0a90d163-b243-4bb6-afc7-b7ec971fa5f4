import { getSubscribedUser } from '@/lib/auth';
import { parseError } from '@/lib/error/parse';
import { createRateLimiter, slidingWindow } from '@/lib/rate-limit';
import { trackCreditUsage } from '@/lib/stripe';
import { nanoid } from 'nanoid';

export const maxDuration = 30;

const rateLimiter = createRateLimiter({
  limiter: slidingWindow(5, '1 m'),
  prefix: 'api-generate-ai-flow',
});

// Type definitions for better type safety
interface NodePosition {
  x: number;
  y: number;
}

interface Node {
  id: string;
  type: string;
  data: {
    source: 'primitive' | 'transform';
    instructions?: string;
    model?: string;
    [key: string]: any;
  };
  position: NodePosition;
  origin?: [number, number];
  [key: string]: any;
}

interface Edge {
  id: string;
  source: string;
  target: string;
  type: string;
  [key: string]: any;
}

interface FlowData {
  nodes: Node[];
  edges: Edge[];
}

export const POST = async (req: Request) => {
  try {
    const user = await getSubscribedUser();

    if (!user) {
      return Response.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    } // Apply rate limiting - skip if there's an error with Redis
    try {
      const { success } = await rateLimiter.limit(user.id);
      if (!success) {
        return Response.json(
          { error: 'Too many requests. Please try again later.' },
          { status: 429 }
        );
      }
    } catch (error) {
      // If rate limiting fails, we'll proceed without it
      // This allows the feature to work even if Redis is unavailable
    }

    const { prompt } = await req.json();

    if (!prompt || typeof prompt !== 'string') {
      return Response.json(
        { error: 'Prompt is required and must be a string' },
        { status: 400 }
      );
    }

    // Calculate positions for a nice cascading layout
    const calculateNodePosition = (
      index: number,
      totalNodes: number,
      gridSize = 250
    ) => {
      // Create a grid layout with maximum 3 nodes per row
      const nodesPerRow = 3;
      const row = Math.floor(index / nodesPerRow);
      const col = index % nodesPerRow;

      return {
        x: col * gridSize,
        y: row * gridSize,
      };
    };
    try {
      // Import env to get the Google Generative AI API key
      const { env } = await import('@/lib/env');

      // Get the Google Generative AI API key from environment variables
      const apiKey = env.GOOGLE_GENERATIVE_AI_API_KEY;

      // Skip the development mode check and always try to use the real API
      // This allows testing with real API responses
      if (!apiKey || apiKey === 'mock-value') {
        // Only use mock if no API key is available
        const { getMockAIFlow } = await import('@/lib/mock-ai-response');
        const mockFlow = getMockAIFlow(prompt);

        // Add validation to ensure the mock flow has the required structure
        if (
          !mockFlow.nodes ||
          !Array.isArray(mockFlow.nodes) ||
          mockFlow.nodes.length === 0
        ) {
          return Response.json(
            { error: 'Mock AI flow generator produced invalid nodes array' },
            { status: 500 }
          );
        }

        if (!mockFlow.edges || !Array.isArray(mockFlow.edges)) {
          return Response.json(
            { error: 'Mock AI flow generator produced invalid edges array' },
            { status: 500 }
          );
        }

        return Response.json(mockFlow);
      }

      // Use the real Gemini API with our API key
      const response = await fetch(
        `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${apiKey}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [
              {
                parts: [
                  {
                    text: `You are a workflow architect for TERSA, an AI workflow platform. Create a flow based on this prompt: "${prompt}"

Available node types:
- text: For text input and output
- image: For image input and generation
- video: For video input and generation
- audio: For audio input, speech synthesis, and transcription
- code: For coding and code generation

Each node has a "source" property which can be either "primitive" (user input) or "transform" (AI operation).

For "transform" nodes, include meaningful "instructions" that guide the AI on what to do.

Format your response as a JSON object with two arrays: "nodes" and "edges". Each node needs a unique ID, type, data object with source and instructions, and position object with x,y coordinates. Each edge connects a source node to a target node.`,
                  },
                ],
              },
            ],
            generationConfig: {
              temperature: 0.2,
              topK: 32,
              topP: 0.95,
              maxOutputTokens: 8192,
              responseMimeType: 'application/json',
              responseSchema: {
                type: 'object',
                properties: {
                  nodes: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        id: { type: 'string' },
                        type: {
                          type: 'string',
                          enum: ['text', 'image', 'video', 'audio', 'code'],
                        },
                        data: {
                          type: 'object',
                          properties: {
                            source: {
                              type: 'string',
                              enum: ['primitive', 'transform'],
                            },
                            instructions: { type: 'string' },
                            model: { type: 'string' },
                          },
                          required: ['source'],
                        },
                        position: {
                          type: 'object',
                          properties: {
                            x: { type: 'number' },
                            y: { type: 'number' },
                          },
                          required: ['x', 'y'],
                        },
                      },
                      required: ['id', 'type', 'data', 'position'],
                    },
                  },
                  edges: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        id: { type: 'string' },
                        source: { type: 'string' },
                        target: { type: 'string' },
                        type: { type: 'string', default: 'animated' },
                      },
                      required: ['id', 'source', 'target'],
                    },
                  },
                },
                required: ['nodes', 'edges'],
              },
            },
          }),
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Gemini API error: ${response.status} ${errorText}`);
      }

      const result = await response.json();

      if (!result.candidates?.[0]?.content?.parts?.[0]?.text) {
        throw new Error('Invalid response from Gemini API');
      }

      let flowData;
      try {
        // Extract the JSON content from the response
        const jsonText = result.candidates[0].content.parts[0].text;
        flowData = JSON.parse(jsonText);
      } catch (e) {
        console.error('Failed to parse JSON response:', e);
        throw new Error('Invalid JSON response from Gemini API');
      }

      // Validate the structure of the response
      if (!Array.isArray(flowData.nodes) || !Array.isArray(flowData.edges)) {
        throw new Error('Response must contain nodes and edges arrays');
      } // Create a map of node IDs and ensure all nodes have required properties
      const nodeIds = new Map();
      const nodeIdList: string[] = [];

      // First pass: process nodes and create IDs
      flowData.nodes = flowData.nodes.map((node, index) => {
        // Store original ID for reference
        const originalId = node.id || `node-${index}`;

        // Generate a new unique ID
        const newId = nanoid(6);
        nodeIds.set(originalId, newId);
        nodeIdList.push(newId);

        // Update the node with the new ID
        node.id = newId;

        // Set default origin for proper rendering
        node.origin = [0, 0.5];

        // Ensure position is valid
        if (
          !node.position ||
          typeof node.position.x !== 'number' ||
          typeof node.position.y !== 'number'
        ) {
          node.position = {
            x: index * 250,
            y: 0,
          };
        }

        return node;
      });

      // Second pass: fix edges to reference the new node IDs
      const edgeIds = new Set();
      flowData.edges = flowData.edges.filter((edge) => {
        // Skip edges that refer to non-existent nodes
        const sourceId = nodeIds.get(edge.source);
        const targetId = nodeIds.get(edge.target);

        if (!sourceId || !targetId) {
          return false; // Filter out invalid edges
        }

        // Update edge with new node IDs
        edge.source = sourceId;
        edge.target = targetId;

        // Ensure edge has a unique ID
        if (!edge.id || edgeIds.has(edge.id)) {
          edge.id = nanoid(6);
        }
        edgeIds.add(edge.id);

        // Ensure all edges have a type
        if (!edge.type) {
          edge.type = 'animated';
        }

        return true;
      });

      // Track credits usage for this operation
      await trackCreditUsage({
        userId: user.id,
        operation: 'ai_flow_generation',
        amount: 5, // Assuming this costs 5 credits
      });

      return Response.json(flowData);
    } catch (error) {
      console.error('Error in AI flow generation:', error);
      return Response.json(
        {
          error:
            error instanceof Error
              ? error.message
              : 'Failed to generate AI flow',
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in API route:', error);
    const { status, message } = parseError(error);
    return Response.json({ error: message }, { status });
  }
};
