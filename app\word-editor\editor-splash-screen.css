/* Splash Screen Styles */

.editor-splash-screen {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: hsl(var(--background));
  z-index: 100;
  animation: fadeOut 0.5s ease-in-out forwards;
  animation-delay: 1.5s;
  pointer-events: none;
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    visibility: hidden;
  }
}

.editor-splash-logo {
  width: 120px;
  height: 120px;
  margin-bottom: 1.5rem;
  animation: logoGlow 1.5s ease-in-out;
}

@keyframes logoGlow {
  0% {
    filter: drop-shadow(0 0 0px hsl(var(--primary) / 0.5));
    transform: scale(0.9);
  }
  50% {
    filter: drop-shadow(0 0 20px hsl(var(--primary) / 0.5));
    transform: scale(1.05);
  }
  100% {
    filter: drop-shadow(0 0 10px hsl(var(--primary) / 0.5));
    transform: scale(1);
  }
}

.editor-splash-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: hsl(var(--foreground));
  margin-bottom: 0.5rem;
  text-align: center;
  background: linear-gradient(
    to right,
    hsl(var(--primary)),
    hsl(var(--secondary))
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: slideIn 1s ease-in-out;
}

.editor-splash-subtitle {
  font-size: 1rem;
  color: hsl(var(--muted-foreground));
  text-align: center;
  max-width: 600px;
  animation: slideIn 1s ease-in-out 0.2s backwards;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.editor-loading-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 2rem;
  animation: fadeIn 0.5s ease-in-out 0.5s backwards;
}

.editor-loading-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: hsl(var(--primary));
  animation: loadingDots 1s infinite ease-in-out;
}

.editor-loading-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.editor-loading-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes loadingDots {
  0% {
    transform: scale(0.5);
    opacity: 0.3;
  }
  50% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(0.5);
    opacity: 0.3;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
