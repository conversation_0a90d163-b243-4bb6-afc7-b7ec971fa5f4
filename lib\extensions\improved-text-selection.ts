'use client';

import { Extension } from '@tiptap/core';
import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state';
import { Decoration, DecorationSet } from '@tiptap/pm/view';

/**
 * ImprovedTextSelection extension for TipTap editor
 * Enhances text selection styling and behavior especially for headings and other special elements
 */
export const ImprovedTextSelection = Extension.create({
  name: 'improvedTextSelection',

  // Add custom attributes to the editor
  addGlobalAttributes() {
    return [
      {
        types: ['heading'],
        attributes: {
          'data-selection-state': {
            default: 'none',
            parseHTML: (element) =>
              element.getAttribute('data-selection-state') || 'none',
            renderHTML: (attributes) => {
              return attributes['data-selection-state'] !== 'none'
                ? { 'data-selection-state': attributes['data-selection-state'] }
                : {};
            },
          },
        },
      },
    ];
  },

  addKeyboardShortcuts() {
    return {
      // Improve keyboard selection behavior
      'Shift-ArrowRight': ({ editor }) => {
        // Let browser handle it but track selection
        setTimeout(() => this.trackSelectionState(editor), 0);
        return true;
      },
      'Shift-ArrowLeft': ({ editor }) => {
        setTimeout(() => this.trackSelectionState(editor), 0);
        return true;
      },
      'Shift-ArrowUp': ({ editor }) => {
        setTimeout(() => this.trackSelectionState(editor), 0);
        return true;
      },
      'Shift-ArrowDown': ({ editor }) => {
        setTimeout(() => this.trackSelectionState(editor), 0);
        return true;
      },
      'Mod-a': ({ editor }) => {
        // Improve select all behavior
        setTimeout(() => this.trackSelectionState(editor), 0);
        return true;
      },
    };
  },

  // Helper method to track selection state
  trackSelectionState(editor) {
    const { selection } = editor.state;
    if (!selection || selection.empty) return;

    // Find all heading nodes in the selection
    editor.state.doc.nodesBetween(selection.from, selection.to, (node, pos) => {
      if (node.type.name === 'heading') {
        editor.commands.updateAttributes(node.type.name, {
          'data-selection-state': 'selected',
        });
      }
      return true;
    });

    // Clear selection state after a delay
    setTimeout(() => {
      if (!editor.isDestroyed) {
        editor.commands.updateAttributes('heading', {
          'data-selection-state': 'none',
        });
      }
    }, 500);
  },

  addProseMirrorPlugins() {
    const extensionThis = this;

    return [
      new Plugin({
        key: new PluginKey('improvedTextSelection'),
        props: {
          handleDOMEvents: {
            mouseup(view) {
              extensionThis.trackSelectionState(view.state.editor);
              return false;
            },
            selectionchange(view) {
              extensionThis.trackSelectionState(view.state.editor);
              return false;
            },
          },
          // Add special decorations to improve selection appearance
          decorations(state) {
            const { selection } = state;
            if (selection.empty) return DecorationSet.empty;

            const decorations: Decoration[] = [];

            // Add special decorations for selected text
            state.doc.nodesBetween(
              selection.from,
              selection.to,
              (node, pos) => {
                if (node.isText && node.text) {
                  // Skip if this text is inside an already decorated node
                  const parent = state.doc.resolve(pos).parent;
                  if (parent.type.name === 'heading') {
                    // Already handled by attributes
                    return true;
                  }

                  // Add decoration for text selection
                  const from = Math.max(pos, selection.from);
                  const to = Math.min(pos + node.nodeSize, selection.to);

                  if (from < to) {
                    decorations.push(
                      Decoration.inline(from, to, {
                        class: 'improved-text-selection',
                      })
                    );
                  }
                }
                return true;
              }
            );

            return DecorationSet.create(state.doc, decorations);
          },
        },
      }),
    ];
  },
});
