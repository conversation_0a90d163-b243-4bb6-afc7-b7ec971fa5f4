'use client';

import { useReactFlow } from '@xyflow/react';
import type { ChangeEvent, FC } from 'react';
import type { CanvasProps } from './canvas';
import { Canvas as CanvasComponent } from './canvas';

export const CanvasWithFeatures: FC<CanvasProps> = (props) => {
  const { getNodes, getEdges, setNodes, setEdges } = useReactFlow();

  const handleDownload = () => {
    const flowData = {
      nodes: getNodes(),
      edges: getEdges(),
    };

    const jsonString = `data:text/json;charset=utf-8,${encodeURIComponent(
      JSON.stringify(flowData, null, 2)
    )}`;
    const link = document.createElement('a');
    link.href = jsonString;
    link.download = 'tersa-flow.json';
    link.click();
  };

  const handleUpload = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) {
      return;
    }

    const reader = new FileReader();

    // Handle file parsing and validation
    const processFlowData = (content: string) => {
      try {
        const flowData = JSON.parse(content);
        // Basic validation to ensure the file is a valid flow
        if (Array.isArray(flowData.nodes) && Array.isArray(flowData.edges)) {
          setNodes(flowData.nodes);
          setEdges(flowData.edges);
        } else {
          alert('Error: Invalid flow file format.');
        }
      } catch (_) {
        alert('Error: Could not parse the flow file.');
      }
    };

    reader.onload = (e) => {
      const content = e.target?.result;
      if (typeof content === 'string') {
        processFlowData(content);
      }
    };

    reader.readAsText(file);
    // Reset the input value to allow uploading the same file again
    event.target.value = '';
  };

  return (
    <CanvasComponent
      {...props}
      onDownload={handleDownload}
      onUpload={handleUpload}
    />
  );
};
