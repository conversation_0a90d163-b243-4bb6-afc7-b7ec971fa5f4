import { cn } from '@/lib/utils';
import type { SVGProps } from 'react';

export const Logo = ({ className, ...props }: SVGProps<SVGSVGElement>) => (
  <svg
    viewBox="0 0 106 118"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={cn('h-auto w-full text-primary', className)}
    {...props}
  >
    <title>Logo</title>
    <path
      d="M52.5762 82.9736C61.9824 82.9737 69.6074 90.5996 69.6074 100.006C69.6073 109.412 61.9824 117.037 52.5762 117.037C43.1699 117.037 35.545 109.412 35.5449 100.006C35.5449 90.5995 43.1699 82.9736 52.5762 82.9736ZM17.0312 62.2402C26.4376 62.2402 34.0635 69.8661 34.0635 79.2725C34.0632 88.6786 26.4374 96.3037 17.0312 96.3037C7.62527 96.3035 0.000248431 88.6784 0 79.2725C0 69.8663 7.62511 62.2404 17.0312 62.2402ZM88.1201 62.2402C97.5264 62.2403 105.151 69.8662 105.151 79.2725C105.151 88.6785 97.5262 96.3037 88.1201 96.3037C78.714 96.3037 71.0891 88.6786 71.0889 79.2725C71.0889 69.8661 78.7138 62.2402 88.1201 62.2402ZM52.5762 41.5059C61.9823 41.5059 69.6073 49.131 69.6074 58.5371C69.6074 67.9434 61.9824 75.5693 52.5762 75.5693C43.1699 75.5693 35.5449 67.9434 35.5449 58.5371C35.5451 49.1309 43.17 41.5059 52.5762 41.5059ZM17.0312 20.7725C26.4376 20.7725 34.0635 28.3974 34.0635 37.8037C34.0635 47.21 26.4376 54.835 17.0312 54.835C7.62512 54.8348 1.03086e-06 47.2099 0 37.8037C0 28.3975 7.62511 20.7727 17.0312 20.7725ZM88.1201 20.7725C97.5264 20.7725 105.151 28.3974 105.151 37.8037C105.151 47.21 97.5264 54.8349 88.1201 54.835C78.7138 54.835 71.0889 47.21 71.0889 37.8037C71.0889 28.3974 78.7138 20.7725 88.1201 20.7725ZM52.5762 0.0371094C61.9824 0.0371661 69.6074 7.66305 69.6074 17.0693C69.6073 26.4755 61.9824 34.1005 52.5762 34.1006C43.1699 34.1006 35.545 26.4756 35.5449 17.0693C35.5449 7.66302 43.1699 0.0371094 52.5762 0.0371094Z"
      fill="currentColor"
    />
  </svg>
);
