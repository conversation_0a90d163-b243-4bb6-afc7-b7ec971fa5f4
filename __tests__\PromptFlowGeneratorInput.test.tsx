import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import fetchMock from 'jest-fetch-mock';
import { toast } from 'sonner';
import { PromptFlowGeneratorInput } from '../components/canvas/PromptFlowGeneratorInput';

// Mock dependencies
jest.mock('@xyflow/react', () => ({
  useReactFlow: () => ({
    setNodes: jest.fn(),
    setEdges: jest.fn(),
  }),
}));

jest.mock('next/navigation', () => ({
  useParams: () => ({ projectId: 'test-project-id' }),
}));

jest.mock('@/hooks/use-save-project', () => ({
  useSaveProject: () => ({ save: jest.fn() }),
}));

jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

describe('PromptFlowGeneratorInput', () => {
  beforeEach(() => {
    fetchMock.resetMocks();
  });

  it('should render a button that opens a dialog', () => {
    render(<PromptFlowGeneratorInput />);

    // Check if the button exists
    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();

    // Click the button to open the dialog
    fireEvent.click(button);

    // Check if the dialog content appears
    expect(screen.getByText('Generate AI Flow')).toBeInTheDocument();
    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });

  it('should show an error when submitting an empty prompt', async () => {
    render(<PromptFlowGeneratorInput />);

    // Open the dialog
    fireEvent.click(screen.getByRole('button'));

    // Try to submit with empty prompt
    fireEvent.click(screen.getByText('Generate Flow'));

    // Check if error toast was shown
    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Please enter a prompt');
    });
  });

  it('should call the API and handle successful response', async () => {
    // Mock successful API response
    fetchMock.mockResponseOnce(
      JSON.stringify({
        nodes: [
          {
            id: 'node1',
            type: 'text',
            data: { source: 'primitive' },
            position: { x: 0, y: 0 },
          },
        ],
        edges: [
          { id: 'edge1', source: 'node1', target: 'node2', type: 'animated' },
        ],
      })
    );

    render(<PromptFlowGeneratorInput />);

    // Open the dialog
    fireEvent.click(screen.getByRole('button'));

    // Enter a prompt
    fireEvent.change(screen.getByRole('textbox'), {
      target: { value: 'Create a blog post generator' },
    });

    // Submit the form
    fireEvent.click(screen.getByText('Generate Flow'));

    // Check if the API was called with correct parameters
    await waitFor(() => {
      expect(fetchMock).toHaveBeenCalledWith(
        '/api/ai/generate-ai-flow',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({ prompt: 'Create a blog post generator' }),
        })
      );
    });

    // Check if success toast was shown
    await waitFor(() => {
      expect(toast.success).toHaveBeenCalledWith(
        'AI flow generated successfully'
      );
    });
  });

  it('should handle API errors correctly', async () => {
    // Mock API error response
    fetchMock.mockRejectOnce(new Error('API Error'));

    render(<PromptFlowGeneratorInput />);

    // Open the dialog
    fireEvent.click(screen.getByRole('button'));

    // Enter a prompt
    fireEvent.change(screen.getByRole('textbox'), {
      target: { value: 'Create a blog post generator' },
    });

    // Submit the form
    fireEvent.click(screen.getByText('Generate Flow'));

    // Check if error toast was shown
    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('API Error');
    });
  });
});
