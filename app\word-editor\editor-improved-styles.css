/* Improved editor styling */

/* Base editor styling */
.editor-content {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  line-height: 1.6;
  color: hsl(var(--foreground));
  outline: none !important;
}

/* Selection styling */
.editor-content .ProseMirror ::selection {
  background-color: rgba(59, 130, 246, 0.3);
  border-radius: 0.125rem;
}

.dark .editor-content .ProseMirror ::selection {
  background-color: rgba(96, 165, 250, 0.4);
  color: white;
}

/* Typography improvements */
.editor-content .ProseMirror h1 {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.2;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: hsl(var(--foreground));
  border-bottom: 1px solid hsl(var(--border));
  padding-bottom: 0.5rem;
}

.editor-content .ProseMirror h2 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.3;
  margin-top: 1.25rem;
  margin-bottom: 0.75rem;
  color: hsl(var(--foreground));
}

.editor-content .ProseMirror h3 {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.4;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  color: hsl(var(--foreground));
}

.editor-content .ProseMirror p {
  margin-bottom: 1rem;
}

.editor-content .ProseMirror ul,
.editor-content .ProseMirror ol {
  margin-left: 1.5rem;
  margin-bottom: 1rem;
}

.editor-content .ProseMirror li {
  margin-bottom: 0.25rem;
}

.editor-content .ProseMirror blockquote {
  border-left: 3px solid hsl(var(--border));
  padding-left: 1rem;
  margin-left: 0;
  font-style: italic;
  color: hsl(var(--muted-foreground));
}

.editor-content .ProseMirror code {
  background-color: hsl(var(--muted) / 0.5);
  border-radius: 0.25rem;
  padding: 0.125rem 0.25rem;
  font-family: monospace;
}

.editor-content .ProseMirror pre {
  background-color: hsl(var(--muted) / 0.7);
  border-radius: 0.25rem;
  padding: 1rem;
  overflow-x: auto;
  font-family: monospace;
}

/* Table styling */
.editor-content .ProseMirror table {
  border-collapse: collapse;
  margin: 1rem 0;
  overflow: hidden;
  width: 100%;
}

.editor-content .ProseMirror table td,
.editor-content .ProseMirror table th {
  border: 1px solid hsl(var(--border));
  padding: 0.5rem;
}

.editor-content .ProseMirror table th {
  background-color: hsl(var(--muted) / 0.5);
  font-weight: 600;
}

/* Images */
.editor-content .ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 0.25rem;
}

/* Links */
.editor-content .ProseMirror a {
  color: hsl(var(--primary));
  text-decoration: underline;
  text-decoration-thickness: 1px;
  text-underline-offset: 2px;
}

/* Task lists */
.editor-content .ProseMirror ul[data-type="taskList"] {
  list-style: none;
  padding: 0;
}

.editor-content .ProseMirror ul[data-type="taskList"] li {
  display: flex;
  align-items: center;
}

.editor-content .ProseMirror ul[data-type="taskList"] li > label {
  margin-right: 0.5rem;
  user-select: none;
}

/* Placeholder */
.editor-content .ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: hsl(var(--muted-foreground) / 0.6);
  pointer-events: none;
  height: 0;
}

/* Responsive Bubble Menu */
.bubble-menu {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  padding: 0.25rem !important;
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.dark .bubble-menu {
  background-color: hsl(var(--card));
  border-color: hsl(var(--border));
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.bubble-menu button {
  height: 28px !important;
  width: 28px !important;
  padding: 0.15rem !important;
}

/* Slash commands styling */
.slash-command-container {
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  max-height: 300px;
  overflow-y: auto;
}

.dark .slash-command-container {
  background-color: hsl(var(--card));
  border-color: hsl(var(--border));
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}
