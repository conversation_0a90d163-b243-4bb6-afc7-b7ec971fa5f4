# v1.0.23 (Sat May 17 2025)

#### ⚠️ Pushed to `main`

- Add support for image sizes ([@haydenbleasel](https://github.com/haydenbleasel))

#### Authors: 1

- <PERSON> ([@haydenbleasel](https://github.com/haydenbleasel))

---

# v1.0.22 (Fri May 16 2025)

#### ⚠️ Pushed to `main`

- For #61 ([@haydenbleasel](https://github.com/haydenbleasel))

#### Authors: 1

- <PERSON> ([@haydenbleasel](https://github.com/haydenbleasel))

---

# v1.0.21 (Fri May 16 2025)

#### ⚠️ Pushed to `main`

- 🤷 ([@haydenbleasel](https://github.com/haydenbleasel))

#### Authors: 1

- <PERSON> ([@haydenbleasel](https://github.com/haydenbleasel))

---

# v1.0.20 (Fri May 16 2025)

#### ⚠️ Pushed to `main`

- Create model_request.md ([@haydenbleasel](https://github.com/haydenbleasel))

#### Authors: 1

- <PERSON> ([@haydenbleasel](https://github.com/haydenbleasel))

---

# v1.0.19 (Fri May 16 2025)

#### ⚠️ Pushed to `main`

- Send feedback to GitHub ([@haydenbleasel](https://github.com/haydenbleasel))

#### Authors: 1

- Hayden Bleasel ([@haydenbleasel](https://github.com/haydenbleasel))

---

# v1.0.18 (Thu May 15 2025)

#### ⚠️ Pushed to `main`

- Update page.tsx ([@haydenbleasel](https://github.com/haydenbleasel))

#### Authors: 1

- Hayden Bleasel ([@haydenbleasel](https://github.com/haydenbleasel))

---

# v1.0.17 (Thu May 15 2025)

#### ⚠️ Pushed to `main`

- Remove remnants ([@haydenbleasel](https://github.com/haydenbleasel))

#### Authors: 1

- Hayden Bleasel ([@haydenbleasel](https://github.com/haydenbleasel))

---

# v1.0.16 (Thu May 15 2025)

:tada: This release contains work from a new contributor! :tada:

Thank you, Karel Vuong ([@karelvuong](https://github.com/karelvuong)), for all your work!

#### 🐛 Bug Fix

- fix: kibo link in readme [#53](https://github.com/haydenbleasel/tersa/pull/53) ([@karelvuong](https://github.com/karelvuong))

#### Authors: 1

- Karel Vuong ([@karelvuong](https://github.com/karelvuong))

---

# v1.0.15 (Thu May 15 2025)

#### ⚠️ Pushed to `main`

- Update transform.tsx ([@haydenbleasel](https://github.com/haydenbleasel))

#### Authors: 1

- Hayden Bleasel ([@haydenbleasel](https://github.com/haydenbleasel))

---

# v1.0.14 (Thu May 15 2025)

#### ⚠️ Pushed to `main`

- Update transform.tsx ([@haydenbleasel](https://github.com/haydenbleasel))

#### Authors: 1

- Hayden Bleasel ([@haydenbleasel](https://github.com/haydenbleasel))

---

# v1.0.13 (Thu May 15 2025)

#### ⚠️ Pushed to `main`

- Misc fixes ([@haydenbleasel](https://github.com/haydenbleasel))

#### Authors: 1

- Hayden Bleasel ([@haydenbleasel](https://github.com/haydenbleasel))

---

# v1.0.12 (Thu May 15 2025)

#### ⚠️ Pushed to `main`

- Increase hobby credits ([@haydenbleasel](https://github.com/haydenbleasel))
- Fix edit image prompt ([@haydenbleasel](https://github.com/haydenbleasel))

#### Authors: 1

- Hayden Bleasel ([@haydenbleasel](https://github.com/haydenbleasel))

---

# v1.0.11 (Thu May 15 2025)

#### ⚠️ Pushed to `main`

- Update hero.tsx ([@haydenbleasel](https://github.com/haydenbleasel))

#### Authors: 1

- Hayden Bleasel ([@haydenbleasel](https://github.com/haydenbleasel))

---

# v1.0.10 (Thu May 15 2025)

#### ⚠️ Pushed to `main`

- Update env.ts ([@haydenbleasel](https://github.com/haydenbleasel))

#### Authors: 1

- Hayden Bleasel ([@haydenbleasel](https://github.com/haydenbleasel))

---

# v1.0.9 (Thu May 15 2025)

#### ⚠️ Pushed to `main`

- Misc fixes ([@haydenbleasel](https://github.com/haydenbleasel))

#### Authors: 1

- Hayden Bleasel ([@haydenbleasel](https://github.com/haydenbleasel))

---

# v1.0.8 (Thu May 15 2025)

#### ⚠️ Pushed to `main`

- Update hero.tsx ([@haydenbleasel](https://github.com/haydenbleasel))

#### Authors: 1

- Hayden Bleasel ([@haydenbleasel](https://github.com/haydenbleasel))

---

# v1.0.7 (Thu May 15 2025)

#### ⚠️ Pushed to `main`

- Fix pricing page links ([@haydenbleasel](https://github.com/haydenbleasel))

#### Authors: 1

- Hayden Bleasel ([@haydenbleasel](https://github.com/haydenbleasel))

---

# v1.0.6 (Thu May 15 2025)

#### ⚠️ Pushed to `main`

- Update sign-up-form.tsx ([@haydenbleasel](https://github.com/haydenbleasel))

#### Authors: 1

- Hayden Bleasel ([@haydenbleasel](https://github.com/haydenbleasel))

---

# v1.0.5 (Thu May 15 2025)

#### ⚠️ Pushed to `main`

- Update route.tsx ([@haydenbleasel](https://github.com/haydenbleasel))

#### Authors: 1

- Hayden Bleasel ([@haydenbleasel](https://github.com/haydenbleasel))

---

# v1.0.4 (Thu May 15 2025)

#### ⚠️ Pushed to `main`

- Update sign-up-form.tsx ([@haydenbleasel](https://github.com/haydenbleasel))

#### Authors: 1

- Hayden Bleasel ([@haydenbleasel](https://github.com/haydenbleasel))

---

# v1.0.3 (Thu May 15 2025)

#### ⚠️ Pushed to `main`

- Update sign-up-form.tsx ([@haydenbleasel](https://github.com/haydenbleasel))

#### Authors: 1

- Hayden Bleasel ([@haydenbleasel](https://github.com/haydenbleasel))

---

# v1.0.2 (Thu May 15 2025)

#### ⚠️ Pushed to `main`

- Update middleware.ts ([@haydenbleasel](https://github.com/haydenbleasel))

#### Authors: 1

- Hayden Bleasel ([@haydenbleasel](https://github.com/haydenbleasel))

---

# v1.0.1 (Thu May 15 2025)

#### ⚠️ Pushed to `main`

- Update hero.tsx ([@haydenbleasel](https://github.com/haydenbleasel))

#### Authors: 1

- Hayden Bleasel ([@haydenbleasel](https://github.com/haydenbleasel))

---

# v1.0.0 (Thu May 15 2025)

#### 💥 Breaking Change

- v1 [#51](https://github.com/haydenbleasel/tersa/pull/51) ([@haydenbleasel](https://github.com/haydenbleasel))

#### Authors: 1

- Hayden Bleasel ([@haydenbleasel](https://github.com/haydenbleasel))

---

# v0.0.1 (Thu May 15 2025)

:tada: This release contains work from a new contributor! :tada:

Thank you, Hayden Bleasel ([@haydenbleasel](https://github.com/haydenbleasel)), for all your work!

#### 🐛 Bug Fix

- Launch [#49](https://github.com/haydenbleasel/tersa/pull/49) ([@haydenbleasel](https://github.com/haydenbleasel))
- Migrate to Supabase [#1](https://github.com/haydenbleasel/tersa/pull/1) ([@haydenbleasel](https://github.com/haydenbleasel))

#### ⚠️ Pushed to `main`

- Update auto ([@haydenbleasel](https://github.com/haydenbleasel))
- Update package.json ([@haydenbleasel](https://github.com/haydenbleasel))
- For #7 ([@haydenbleasel](https://github.com/haydenbleasel))
- Add error titles ([@haydenbleasel](https://github.com/haydenbleasel))
- Tooltip fixes ([@haydenbleasel](https://github.com/haydenbleasel))
- Update page.tsx ([@haydenbleasel](https://github.com/haydenbleasel))
- Add regenerate buttons ([@haydenbleasel](https://github.com/haydenbleasel))
- Misc fix ([@haydenbleasel](https://github.com/haydenbleasel))
- Update demo.ts ([@haydenbleasel](https://github.com/haydenbleasel))
- Update layout.tsx ([@haydenbleasel](https://github.com/haydenbleasel))
- Only parse direct incomers ([@haydenbleasel](https://github.com/haydenbleasel))
- Update index.tsx ([@haydenbleasel](https://github.com/haydenbleasel))
- Fix audio ([@haydenbleasel](https://github.com/haydenbleasel))
- Rework to use `generated` prop ([@haydenbleasel](https://github.com/haydenbleasel))
- Misc naming fixes ([@haydenbleasel](https://github.com/haydenbleasel))
- Misc fixes ([@haydenbleasel](https://github.com/haydenbleasel))
- Update create.ts ([@haydenbleasel](https://github.com/haydenbleasel))
- Remove redundant transcription ([@haydenbleasel](https://github.com/haydenbleasel))
- Finish image generation ([@haydenbleasel](https://github.com/haydenbleasel))
- Drop node fixes ([@haydenbleasel](https://github.com/haydenbleasel))
- Pre compute transcriptions ([@haydenbleasel](https://github.com/haydenbleasel))
- Update transform.tsx ([@haydenbleasel](https://github.com/haydenbleasel))
- Parse image descriptions ([@haydenbleasel](https://github.com/haydenbleasel))
- Make vision and transcription model configurable ([@haydenbleasel](https://github.com/haydenbleasel))
- Isolate connection validity logic ([@haydenbleasel](https://github.com/haydenbleasel))
- Finish text transform ([@haydenbleasel](https://github.com/haydenbleasel))
- Start building node logic ([@haydenbleasel](https://github.com/haydenbleasel))
- Delete transcribe node ([@haydenbleasel](https://github.com/haydenbleasel))
- Cleanup node types ([@haydenbleasel](https://github.com/haydenbleasel))
- Merge audio nodes ([@haydenbleasel](https://github.com/haydenbleasel))
- Merge video nodes ([@haydenbleasel](https://github.com/haydenbleasel))
- Merge image nodes ([@haydenbleasel](https://github.com/haydenbleasel))
- Merge text nodes ([@haydenbleasel](https://github.com/haydenbleasel))
- Remove transform nodes from toolbar ([@haydenbleasel](https://github.com/haydenbleasel))
- Add transform / primitive switcher ([@haydenbleasel](https://github.com/haydenbleasel))
- Flatten primitive nodes ([@haydenbleasel](https://github.com/haydenbleasel))
- Update next.config.ts ([@haydenbleasel](https://github.com/haydenbleasel))
- Update transcribe.tsx ([@haydenbleasel](https://github.com/haydenbleasel))
- Responsive fixes ([@haydenbleasel](https://github.com/haydenbleasel))
- Split up transform nodes ([@haydenbleasel](https://github.com/haydenbleasel))
- Start breaking up nodes ([@haydenbleasel](https://github.com/haydenbleasel))
- Fix incoming parser ([@haydenbleasel](https://github.com/haydenbleasel))
- Fix paths ([@haydenbleasel](https://github.com/haydenbleasel))
- Merge in waitlist ([@haydenbleasel](https://github.com/haydenbleasel))
- Update inner.tsx ([@haydenbleasel](https://github.com/haydenbleasel))
- Type fixes ([@haydenbleasel](https://github.com/haydenbleasel))
- Finish drafting speech transform node ([@haydenbleasel](https://github.com/haydenbleasel))
- Update model-selector.tsx ([@haydenbleasel](https://github.com/haydenbleasel))
- Add project settings ([@haydenbleasel](https://github.com/haydenbleasel))
- Save previews to DB ([@haydenbleasel](https://github.com/haydenbleasel))
- Add ability to create projects ([@haydenbleasel](https://github.com/haydenbleasel))
- Finish implementing saving ([@haydenbleasel](https://github.com/haydenbleasel))
- Add database and projects ([@haydenbleasel](https://github.com/haydenbleasel))
- Store width / height for media ([@haydenbleasel](https://github.com/haydenbleasel))
- Fix padding and uploaders on media nodes ([@haydenbleasel](https://github.com/haydenbleasel))
- Finish audio uploads and transform transcription ([@haydenbleasel](https://github.com/haydenbleasel))
- Start working on file uploads ([@haydenbleasel](https://github.com/haydenbleasel))
- Scaffold automatic transcription ([@haydenbleasel](https://github.com/haydenbleasel))
- Delete unused images ([@haydenbleasel](https://github.com/haydenbleasel))
- Add dynamic image models ([@haydenbleasel](https://github.com/haydenbleasel))
- Remove proximity connections ([@haydenbleasel](https://github.com/haydenbleasel))
- Fix theme switcher ([@haydenbleasel](https://github.com/haydenbleasel))
- Improve design of Auth ([@haydenbleasel](https://github.com/haydenbleasel))
- Build fixes ([@haydenbleasel](https://github.com/haydenbleasel))
- Patch Kibo UI editor issues ([@haydenbleasel](https://github.com/haydenbleasel))
- Add vercel analytics ([@haydenbleasel](https://github.com/haydenbleasel))
- Update images ([@haydenbleasel](https://github.com/haydenbleasel))
- Add dark mode support ([@haydenbleasel](https://github.com/haydenbleasel))
- Update files ([@haydenbleasel](https://github.com/haydenbleasel))
- Scaffold env ([@haydenbleasel](https://github.com/haydenbleasel))
- Misc fixes, group models ([@haydenbleasel](https://github.com/haydenbleasel))
- Isolate chat models ([@haydenbleasel](https://github.com/haydenbleasel))
- Start working on model selection ([@haydenbleasel](https://github.com/haydenbleasel))
- Finish redesigning cards ([@haydenbleasel](https://github.com/haydenbleasel))
- Start working on design updates ([@haydenbleasel](https://github.com/haydenbleasel))
- Fix transform nodes, add "esc" shortcut to drop node ([@haydenbleasel](https://github.com/haydenbleasel))
- Make transform nodes compute their own prompts ([@haydenbleasel](https://github.com/haydenbleasel))
- Update text.tsx ([@haydenbleasel](https://github.com/haydenbleasel))
- Add more scaffolding nodes ([@haydenbleasel](https://github.com/haydenbleasel))
- UI fixes ([@haydenbleasel](https://github.com/haydenbleasel))
- Rebuild drop node ([@haydenbleasel](https://github.com/haydenbleasel))
- Add focus button ([@haydenbleasel](https://github.com/haydenbleasel))
- Start working on toolbar ([@haydenbleasel](https://github.com/haydenbleasel))
- Fix handle code ([@haydenbleasel](https://github.com/haydenbleasel))
- Prevent cyclic connections ([@haydenbleasel](https://github.com/haydenbleasel))
- Offset edges ([@haydenbleasel](https://github.com/haydenbleasel))
- Add animated connection line ([@haydenbleasel](https://github.com/haydenbleasel))
- Add animated edges ([@haydenbleasel](https://github.com/haydenbleasel))
- Start working on proximity ([@haydenbleasel](https://github.com/haydenbleasel))
- Draft node toolbar ([@haydenbleasel](https://github.com/haydenbleasel))
- Add ability to resize node ([@haydenbleasel](https://github.com/haydenbleasel))
- Start working on drop connect ([@haydenbleasel](https://github.com/haydenbleasel))
- Add tooltips ([@haydenbleasel](https://github.com/haydenbleasel))
- Add image support ([@haydenbleasel](https://github.com/haydenbleasel))
- Create node layout ([@haydenbleasel](https://github.com/haydenbleasel))
- Add auth screens, misc fixes ([@haydenbleasel](https://github.com/haydenbleasel))
- Update canvas.tsx ([@haydenbleasel](https://github.com/haydenbleasel))
- Start working on AI logic ([@haydenbleasel](https://github.com/haydenbleasel))
- Update primary color, add transform node ([@haydenbleasel](https://github.com/haydenbleasel))
- Add Clerk ([@haydenbleasel](https://github.com/haydenbleasel))
- Add Tailwind typography ([@haydenbleasel](https://github.com/haydenbleasel))
- Split out toolbar ([@haydenbleasel](https://github.com/haydenbleasel))
- Draft image and text nodes ([@haydenbleasel](https://github.com/haydenbleasel))
- Update biome.json ([@haydenbleasel](https://github.com/haydenbleasel))
- Draft toolbar ([@haydenbleasel](https://github.com/haydenbleasel))
- Install tw-animate-css ([@haydenbleasel](https://github.com/haydenbleasel))
- Install all components ([@haydenbleasel](https://github.com/haydenbleasel))
- Install shadcn/ui ([@haydenbleasel](https://github.com/haydenbleasel))
- Scaffold canvas ([@haydenbleasel](https://github.com/haydenbleasel))
- Add Ultracite ([@haydenbleasel](https://github.com/haydenbleasel))
- Initial commit from Create Next App ([@haydenbleasel](https://github.com/haydenbleasel))

#### Authors: 1

- Hayden Bleasel ([@haydenbleasel](https://github.com/haydenbleasel))
